<script setup lang="ts" name="reportprogressdetail">
import {onActivated, ref, onMounted, nextTick, computed} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {
	ledgerDepartmentDataStatisticsUrgeDepartment,
	ledgerDataAnalysisBatchDelete,
	ledgerDepartmentDataStatistics,
	ledgerDepartmentFilesDetail,
	ledgerDepartmentDataStatisticsDetailList,
	getRegionAll,
	getChildRegion,
	ledgergradeparentid,
} from '@/api/LedgerApi'
import {dayjs, ElMessage, ElMessageBox} from 'element-plus'
import {ReminderCycleList} from '@/define/ledger.define'
import {saveAs} from 'file-saver'
import {updateLabelTitle} from '@/hooks/useLabels'

import {DEPARTMENT_OPTIONS_ENUM} from '@/define/organization.define'
import {GetWorkflowTaskTable} from '@/api/workflowTaskApi'

const roles: any = ref([])
const rolesOptions: any = ref([])
const route = useRoute()
const router = useRouter()
const tableRef = ref()

// 表格中的操作列
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'view') {
		router.push({path: '/ledger/viewAnalysis', query: {id: row.id}})
	}
	if (btn.code === 'edit') {
		let data: any = [row.id]
		ElMessageBox.confirm(`是否催办 ${row.name} ?`, '消息确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				ledgerDataAnalysisBatchDelete(data).then(() => {
					ElMessage.success('删除成功！')
					getList()
				})
			})
			.catch(() => {})
	}
}
// 查询条件
const runwayForm: any = ref({
	filter: '',
	districtId: [],
	fillProgressStatus: '',
	grade: '',
	TaskItemId: null,
})
// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}
// 多选数据
const statisticsList: any = ref([])
const selectedCount = ref(0)
const selectionChange = (selection: any[]) => {
	selectedCount.value = selection.length
	statisticsList.value = selection
}
const regionData = ref([])
function arrayToTree(arr: any[]) {
	const newArr: any = []
	const map: any = {}
	arr.forEach((item) => {
		// if (!item.children)
		//   item.children = [] // 判断数据是否有子级   没有则进行子级的添加
		map[item.id] = item // 添加对应的映射关系
		item.key = item.id
		item.label = item.name
	})
	arr.forEach((item) => {
		if (map[item.parentId || item.companyId]) {
			if (!map[item.parentId || item.companyId].children)
				map[item.parentId || item.companyId].children = []
			map[item.parentId || item.companyId].children.push(item)
		} else {
			newArr.push(item)
		}
	})
	return newArr.length ? newArr : []
}

const getRegionList = async () => {
	ledgergradeparentid({grade: 2}).then((res) => {
		res.data.items = res.data.items.map((data: any) => ({...data, leaf: data.isLeaf}))
		regionData.value = arrayToTree(res.data.items)
		console.log(regionData.value)
	})
}

function onLoadData(treeNode: any, resolve: any) {
	if (treeNode.data.length === 0) return
	if (treeNode.data.length === 0 || !treeNode.data.id) return
	//   if (treeNode.data?.treeCode.split('.').length < 3) {
	//     resolve();
	//     return;
	//   }
	// 查询下级区域。
	ledgergradeparentid({grade: treeNode.data.grade + 1, parentId: treeNode.data.id}).then(
		(childList: any) => {
			if (childList) {
				treeNode.data.children = childList.data.items?.map((item: any) => ({
					...item,

					leaf: item.isLeaf,
					children: [],
				}))

				if (!treeNode.data.children) {
					treeNode.data.children = []
				}

				// const halfLength = Math.floor(treeNode.childNodes.length / 2)
				// console.log(treeNode?.childNodes)
				// treeNode.childNodes = treeNode.childNodes.slice(0, halfLength);
				// console.log(treeNode)
				// if (treeNode.isLeaf) return resolve([])
				resolve((treeNode.data.children = childList?.data?.items))

				// regionData.value = childList?.data?.items
			}
		}
	)
}

// 表中的内容
const tableData = ref([])
// 表头
const colData = ref([
	{
		prop: 'name',
		label: '应更新部门名称',
		tooltip: true,
		align: 'center',
	},
	{
		prop: 'taskName',
		label: '填报周期',
		align: 'center',
	},
	// {
	// 	prop: 'reminderInterval',
	// 	label: '更新周期',
	// 	align: 'center',
	// },
	{
		prop: 'reminderConfig',
		label: '截止时间',
		align: 'center',
		width: 200,
	},
	// {
	// 	prop: 'fillProgressStatus',
	// 	label: `更新周期`,
	// 	align: 'center',
	// },
	{
		prop: 'fillProgressStatus',
		label: `更新情况`,
		align: 'center',
	},
	{
		prop: 'totalUpdateCount',
		label: `总更新量`,
		align: 'center',
	},
	{
		prop: 'periodTotalUpdateCount',
		label: `按时更新数据量`,
		align: 'center',
	},
	{
		prop: 'periodAddCount',
		label: `新增量`,
		align: 'center',
	},
	{
		prop: 'periodDeleteCount',
		label: `删除量`,
		align: 'center',
	},
	{
		prop: 'periodEditCount',
		label: `编辑量`,
		align: 'center',
	},
	{
		prop: 'overduePeriodTotalUpdateCount',
		label: `超期更新数据量`,
		align: 'center',
	},
	{
		prop: 'overduePeriodAddCount',
		label: `新增量`,
		align: 'center',
	},
	{
		prop: 'overduePeriodDeleteCount',
		label: `删除量`,
		align: 'center',
	},
	{
		prop: 'overduePeriodEditCount',
		label: `编辑量`,
		align: 'center',
	},
])

const fillProgressStatusObj: any = {
	0: '未更新',
	1: '已更新',
	2: '已核实无需填报',
}

//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 查询
const getList = () => {
	console.log(runwayForm.value)
	ledgerDepartmentDataStatisticsDetailList({
		skipCount: (pagination.value.page - 1) * pagination.value.size,
		MaxResultCount: pagination.value.size,
		// ...runwayForm.value,
		filter: runwayForm.value.filter,
		districtId: String(runwayForm.value.districtId),
		grade: runwayForm.value.grade,
		// fillStatisticsType:route.query.fillStatisticsType,
		fillProgressStatus: runwayForm.value.fillProgressStatus,
		ledgerId: route.query.ledgerId,
		TaskItemId: runwayForm.value.TaskItemId,
	}).then((res: any) => {
		tableData.value = res.data.items
		pagination.value.total = res.data.totalCount
	})
}
const initData = ref<any>({})
const dataInfo: any = ref({})
const getDetil = () => {
	const tid = runwayForm.value.TaskItemId ? '/' + runwayForm.value.TaskItemId : ''
	ledgerDepartmentDataStatistics(`${route.query.ledgerId}${tid}`).then((res: any) => {
		console.log(res)
		initData.value = res.data
		dataInfo.value = {
			name: initData.value.ledger?.name,
			name1: initData.value.ledger?.ledgerType.name,
			lastOnlineTime: initData.value.ledger?.lastOnlineTime,
			taskItemName: initData.value.taskItemName,
			departmentCountByNeedUpdate: initData.value.departmentCountByNeedUpdate,
			departmentCountByUpdated: initData.value.departmentCountByUpdated,
			departmentCountByNotUpdated: initData.value.departmentCountByNotUpdated,
		}

		updateLabelTitle({
			path: router.currentRoute.value.fullPath,
			title: `填报进度-我的业务表-${dataInfo.value.name}`,
		})
	})
}

// 通知类型选择弹窗相关变量
const notifyTypeDialogVisible = ref(false)
const notifyTypes = ref<number[]>([])
const notifyTypeOptions = ref([
	{
		value: 3,
		label: '发送系统内通知',
	},
	{
		value: 1,
		label: '发送渝快政通知',
	},
	{
		value: 2,
		label: '发送渝快政Ding通知',
	},
])

// 批量催办 - 打开选择通知类型弹窗
const ledgerArrDelete = () => {
	if (statisticsList.value.length === 0) {
		ElMessage.warning('请先选择需要催办的数据')
		return
	}
	// 重置选择
	notifyTypes.value = []
	// 打开弹窗
	notifyTypeDialogVisible.value = true
}

// 确认催办
const confirmUrge = () => {
	if (notifyTypes.value.length === 0) {
		ElMessage.warning('请至少选择一种通知类型')
		return
	}

	let data: any = []
	statisticsList.value.forEach((item: any, index: any) => {
		data.push(item.departmentId)
	})

	ledgerDepartmentDataStatisticsUrgeDepartment({
		ledgerId: route.query.ledgerId,
		departmentIds: data,
		ids: statisticsList.value.map((v: any) => v.id),
		notifyLedgerTypes: notifyTypes.value,
	}).then(() => {
		ElMessage.success('催办成功！')
		selectedCount.value = 0
		tableRef.value?.clearSelection()
		statisticsList.value = []
		notifyTypeDialogVisible.value = false
		getList()
	})
	// ElMessageBox.confirm('是否要批量催办?', '消息确认', {
	// 	confirmButtonText: '确定',
	// 	cancelButtonText: '取消',
	// 	type: 'warning',
	// })
	// 	.then(() => {

	// 	})
	// 	.catch(() => {})
}
// 高级查询
const seniorList = () => {
	// pagination.value.page = 1
	// pagination.value.size = 10
	getList()
	getDetil()
}
// 清空

const empty = () => {
	runwayForm.value.filter = ''
	runwayForm.value.districtId = ''
	runwayForm.value.fillProgressStatus = null
	runwayForm.value.grade = null
	runwayForm.value.TaskItemId = null
	getList()
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.skipCount = 0
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}
onActivated(() => {
	getList()
	getDetil()
})

const buildTree = (items: any[]) => {
	const map = {} // 用于快速查找父节点的映射
	const tree: any[] = [] // 最终的树形结构

	// 遍历每个节点，并创建带有label属性的新对象
	items.forEach((item: {name: any; id: string | number}) => {
		const newNode = {...item, children: [], label: item.name} // 添加label属性
		map[item.id] = newNode // 将新节点放入映射中
	})
	// 遍历每个节点，如果它有父节点，则将其添加到父节点的children数组中
	items.forEach((item: {parentId: any; id: string | number}) => {
		const parentId = item.parentId
		if (parentId === null) {
			// 没有父节点，说明是根节点，直接加入树中
			tree.push(map[item.id])
		} else {
			// 添加到父节点的children数组中
			if (map[parentId]) {
				map[parentId].children.push(map[item.id])
			}
		}
	})

	return tree
}
const wfList = ref<any[]>([])
const getWfList = async () => {
	const res = await GetWorkflowTaskTable({
		maxResultCount: 999,
		skipCount: 0,
		bindObjectId: route.query.ledgerId,
	})
	if (res.data.items.length !== 0) {
		runwayForm.value.TaskItemId = res.data.items[0].id
		wfList.value = res.data.items.map((v) => ({
			label: v.name,
			value: v.id,
		}))
		getList()
		getDetil()
	}
}
onMounted(() => {
	// getRegionAll({grade: 3}).then((res) => {
	// 	regionData.value = res.data.items
	// 	const arr:any = buildTree(res.data.items)

	// 	regionData.value = [{...arr[0]}]
	// 	console.log(regionData.value)
	// })
	getRegionList()
	getWfList()
})
const getReadCycle = computed(() => {
	return (interval: number) => {
		switch (interval) {
			case 0:
				return '不提醒'
			case 2:
				return '每周'
			case 3:
				return '每月'
			case 4:
				return '每季度'
			case 5:
				return '每年'
			case 6:
				return '每半月'
			case 7:
				return '每半年'
			default:
				return '-'
		}
	}
})
function isFirstHalfOfMonth() {
	const currentDate = new Date()
	const dayOfMonth = currentDate.getDate()
	return dayOfMonth <= 15
}
function isFirstHalfOfYear() {
	const currentDate = new Date()
	const monthOfYear = currentDate.getMonth()
	return monthOfYear < 6
}
const getReadTime = computed(() => {
	const date = new Date()
	const weekDays = ['一', '二', '三', '四', '五', '六', '日']
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const day = date.getDate()
	const week = date.getDay() == 0 ? 7 : date.getDay()
	const qtr = month <= 3 ? 1 : month <= 6 ? 2 : month <= 9 ? 3 : 4
	return (time: any) => {
		if (time && time.interval == 2) {
			if (time.weeklyDeadlineDayOfWeek !== null) {
				return `每周${weekDays[time.weeklyDeadlineDayOfWeek - 1]}前`
			} else {
				return '未设置'
			}
		} else if (time && time.interval == 3) {
			if (time.monthlyDeadlineDay) {
				return `每月${time.monthlyDeadlineDay}日前`
			} else {
				return '未设置'
			}
		} else if (time && time.interval == 4) {
			if (time['quarterlyDeadlineDay' + qtr] !== null) {
				const text = new Date(
					time['quarterlyDeadlineDay' + qtr].substring(4).replace(/./, year + '-')
				)
				return `${text.getMonth() + 1}月${text.getDate()}日前`
			} else {
				return '未设置'
			}
		} else if (time && time.interval == 5) {
			if (time.yearlyDeadlineDay !== null) {
				const text = new Date(time.yearlyDeadlineDay.substring(4).replace(/./, year + '-'))
				return `${text.getMonth() + 1}月${text.getDate()}日前`
			} else {
				return '未设置'
			}
		} else if (time && time.interval == 6) {
			if (isFirstHalfOfMonth()) {
				// 上半个月
				if (time && time.downHalfMonthReminderTime) {
					return `每月${time.downHalfMonthReminderTime}日前`
				} else {
					return '未设置'
				}
			} else {
				if (time && time.downHalfMonthDateOnlyTime) {
					return `每月${time.downHalfMonthDateOnlyTime}日前`
				} else {
					return '未设置'
				}
			}
		} else if (time && time.interval == 7) {
			if (isFirstHalfOfYear()) {
				if (time.upHalfYearDateOnlyTime !== null) {
					const text = new Date(
						time.upHalfYearDateOnlyTime.substring(4).replace(/./, year + '-')
					)
					return `${text.getMonth() + 1}月${text.getDate()}日前`
				} else {
					return '未设置'
				}
			} else {
				if (time.downHalfYearDateOnlyTime !== null) {
					const text = new Date(
						time.downHalfYearDateOnlyTime.substring(4).replace(/./, year + '-')
					)
					return `${text.getMonth() + 1}月${text.getDate()}日前`
				} else {
					return '未设置'
				}
			}
		}
	}
})

const onChange = (node: any) => {
	console.log(node)
}
const onImport = () => {
	ledgerDepartmentFilesDetail({
		ledgerIds: [route.query.ledgerId],
		fillStatisticsType: route.query.fillStatisticsType,
	}).then((res: any) => {
		const blob = res.data
		const fileName = `当前周期详细更新数据.zip`

		saveAs(
			new Blob([blob], {
				type: 'apilication/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			}),
			`${fileName}`
		)

		ElMessage.success('下载成功')
	})
}
const searchOptions1 = [
	{
		value: 0,
		label: '未更新',
	},
	{
		value: 1,
		label: '已更新',
	},
	{
		value: 2,
		label: '已核实无更新',
	},
]

const searchOptions2 = [
	{
		value: 2,
		label: '市级',
	},
	{
		value: 3,
		label: '区级',
	},
	{
		value: 4,
		label: '街镇',
	},
	{
		value: 5,
		label: '村社',
	},
]

// 详情组件title参数 可变化
const titleList = ref([
	{
		width: '25%',
		title: '业务表名称',
		key: 'name',
	},
	{
		width: '25%',
		title: '发布部门',
		key: 'name1',
	},
	{
		width: '25%',
		title: '上线时间',
		key: 'lastOnlineTime',
	},
	{
		width: '25%',
		title: '填报进度统计周期',
		key: 'taskItemName',
	},
	{
		width: '25%',
		title: '应更新部门（个）',
		key: 'departmentCountByNeedUpdate',
	},
	{
		width: '25%',
		title: '已更新部门（个）',
		key: 'departmentCountByUpdated',
	},
	{
		width: '25%',
		title: '未更新部门（个）',
		key: 'departmentCountByNotUpdated',
	},
])
</script>
<template>
	<div class="dataAnalysis">
		<Block
			:enable-fixed-height="true"
			:enable-expand-content="true"
			:enable-back-button="false"
			:enable-close-button="true"
			:title="'填报进度'"
			@heightChanged="onBlockHeightChanged"
		>
			<template #title>
				<div class="titleHeader">
					<p class="titleHeader-text"><span></span>填报进度</p>
					<!-- <small>
						<el-icon><InfoFilled /></el-icon>
						实时更新
					</small> -->
				</div>
			</template>
			<template #topRight>
				<el-button type="primary" size="small" @click="onImport" style="color: #fff">
					导出数据
				</el-button>
				<el-button
					type="danger"
					size="small"
					:disabled="selectedCount <= 0"
					@click="ledgerArrDelete"
					style="margin-left: 10px"
				>
					批量催办
				</el-button>
			</template>

			<template #expand>
				<div class="search-box" v-action:enter="seniorList">
					<el-input
						class="search-input"
						v-model="runwayForm.filter"
						placeholder="请输入部门名称"
						size="default"
						clearable
					></el-input>
					<el-select
						v-model="runwayForm.fillProgressStatus"
						clearable
						@change="seniorList"
						placeholder="请选择更新情况"
						size="default"
					>
						<el-option
							v-for="item in searchOptions1"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-select
						v-model="runwayForm.grade"
						clearable
						@change="seniorList"
						placeholder="请选择层级"
						size="default"
					>
						<el-option
							v-for="item in searchOptions2"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-tree-select
						v-model="runwayForm.districtId"
						:data="regionData"
						placeholder="请选择区域"
						:multiple="true"
						@change="seniorList"
						:load="onLoadData"
						lazy
						remote
						collapse-tags
						:props="{
							label: 'name',
							value: 'id',
							isLeaf: 'isLeaf',
						}"
						collapse-tags-tooltip
						:check-strictly="true"
						:max-collapse-tags="1"
						:render-after-expand="true"
						style="width: 340px"
					>
					</el-tree-select>

					<el-select
						v-model="runwayForm.TaskItemId"
						clearable
						@change="seniorList"
						placeholder="请选择填报周期"
						size="default"
					>
						<el-option
							v-for="item in wfList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-button size="default" type="default" @click="empty">
						<i class="i-ic-baseline-cleaning-services" mr-3px></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="seniorList">
						<i class="i-ic-baseline-send" mr-3px></i>
						查询</el-button
					>
				</div>
			</template>

			<InfoOverViewComp :data="dataInfo" :titles="titleList" />
			<TableV2
				ref="tableRef"
				:auto-height="true"
				:columns="colData"
				:defaultTableData="tableData"
				:headers="{Urlkey: 'ledger'}"
				:enableToolbar="false"
				:enable-create="false"
				:enable-edit="false"
				:enable-delete="false"
				:enableSelection="true"
				:enableIndex="false"
				:req-params="reqParams"
				:buttons="[]"
				@clickButton="onTableClickButton"
				@selection-change="selectionChange"
				class="mg-top-5"
			>
				<template #name="{row}">
					{{ row.belongRegionName }}-{{ row.department?.name }}
				</template>
				<template #fillProgressStatus="{row}">
					{{ fillProgressStatusObj[row.fillProgressStatus] }}
				</template>
				<template #reminderInterval="{row}">
					{{ getReadCycle(row.reminderConfig?.interval) }}
				</template>
				<template #reminderConfig="{row}">
					<!-- {{ getReadTime(row.reminderConfig) ?? '-' }} -->
					{{ row?.deadline }}
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			>
			</Pagination>
		</Block>

		<!-- 通知类型选择弹窗 -->
		<el-dialog
			v-model="notifyTypeDialogVisible"
			title="选择通知类型"
			width="30%"
			:close-on-click-modal="false"
		>
			<el-checkbox-group v-model="notifyTypes">
				<el-checkbox
					v-for="item in notifyTypeOptions"
					:key="item.value"
					:label="item.value"
				>
					{{ item.label }}
				</el-checkbox>
			</el-checkbox-group>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="notifyTypeDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="confirmUrge">确认</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style scoped lang="scss">
.search-box {
	align-items: center;
	display: flex;
	height: 62px;
	overflow: hidden;
	padding: 15px;
	transition: all 0.15s linear;
	white-space: nowrap;
	width: 100%;

	> div {
		margin-right: 15px;
		width: 25%;
	}
}
.more-operations {
	// width: 200px;
	// flex-wrap: wrap;
	display: flex;
	padding: 5px;
	span {
		color: #fff !important;
	}
	:deep(button) {
		&:nth-child(5n) {
			margin-right: 0;
		}

		span {
			color: #fff !important;
		}
	}
}
.titleHeader {
	display: flex;
	align-items: center;
	p {
		margin-right: 10px;
		font-size: 16px;
	}
}
.info-chunk {
	display: flex;
	// justify-content: space-between;
	flex-wrap: wrap;
	margin-bottom: 15px;
	.info-main {
		display: flex;
		min-width: 400px;
		margin-bottom: 14px;
		font-size: 14px;
		.info-title {
			font-weight: 550;
			text-align: right;
			min-width: 130px;
		}
	}
}
</style>
