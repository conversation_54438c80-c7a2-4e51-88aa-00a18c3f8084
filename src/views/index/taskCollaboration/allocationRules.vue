<script setup lang="ts" name="allocationrules">
import {reactive, ref} from 'vue'
import {
	PostMissionObjectivesRoles,
	PutMissionObjectivesRoles,
	DeleteMissionObjectivesRoles,
} from '@/api/TaskCollaborationApi'
import {ElMessage} from 'element-plus'

const formProps = ref([{label: '规则名称', prop: 'name', type: 'text'}])
const formSearch = ref({name: null})
const dialogForm = ref({})
const show = ref(false)
const isNew = ref(true)
const currentRow = ref({id: ''})

const columns = [
	{label: '规则名称', prop: 'name'},
	{label: '规则描述', prop: 'description'},
	{label: '更新时间', prop: 'lastModificationTime'},
]

const tableRef = ref()
const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams = reactive({
	name: null,
	skipCount: 0,
	maxResultCount: 10,
})
const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.maxResultCount = pagination.size
	reqParams.name = formSearch.value.name
}

const onTableButtonClick = ({btn, row}: any) => {
	currentRow.value = row
	if (btn.code == 'edit') {
		show.value = true
		isNew.value = false
		dialogForm.value = row
	} else if (btn.code == 'delete') {
		DeleteMissionObjectivesRoles(row.id).then(() => {
			ElMessage.success('删除成功')
			tableRef.value.reload()
		})
	}
}

const onClickAdd = () => {
	isNew.value = true
	dialogForm.value = {}
	show.value = true
}

const onSubmit = () => {
	if (isNew.value) {
		PostMissionObjectivesRoles(dialogForm.value).then(() => {
			ElMessage.success('新增成功')
			show.value = false
			tableRef.value.reload()
		})
	} else {
		PutMissionObjectivesRoles(currentRow.value.id, dialogForm.value).then(() => {
			ElMessage.success('修改成功')
			show.value = false
			tableRef.value.reload()
		})
	}
}

const onBatchDelete = () => {
	const rows = tableRef.value.getSelectionRows()
	if (rows.length == 0) {
		ElMessage.error('请选择要删除的行')
		return
	}
	const promise: Promise<any>[] = []
	rows.forEach((row: any) => {
		promise.push(DeleteMissionObjectivesRoles(row.id))
	})
	Promise.all(promise).then(() => {
		ElMessage.success('删除成功')
		tableRef.value.reload()
		tableRef.value.clearSelection()
	})
}
</script>
<template>
	<div class="allocationrules">
		<Block title="分配规则" :enable-fixed-height="true" @heightChanged="onBlockHeightChanged">
			<template #topRight>
				<el-button size="small" type="danger" @click="onBatchDelete">批量删除</el-button>
				<el-button size="small" type="primary" @click="onClickAdd">新增规则</el-button>
			</template>
			<template #expand>
				<div class="search">
					<Form
						:props="formProps"
						v-model="formSearch"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					></Form>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/newFeature/mission-objectives-roles"
				:columns="columns"
				:req-params="reqParams"
				:enable-toolbar="false"
				:height="tableHeight"
				:enable-own-button="false"
				:enable-selection="true"
				:buttons="[
					{code: 'edit', label: '编辑', type: 'primary'},
					{code: 'delete', label: '删除', type: 'danger', popconfirm: '确认删除吗？'},
				]"
				@click-button="onTableButtonClick"
				@completed="
					() => {
						pagination.total = tableRef.value.getTotal()
					}
				"
			></TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>

		<Dialog v-model="show" :title="isNew ? '新增规则' : '编辑规则'" @click-confirm="onSubmit">
			<Form
				v-model="dialogForm"
				:props="[
					{label: '规则名称', prop: 'name', type: 'text'},
					{label: '规则描述', prop: 'description', type: 'textarea'},
				]"
				:enable-button="false"
			></Form>
		</Dialog>
	</div>
</template>
<route>
    {
		meta: {
			title: '分配规则',
		},
	}
</route>
<style scoped lang="scss"></style>
