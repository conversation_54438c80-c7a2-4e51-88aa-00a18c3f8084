<script setup lang="ts" name="taskdistribute">
import {reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ReportsFlowStatus} from '@/define/statement.define'
import {FlowExecutionCycle} from '@/define/workFlowTask.define'
import {UpdateMissionObjectives} from '@/api/TaskCollaborationApi'
import {ElMessage} from 'element-plus'

const router = useRouter()
const loading = ref(false)
const status = ReportsFlowStatus.filter((f: any) => f.type == 'plan')

const formProps = ref([
	{label: '任务名称', prop: 'taskName', type: 'text'},
	{
		label: '任务类型',
		prop: 'taskType',
		type: 'select',
		options: [
			{label: '报表', value: '报表'},
			{label: '业务表', value: '业务表'},
		],
	},
])
const form: any = ref({taskName: ''})

const columns = [
	{label: '任务名称', prop: 'taskName'},
	{label: '任务类型', prop: 'taskType'},
	{label: '创建部门-科室-人', prop: 'creator'},
	{label: '开始时间', prop: 'startTime'},
	{label: '执行周期', prop: 'executePeriod'},
	{label: '执行流程', prop: 'executeFlow'},
	{label: '确认状态', prop: 'confirmStatus'},
]

const tableRef = ref()
const tableData = ref([])
const tableHeight = ref(0)
const curRow: any = ref(null)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams: any = reactive({
	skipCount: 0,
	maxResultCount: 10,
})

const show = ref(false)
const showResult = ref(false)
const showDistribute = ref(false)
const showIntelligent = ref(false)
const objectionValue = ref('')
const intelligentValue = ref('')
const distributeValue = ref('')

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onSubmit = () => {
	reqParams.skipCount = 0
	reqParams.maxResultCount = pagination.size
	reqParams.taskName = form.value.taskName
	reqParams.taskType = form.value.taskType
}

const onTableButtonClick = ({btn, row, index}: any) => {
	curRow.value = row
	if (btn.code === 'confirm') {
		UpdateMissionObjectives(row.id, {
			isConfirm: true,
		}).then((res: any) => {
			ElMessage.success('确认成功')
		})
	} else if (btn.code === 'dispute') {
		show.value = true
	} else if (btn.code === 'evaluate') {
		showResult.value = true
	} else if (btn.code === 'distribute') {
		showDistribute.value = true
	}
}

const onClickConfirm = () => {
	UpdateMissionObjectives(curRow.value.id, {
		objectionContent: objectionValue.value,
	}).then((res: any) => {
		ElMessage.success('异议成功')
		show.value = false
		curRow.value = null
	})
}

const onClickDistributionRule = () => {
	router.push({
		path: '/taskCollaboration/allocationrules',
	})
}
</script>
<template>
	<div class="taskdistribute">
		<Block title="任务分配" :enable-fixed-height="true" @heightChanged="onBlockHeightChanged">
			<template #topRight>
				<el-button
					size="small"
					type="primary"
					@click=";(showIntelligent = true), (intelligentValue = '')"
				>
					智能推荐
				</el-button>
				<el-button size="small" type="primary" @click="onClickDistributionRule"
					>分配规则管理</el-button
				>
			</template>
			<template #expand>
				<div class="search">
					<Form
						:props="formProps"
						v-model="form"
						:column-count="3"
						:label-width="74"
						:enable-reset="false"
						:loading="loading"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSubmit"
					></Form>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/newFeature/mission-objectives"
				v-model="tableData"
				:columns="columns"
				:req-params="reqParams"
				:enable-toolbar="false"
				:height="tableHeight"
				:enable-own-button="false"
				:buttons="[
					{code: 'confirm', label: '确认', type: 'primary', disabled: 'row.isConfirm	'},
					{code: 'dispute', label: '异议', type: 'primary', disabled: 'row.isConfirm'},
					{code: 'evaluate', label: '评估', type: 'primary', disabled: '!row.isConfirm'},
					{
						code: 'distribute',
						label: '分配',
						type: 'primary',
						disabled: '!row.isConfirm',
					},
				]"
				@loading="($event: boolean) => loading = $event"
				@click-button="onTableButtonClick"
				@completed="
					() => {
						pagination.total = tableRef.getTotal()
					}
				"
			>
				<template #taskName="scope">
					{{ scope.row.planTask.name }}
				</template>
				<template #taskType="scope">
					{{ scope.row.planTask.taskType }}
				</template>
				<template #creator="scope">
					{{ scope.row.planTask.creatorDepartmentName }}
					-
					{{ scope.row.planTask.creatorUserName }}
				</template>
				<template #startTime="scope">
					{{ scope.row.planTask.executionStartTime }}
				</template>
				<template #executePeriod="scope">
					{{
						FlowExecutionCycle.filter(
							(v: any) =>
								v.value === scope.row.planTask.executionIntervalConfig?.interval
						)[0]?.label
					}}
				</template>
				<template #confirmStatus="scope">
					{{ scope.row.isConfirm ? '已确认' : '待确认' }}
				</template>
				<template #executeFlow="scope">
					{{ scope.row.schemeName || '-' }}
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>

		<Dialog v-model="show" title="异议" @click-confirm="onClickConfirm">
			<FormItem
				v-model="objectionValue"
				:items="[{prop: 'reason', label: '异议内容', type: 'textarea'}]"
			></FormItem>
		</Dialog>

		<Dialog v-model="showResult" title="评估结果" :enable-confirm="false">
			经评估，该任务执行状态正常，请及时关注系统消息，按期进行任务数据填报工作。
		</Dialog>

		<Dialog
			v-model="showIntelligent"
			title="智能推荐"
			@open="() => (intelligentValue = '')"
			@click-confirm="
				() => {
					ElMessage.success('智能推荐成功')
					showIntelligent = false
				}
			"
		>
			<FormItem
				v-model="intelligentValue"
				:items="[
					{
						prop: 'reason',
						label: '智能推荐',
						type: 'select',
						options: [
							{label: '按人员偏好推荐', value: 1},
							{label: '按任务相似性', value: 2},
							{label: '按紧急程度', value: 3},
							{label: '按业务拓展', value: 4},
						],
					},
				]"
			></FormItem>
		</Dialog>

		<Dialog
			v-model="showDistribute"
			title="分配"
			@open="() => (distributeValue = '')"
			@click-confirm="
				() => {
					ElMessage.success('分配成功')
					showDistribute = false
				}
			"
		>
			<FormItem
				v-model="distributeValue"
				:items="[
					{
						prop: 'type',
						label: '分配类型',
						type: 'select',
						options: [
							{label: '按职责分配', value: 1},
							{label: '按能力分配', value: 2},
							{label: '按岗位分配', value: 3},
							{label: '按技能分配', value: 4},
							{label: '按优先级分配', value: 5},
							{label: '按紧急程度分配', value: 6},
							{label: '按业务熟悉度分配', value: 7},
						],
					},
				]"
			></FormItem>
		</Dialog>
	</div>
</template>
<route>
    {
		meta: {
			title: '任务分配',
		},
	}
</route>
<style scoped lang="scss"></style>
