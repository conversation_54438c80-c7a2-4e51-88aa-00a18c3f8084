<script setup lang="ts" name="taskflow">
import {nextTick, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {FlowNodeTypes} from '@/define/Workflow'
import SubTaskConfig from './components/SubTaskConfig.vue'
import {GetFlowNodes} from '@/api/TaskCollaborationApi'
import {updateLabelTitle} from '@/hooks/useLabels'

const route = useRoute()
const router = useRouter()
const flowRef = ref()
const flowConfig = ref({})
const showSubTaskConfig = ref(false)
const completed = ref(false)

const infoData: any = ref(null)
const currentNode: any = ref(null)

const onFlowNodeClick = (flowNodeInfo: any) => {
	flowRef.value.getNodeBeforeById(flowNodeInfo.node.id).then((node: any) => {
		nextTick(() => {
			if (node.type === FlowNodeTypes.Sub) {
				showSubTaskConfig.value = true
				currentNode.value = node
				currentNode.value.data = Object.assign(node.data, {
					missionObjectivesId: infoData.value.id,
					parentId: infoData.value.id,
				})
			}
		})
	})
}

const getFlowNodesById = () => {
	completed.value = false
	GetFlowNodes(route.query.id as string).then((res: any) => {
		const {data} = res
		const config: any = {
			id: 'flowinput',
			label: '流程开始',
			type: 'input',
			childNode: [
				{
					id: data.id,
					label: '主任务',
					type: 'main',
					data: {label: ' '},
					childNode: [],
				},
			],
		}

		const loops = (arr: any[], node: any) => {
			arr.forEach((item: any) => {
				node.childNode.push({
					id: item.id,
					label: '子任务',
					type: 'sub',
					childNode: [],
					data: {
						parentId: data.id,
						missionObjectivesId: data.id,
						raw: JSON.parse(JSON.stringify(item)),
					},
				})
				if (item.children.length > 0) {
					loops(item.children, node)
				}
			})
		}

		loops(data.children, config.childNode[0])

		infoData.value = data
		completed.value = true
		flowConfig.value = config
	})
}

watch(
	() => showSubTaskConfig.value,
	(value) => {
		if (!value) {
			getFlowNodesById()
		}
	}
)

onMounted(() => {
	getFlowNodesById()
	updateLabelTitle({
		path: router.currentRoute.value.fullPath,
		title: `任务目标拆解-${route.query.name}`,
	})
})
</script>
<template>
	<div class="task-flow">
		<Block
			title="报表任务目标拆解"
			:enable-fixed-height="true"
			:enable-expand-content="false"
			:enable-close-button="false"
			:enable-back-button="true"
		>
			<Flow
				v-if="completed"
				ref="flowRef"
				v-model="flowConfig"
				showButton="sub"
				:subtasksParallel="true"
				@node-click="onFlowNodeClick"
			></Flow>

			<SubTaskConfig v-model="showSubTaskConfig" :node="currentNode"></SubTaskConfig>
		</Block>
	</div>
</template>
<route>
    {
        meta: {
            title: '报表任务目标拆解-任务目标拆解',
        },
    }
</route>
<style scoped lang="scss"></style>
