<script setup lang="ts" name="reportprogresslist">
import {onActivated, ref, onMounted, nextTick, computed, onDeactivated} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {
	ledgerDepartmentDataStatisticsUrgeUser,
	ledgerDataAnalysisBatchDelete,
	ledgerDepartmentFiles,
	ledgerDepartmentDataStatisticsList,
	ledgerDepartmentFilesDetail,
} from '@/api/LedgerApi'
import {dayjs, ElMessage, ElMessageBox} from 'element-plus'
import {ReminderCycleList} from '@/define/ledger.define'
import {saveAs} from 'file-saver'
import {DEPARTMENT_OPTIONS_ENUM} from '@/define/organization.define'
import {USER_ROLES_ENUM} from '@/define/organization.define'
import {updateLabelTitle} from '@/hooks/useLabels'

const roles: any = ref([])
const rolesOptions: any = ref([])
const route = useRoute()
const router = useRouter()

// 表格中的操作列
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'view') {
		router.push({
			path: '/taskCollaboration/reportProgressDetail',
			query: {
				ledgerId: row.ledgerId,
				fillStatisticsType: runwayForm.value.fillStatisticsType,
				name: row.ledger.name,
			},
		})
	}
	if (btn.code === 'edit') {
		let data: any = [row.id]
		ElMessageBox.confirm(`是否催办 ${row.name} ?`, '消息确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				ledgerDataAnalysisBatchDelete(data).then(() => {
					ElMessage.success('删除成功！')
					getList()
				})
			})
			.catch(() => {})
	}
}
// 查询条件
const runwayForm: any = ref({
	ReminderInterval: null,
	organize: '',
	fillStatisticsType: 0,
	filter: '',
	DepartmentName: '',
	departmentId: '',
	LedgerRunwayId: undefined,
	ledgerFillStatus: null,
	departmentLevels: null,
})
// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}
// 多选数据
const statisticsList: any = ref([])
const selectedCount = ref(0)
const selectionChange = (selection: any[]) => {
	selectedCount.value = selection.length
	statisticsList.value = selection
}
const currentUser = ref(JSON.parse(localStorage.getItem('currentUserInfo') as string))
const role = ref(
	currentUser.value.staffRole.some((item) => item === USER_ROLES_ENUM.LEDGER_PERATOR)
)
const superAdmin = ref(currentUser.value.baseRoles.some((item) => item === 'ledger-super-admin'))

// 表中的内容
const tableData = ref([])
// 表头
const colData = ref([
	{
		prop: 'name',
		label: '业务表名称',
		tooltip: true,
		align: 'center',
	},
	{
		prop: 'reminderInterval',
		label: '更新周期',
		align: 'center',
	},
	{
		prop: 'deadline',
		label: '截止时间',
		align: 'center',
		width: 200,
	},
	{
		prop: 'departmentCountByNeedUpdate',
		label: `应更新部门(个)\n(有业务表增删改权限)`,
		align: 'center',
		width: 230,
	},
	{
		prop: 'departmentCountByUpdated',
		label: `完成更新(个)`,
		align: 'center',
	},
	{
		prop: 'departmentCountByNotUpdated',
		label: `未完成更新(个)`,
		align: 'center',
	},
	{
		prop: 'statisticsUpdateTime',
		label: `最近更新时间`,
		align: 'center',
	},
	{
		prop: 'creatorDepartmentName',
		label: `发布部门`,
		align: 'center',
	},
	{
		prop: 'lastOnlineTime',
		label: `上线时间`,
		align: 'center',
	},
])

//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
const tableRef = ref()
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
const isGrade = ref<any>(null)
const isParentId = ref<any>(null)
// 查询
const getList = () => {
	loading.value = true
	console.log(isGrade.value)
	let grade = isGrade.value ? isGrade.value : null
	let parentId = isParentId.value ? isParentId.value : null
	console.log(runwayForm.value)

	ledgerDepartmentDataStatisticsList({
		skipCount: (pagination.value.page - 1) * pagination.value.size,
		MaxResultCount: pagination.value.size,
		...runwayForm.value,
		departmentId: runwayForm.value.departmentId,
		grade,
		parentId,
		ledgerFillStatus: runwayForm.value.ledgerFillStatus,
		departmentLevels: runwayForm.value.departmentLevels,
	}).then((res: any) => {
		tableData.value = res.data.items
		pagination.value.total = res.data.totalCount
		loading.value = false
		// isGrade.value = null
		// isParentId.value = null
	})
}
// 通知类型选择弹窗相关变量
const notifyTypeDialogVisible = ref(false)
const notifyTypes = ref<number[]>([])
const notifyTypeOptions = ref([
	{
		value: 3,
		label: '发送系统内通知',
	},
	{
		value: 1,
		label: '发送渝快政通知',
	},
	{
		value: 2,
		label: '发送渝快政Ding通知',
	},
])

// 批量催办 - 打开选择通知类型弹窗
const ledgerArrDelete = () => {
	if (statisticsList.value.length === 0) {
		ElMessage.warning('请先选择需要催办的数据')
		return
	}
	// 重置选择
	notifyTypes.value = []
	// 打开弹窗
	notifyTypeDialogVisible.value = true
}

// 确认催办
const confirmUrge = () => {
	if (notifyTypes.value.length === 0) {
		ElMessage.warning('请至少选择一种通知类型')
		return
	}

	let data: any = []
	statisticsList.value.forEach((item: any, index: any) => {
		data.push(item?.ledgerId)
	})

	ledgerDepartmentDataStatisticsUrgeUser({
		ledgerIds: data,
		notifyLedgerTypes: notifyTypes.value,
	}).then(() => {
		ElMessage.success('催办成功！')
		selectedCount.value = 0
		tableRef.value?.clearSelection()
		statisticsList.value = []
		notifyTypeDialogVisible.value = false
		getList()
	})
	// ElMessageBox.confirm('是否要批量催办?', '消息确认', {
	// 	confirmButtonText: '确定',
	// 	cancelButtonText: '取消',
	// 	type: 'warning',
	// })
	// 	.then(() => {

	// 	})
	// 	.catch(() => {})
}
// 高级查询
const seniorList = () => {
	// pagination.value.page = 1
	// pagination.value.size = 10
	getList()
}
// 清空

const empty = () => {
	runwayForm.value.ReminderInterval = null
	runwayForm.value.filter = ''
	runwayForm.value.organize = ''
	runwayForm.value.DepartmentName = ''
	runwayForm.value.departmentId = ''
	runwayForm.value.LedgerRunwayId = undefined
	runwayForm.value.LitStatus = undefined
	runwayForm.value.ledgerFillStatus = null
	runwayForm.value.departmentLevels = null
	cascadeValue.value = []
	currentGrade.value = null
	isGrade.value = null
	isParentId.value = null
	getList()
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.skipCount = 0
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}

onActivated(() => {
	updateLabelTitle({
		path: router.currentRoute.value.fullPath,
		title: `填报进度-我的业务表`,
	})
	getList()
})

const buttons = ref([{code: 'view', label: '查看详情', type: 'primary', verify: 'true'}])

const oneSelectProps = ref({
	lazy: true,
	url: '/api/platform/department/generalDepartmentQuery',
	method: 'GET',
	multiple: false,
	checkStrictly: true,
	beforeCompleted: (node: any, data: any, query: any) => {
		const first = DEPARTMENT_OPTIONS_ENUM.map((f: any) => f.label)
		let grade = 2
		let parentId = ''
		if (first.includes(node.label)) {
			grade = node.value
			isGrade.value = node.value
			console.log(isGrade.value)
			isParentId.value = null
		} else {
			grade = node.pathNodes[0].value
			parentId = node.value
			isGrade.value = node.pathNodes[0].value
			console.log(isGrade.value)

			isParentId.value = node.value
		}
		query = {
			keyWord: '',
			grade,
			parentId,
		}
		return {data, query}
	},
})
const cascadeValue: any = ref([])
const currentGrade: any = ref(null)
const treeCodeLength: any = ref()
const searchOptions = ReminderCycleList.filter((item: any) => item.label !== '不提醒')
const completedList = ref([
	{label: '已完成', value: 1},
	{label: '未完成', value: 0},
])
const LitStatusList = ref([
	{label: '正常预警', value: 1},
	{label: '黄色预警', value: 2},
	{label: '红色预警', value: 3},
])
const fillingGradeList = ref([
	{label: '市级', value: 2},
	{label: '区县', value: 3},
	{label: '街镇', value: 4},
	{label: '村社', value: 5},
])
const currentUserGrade = JSON.parse(localStorage.getItem('currentDepartmentInfo') as string)
treeCodeLength.value = currentUserGrade.region.treeCode.split('.').length
const onCascadeElChange = (val: any, nodes: any) => {
	// const first = DEPARTMENT_OPTIONS_ENUM.map((f: any) => f.label)
	// if (first.includes(nodes.label)) {
	// 		isGrade.value =  nodes.value
	// 		console.log(isGrade.value)
	// 	} else {
	// 		isGrade.value = nodes.pathNodes[0].value
	// 		isParentId.value = nodes.value
	// 	}
	// currentGrade.value = val[0]?val[0]:null
	// isGrade.value = val[0]

	if (nodes[0]?.label === '市级部门') {
		isGrade.value = 2
	}
	if (val && val.length === 1) {
		runwayForm.value.departmentId = ''
	} else {
		isGrade.value = val ? val[0] : null
		runwayForm.value.departmentId = val ? val[val.length - 1] : ''
	}
	if (val && val.length === 3) {
		isParentId.value = val[val.length - 2]
		runwayForm.value.parentId = val[val.length - 2]
	}
}

const activeName = ref('我的业务表进度')
const onClickTabs = (val: any) => {
	selectedCount.value = 0
	if (val == '我的业务表进度') {
		runwayForm.value.fillStatisticsType = 0
	} else if (val == '全市业务表进度') {
		runwayForm.value.fillStatisticsType = 1
	} else {
		runwayForm.value.fillStatisticsType = 2
	}
	getList()
}
const getReadCycle = computed(() => {
	return (interval: number) => {
		switch (interval) {
			case 0:
				return '不提醒'
			case 2:
				return '每周'
			case 3:
				return '每月'
			case 4:
				return '每季度'
			case 5:
				return '每年'
			case 6:
				return '每半月'
			case 7:
				return '每半年'
			default:
				return '-'
		}
	}
})
function isFirstHalfOfMonth() {
	const currentDate = new Date()
	const dayOfMonth = currentDate.getDate()
	return dayOfMonth <= 15
}
function isFirstHalfOfYear() {
	const currentDate = new Date()
	const monthOfYear = currentDate.getMonth()
	return monthOfYear < 6
}
const getReadTime = computed(() => {
	const date = new Date()
	const weekDays = ['一', '二', '三', '四', '五', '六', '日']
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const day = date.getDate()
	const week = date.getDay() == 0 ? 7 : date.getDay()
	const qtr = month <= 3 ? 1 : month <= 6 ? 2 : month <= 9 ? 3 : 4
	return (time: any) => {
		if (time?.interval == 2) {
			if (time?.weeklyDeadlineDayOfWeek !== null) {
				return `每周${weekDays[time?.weeklyDeadlineDayOfWeek - 1]}前`
			} else {
				return '未设置'
			}
		} else if (time?.interval == 3) {
			if (time?.monthlyDeadlineDay) {
				return `每月${time?.monthlyDeadlineDay}日前`
			} else {
				return '未设置'
			}
		} else if (time?.interval == 4) {
			if (time['quarterlyDeadlineDay' + qtr] !== null) {
				const text = new Date(
					time['quarterlyDeadlineDay' + qtr].substring(4).replace(/./, year + '-')
				)
				return `${text.getMonth() + 1}月${text.getDate()}日前`
			} else {
				return '未设置'
			}
		} else if (time?.interval == 5) {
			if (time?.yearlyDeadlineDay !== null) {
				const text = new Date(time?.yearlyDeadlineDay.substring(4).replace(/./, year + '-'))
				return `${text.getMonth() + 1}月${text.getDate()}日前`
			} else {
				return '未设置'
			}
		} else if (time?.interval == 6) {
			if (isFirstHalfOfMonth()) {
				// 上半个月
				if (time?.downHalfMonthReminderTime) {
					return `每月${time?.downHalfMonthReminderTime}日前`
				} else {
					return '未设置'
				}
			} else {
				if (time?.downHalfMonthDateOnlyTime) {
					return `每月${time?.downHalfMonthDateOnlyTime}日前`
				} else {
					return '未设置'
				}
			}
		} else if (time?.interval == 7) {
			if (isFirstHalfOfYear()) {
				if (time.upHalfYearDateOnlyTime !== null) {
					const text = new Date(
						time.upHalfYearDateOnlyTime.substring(4).replace(/./, year + '-')
					)
					return `${text.getMonth() + 1}月${text.getDate()}日前`
				} else {
					return '未设置'
				}
			} else {
				if (time.downHalfYearDateOnlyTime !== null) {
					const text = new Date(
						time.downHalfYearDateOnlyTime.substring(4).replace(/./, year + '-')
					)
					return `${text.getMonth() + 1}月${text.getDate()}日前`
				} else {
					return '未设置'
				}
			}
		}
	}
})

const exportAllButton = ref(false)
const onImportFile = (val: any) => {
	if (val == 'one') {
		let data = {
			ids: statisticsList.value.map((item: any) => item.ledgerId),
		}
		console.log(data.ids)
		if (data.ids.length <= 0) return ElMessage.warning('请选择业务表')
		ledgerDepartmentFiles(data).then((res: any) => {
			const blob = res.data
			const fileName = `导出进度统计.xlsx`

			// if ('download' in document.createElement('a')) {
			// 	const link = document.createElement('a')
			// 	link.download = fileName
			// 	link.style.display = 'none'
			// 	link.href = URL.createObjectURL(blob)
			// 	document.body.appendChild(link)
			// 	link.click()
			// 	URL.revokeObjectURL(link.href)
			// 	document.body.removeChild(link)
			// }

			saveAs(
				new Blob([blob], {
					type: 'apilication/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				}),
				`${fileName}`
			)
			// tableRef.value.clearSelection()
			ElMessage.success('下载成功')
		})
	} else {
		const ids = statisticsList.value.map((item: any) => {
			return item.ledgerId
		})
		if (ids.length <= 0) return ElMessage.warning('请选择业务表')
		ElMessage.warning('文件正在生成中，请耐心等待')
		exportAllButton.value = true
		ledgerDepartmentFilesDetail({
			fillStatisticsType: runwayForm.value.fillStatisticsType,
			ledgerIds: ids,
		}).then((res: any) => {
			const blob = res.data
			const fileName = `导出进度详情.zip`

			saveAs(
				new Blob([blob], {
					type: 'apilication/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				}),
				`${fileName}`
			)
			exportAllButton.value = false
			ElMessage.success('下载成功')
		})
	}
}
const loading = ref(false)

const onClear = () => {
	cascadeValue.value = []
	runwayForm.value.departmentId = null
	isGrade.value = null
	isParentId.value = null
}

const currentDate = ref(new Date())

const previousDay = computed(() => {
	currentDate.value.setHours(0, 0, 0, 0)
	const previousDate = new Date(currentDate.value.getTime() - 24 * 60 * 60 * 1000)

	const formatDate = (date) => {
		let year = date.getFullYear()
		let month = date.getMonth() + 1
		let day = date.getDate()

		month = month < 10 ? '0' + month : month
		day = day < 10 ? '0' + day : day

		return `${year}年${month}月${day}日`
	}
	return formatDate(previousDate)
})
</script>
<template>
	<div class="dataAnalysis">
		<Block
			:enable-fixed-height="true"
			:enable-expand-content="true"
			:title="'填报进度'"
			@heightChanged="onBlockHeightChanged"
		>
			<template #title>
				<div class="titleHeader">
					<p class="titleHeader-text"><span></span>填报进度</p>
					<!-- <small>
						<el-icon><InfoFilled /></el-icon>
						数据统计截止{{ previousDay }}
					</small> -->
				</div>
			</template>
			<template #topRight>
				<el-dropdown trigger="click" style="margin-left: 10px">
					<el-button type="primary" size="small">
						导出数据<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<div class="more-operations">
							<el-button
								type="primary"
								size="small"
								:loading="exportAllButton"
								@click="onImportFile('all')"
								style="color: #fff"
							>
								导出进度详情
							</el-button>
							<el-button
								type="primary"
								size="small"
								@click="onImportFile('one')"
								style="color: #fff"
							>
								导出进度统计
							</el-button>
						</div>
					</template>
				</el-dropdown>
				<el-button
					type="danger"
					size="small"
					:disabled="selectedCount <= 0"
					@click="ledgerArrDelete"
					style="margin-left: 10px"
				>
					批量催办
				</el-button>
			</template>

			<template #expand>
				<div class="search-box" v-action:enter="seniorList">
					<Cascade
						v-model="cascadeValue"
						:one-select="true"
						:one-select-props="oneSelectProps"
						:show-all-levels="true"
						:options="DEPARTMENT_OPTIONS_ENUM"
						:keys="['name', 'id']"
						max-level="3"
						@el-change="onCascadeElChange"
						@clear="onClear"
						placeholder="请选择发布部门"
					></Cascade>
					<el-select
						v-model="runwayForm.ReminderInterval"
						clearable
						@change="seniorList"
						placeholder="请选择业务表更新周期"
						size="default"
					>
						<el-option
							v-for="item in searchOptions"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-input
						class="search-input"
						v-model="runwayForm.filter"
						placeholder="请输入业务表名称"
						size="default"
						clearable
					></el-input>

					<el-select
						v-model="runwayForm.ledgerFillStatus"
						clearable
						@change="seniorList"
						placeholder="请选择是否完成"
						size="default"
					>
						<el-option
							v-for="item in completedList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>

					<!-- 新增预警 -->
					<el-select
						v-model="runwayForm.LitStatus"
						clearable
						@change="seniorList"
						placeholder="请选择预警等级"
						size="default"
					>
						<el-option
							v-for="item in LitStatusList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>

					<el-select
						v-model="runwayForm.departmentLevels"
						clearable
						multiple
						placeholder="请选择填报部门"
						size="default"
					>
						<el-option
							v-for="item in fillingGradeList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-button size="default" type="default" @click="empty">
						<i class="i-ic-baseline-cleaning-services" mr-3px></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="seniorList">
						<i class="i-ic-baseline-send" mr-3px></i>
						查询</el-button
					>
				</div>
			</template>
			<!-- <el-tabs v-model="activeName" class="tabs-ui" @tab-change="onClickTabs" :before-leave="() => !loading" style="margin-bottom: 15px;">
					<el-tab-pane :label="`我的业务表进度`" name="我的业务表进度"></el-tab-pane>
					<el-tab-pane v-if='superAdmin' :label="`全市业务表进度`" name="全市业务表进度"></el-tab-pane>
					<el-tab-pane v-if="role" :label="`区县业务表进度`" name="区县业务表进度"></el-tab-pane>

				</el-tabs> -->
			<TableV2
				ref="tableRef"
				row-key="ledgerId"
				:auto-height="false"
				:columns="colData"
				:height="tableHeight"
				:defaultTableData="tableData"
				:headers="{Urlkey: 'ledger'}"
				:enableToolbar="false"
				:enable-create="false"
				:enable-edit="false"
				:enable-delete="false"
				:enableSelection="true"
				:enableIndex="false"
				:req-params="reqParams"
				:buttons="buttons"
				:loading="loading"
				@clickButton="onTableClickButton"
				@selection-change="selectionChange"
				@loading="loading = $event"
			>
				<template #name="{row}">
					<span
						class="link-click"
						@click="
							$router.push({path: '/ledger/fill', query: {ledgerId: row.ledgerId}})
						"
					>
						{{ row.ledger?.name }}
					</span>
				</template>
				<template #reminderInterval="{row}">
					{{ getReadCycle(row.ledger?.taskInterval) }}
				</template>
				<template #deadline="{row}">
					{{ row.ledger?.deadline }}
				</template>
				<template #creatorDepartmentName="{row}">
					{{ row.ledger?.ledgerType?.name }}
				</template>

				<template #lastOnlineTime="{row}">
					{{ row.ledger?.lastOnlineTime }}
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			>
			</Pagination>
		</Block>

		<!-- 通知类型选择弹窗 -->
		<el-dialog
			v-model="notifyTypeDialogVisible"
			title="请选择通知类型"
			:visible.sync="notifyTypeDialogVisible"
			width="30%"
		>
			<el-checkbox-group v-model="notifyTypes">
				<el-checkbox
					v-for="item in notifyTypeOptions"
					:key="item.value"
					:label="item.value"
				>
					{{ item.label }}
				</el-checkbox>
			</el-checkbox-group>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="notifyTypeDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="confirmUrge">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style scoped lang="scss">
.search-box {
	align-items: center;
	display: flex;
	height: 62px;
	overflow: hidden;
	padding: 15px;
	transition: all 0.15s linear;
	white-space: nowrap;
	width: 100%;

	> div {
		margin-right: 15px;
		width: 25%;
	}
}
.more-operations {
	// width: 200px;
	// flex-wrap: wrap;
	display: flex;
	padding: 5px;
	span {
		color: #fff !important;
	}
	:deep(button) {
		&:nth-child(5n) {
			margin-right: 0;
		}

		span {
			color: #fff !important;
		}
	}
}
.titleHeader {
	display: flex;
	align-items: center;
	p {
		margin-right: 10px;
		font-size: 16px;
	}
}
</style>
