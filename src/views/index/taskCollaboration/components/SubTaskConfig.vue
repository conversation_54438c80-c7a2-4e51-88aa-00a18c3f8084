<script setup lang="ts">
import {defineProps, ref, watch} from 'vue'
import {useUserStore} from '@/stores/useUserStore'
import axios from 'axios'
import {useArrayToTree} from '@/hooks/useConvertHook'
import {STAFFROLEARRAY} from '@/define/organization.define'
import {CreateFlowNode, UpdateFlowNode} from '@/api/TaskCollaborationApi'

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
	node: {
		type: [Object, null],
		default: () => ({}),
	},
})

const userStore = useUserStore()
const loading = ref(false)

const treeRef = ref()
const treeProps = {
	label: 'label',
	value: 'value',
	children: 'children',
	isLeaf: 'isLeaf',
}

const formProps = ref([
	{
		label: '子任务名称',
		prop: 'taskName',
		type: 'text',
	},
	{
		label: '责任人',
		prop: 'user',
		type: 'text',
	},
	{
		label: '开始时间',
		prop: 'startTime',
		type: 'datetime',
	},
	{
		label: '结束时间',
		prop: 'endTime',
		type: 'datetime',
	},
])
const formRule = ref({
	taskName: [{required: true, message: '请输入子任务名称', trigger: 'blur'}],
	user: [{required: true, message: '请输入责任人', trigger: 'blur'}],
})
const form: any = ref({user: []})

const selectDepartmentGroup: any = ref([])

const onConfirm = () => {
	if (!props.node) return

	const data = {
		missionObjectivesId: props.node.data.missionObjectivesId,
		parentId: props.node.data.parentId,
		userId: userStore.getUserInfo.id,
		userName: userStore.getUserInfo.name,
		name: form.value.taskName,
		description: form.value.user.join(','),
		startTime: form.value.startTime,
		endTime: form.value.endTime,
	}

	if (!props.node.data.raw) {
		CreateFlowNode(data).then((res: any) => {
			emit('update:modelValue', false)
		})
	} else {
		UpdateFlowNode(props.node.id, data).then((res: any) => {
			emit('update:modelValue', false)
		})
	}
}

const loadNode = (node: any, resolve: any) => {
	if (node.data.length === 0 || !node.data.id) return
	axios
		.request({
			method: 'get',
			url: `/api/platform/departmentInternal/${node.data.id}/bind-users`,
		})
		.then((users: any) => {
			const userList =
				users.data.items
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
					.map((res: any) => ({
						label: res.name,
						value: res?.department?.id + '/' + res.id,
						departmentId: res?.department?.id,
						isLeaf: true,
						disabled: res?.department?.id ? false : true,
					})) ?? []
			resolve(node.data.children.concat(userList))
		})
}

const loadNodeCustom = async (id: string) => {
	return new Promise((resolve) => {
		if (treeRef.value.getNode(id).data.children.length > 0) {
			resolve('')
			return
		}
		axios
			.request({
				method: 'get',
				url: `/api/platform/departmentInternal/${id}/bind-users`,
			})
			.then((users: any) => {
				const userList =
					users.data.items
						.filter(
							(user: any) =>
								(user.staffRole.includes(STAFFROLEARRAY[2]) ||
									user.staffRole.includes(STAFFROLEARRAY[0]) ||
									user.staffRole.includes(STAFFROLEARRAY[3])) &&
								user.id !==
									JSON.parse(localStorage.getItem('currentUserInfo') as string).id
						)
						.map((res: any) => ({
							label: res.name,
							value: res?.department?.id + '/' + res.id,
							departmentId: res?.department?.id,
							isLeaf: true,
							disabled: res?.department?.id ? false : true,
						})) ?? []

				console.log(111, treeRef.value)

				treeRef.value.updateKeyChildren(id, userList)
				resolve('')
			})
	})
}

const filterMethod = (query?: string) => {
	if (query) {
		loading.value = true
		axios
			.request({
				method: 'get',
				url: `/api/platform/department/department-bind-users?filter=${query}`,
			})
			.then((res: any) => {
				loading.value = false
				const {data} = res

				const arr = data.items
					.map((v: any) => ({
						...v,
						label: v.name,
						name: v.department?.parent?.name + '-' + v.department?.name + '-' + v.name,
						value: v?.department?.id + '/' + v.id,
						departmentId: v?.department?.id,
						isLeaf: true,
						disabled: false,
					}))
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
				selectDepartmentGroup.value = useArrayToTree(
					arr,
					'id',
					'parentId',
					'name',
					true,
					'children'
				)
			})
	} else {
		getDepartmentChildren()
	}
}

const getDepartmentChildren = async () => {
	return new Promise((resolve) => {
		axios
			.request({
				method: 'get',
				url: '/api/platform/departmentInternal/get-department-children',
			})
			.then((res) => {
				const {data} = res
				const arr = data.map((v: any) => ({
					...v,
					disabled: v?.department?.departmentId ? false : true,
					children: v.children === null ? [] : v.children,
				}))

				selectDepartmentGroup.value = useArrayToTree(
					arr,
					'id',
					'parentId',
					'name',
					true,
					'children'
				)
				resolve('')
			})
			.catch((error: any) => {})
	})
}

const onOpened = async () => {
	await getDepartmentChildren()
	const node = props.node
	if (node && node.data.raw) {
		const {raw} = node.data
		const split = raw.description.split(',')

		for (const item of split) {
			const [departmentId, _] = item.split('/')
			await loadNodeCustom(departmentId)
		}

		form.value = {
			taskName: raw.name,
			user: split,
			startTime: raw.startTime,
			endTime: raw.endTime,
		}
	} else {
		form.value = {
			taskName: '',
			user: [],
			startTime: null,
			endTime: null,
		}
	}
}
</script>
<template>
	<div class="sub-task-config">
		<Drawer
			v-bind="$attrs"
			:enable-close="false"
			:destroy-on-close="true"
			title="子任务属性明确配置"
			@opened="onOpened"
			@click-confirm="onConfirm"
			@close="emit('update:modelValue', false)"
		>
			<Form
				v-model="form"
				:props="formProps"
				:rules="formRule"
				:enable-button="false"
				label-width="95"
			>
				<template #form-user>
					<el-tree-select
						ref="treeRef"
						v-model="form.user"
						node-key="id"
						:data="selectDepartmentGroup"
						:load="loadNode"
						:props="treeProps"
						:remote-method="filterMethod"
						:loading="loading"
						filterable
						multiple
						remote
						lazy
						@change="(val) => console.log(val)"
					>
					</el-tree-select>
				</template>
			</Form>
		</Drawer>
	</div>
</template>
<style scoped lang="scss"></style>
