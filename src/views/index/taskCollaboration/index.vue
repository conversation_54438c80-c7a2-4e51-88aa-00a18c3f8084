<script setup lang="ts" name="taskcollaboration">
import {reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ReportsFlowStatus} from '@/define/statement.define'
import {FlowExecutionCycle} from '@/define/workFlowTask.define'
import {UpdateMissionObjectives} from '@/api/TaskCollaborationApi'
import {ElMessage} from 'element-plus'

const router = useRouter()
const loading = ref(false)

const columns = [
	{label: '任务名称', prop: 'taskName'},
	{label: '任务类型', prop: 'taskType'},
	{label: '创建部门-科室-人', prop: 'creator', width: 205},
	{label: '开始时间', prop: 'startTime', width: 175},
	{label: '执行周期', prop: 'executePeriod'},
	{label: '任务状态', prop: 'taskStatus'},
	{label: '任务启停', prop: 'isMission'},
	// {label: '执行流程', prop: 'executeFlow'},
]

const formProps = ref([{label: '任务名称', prop: 'taskName', type: 'text'}])
const form: any = ref({})

const tableRef = ref()
const tableData = ref([])
const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams = reactive({
	taskName: null,
	skipCount: 0,
	maxResultCount: 10,
})
const status = ReportsFlowStatus.filter((f: any) => f.type == 'plan')

const onSubmit = () => {
	reqParams.skipCount = 0
	reqParams.maxResultCount = pagination.size
	reqParams.taskName = form.value.taskName
}

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onTableButtonClick = ({btn, row, index}: any) => {
	console.log(btn, row)

	if (btn.code == 'disassembly') {
		router.push({
			path: '/taskCollaboration/taskflow',
			query: {
				id: row.id,
				name: row.planTask.name,
			},
		})
	} else if (btn.code == 'view') {
		router.push({
			path: '/flowTask/detail',
			query: {
				id: row.reportTaskId,
				rid: row.id,
				from: 'taskprogress',
			},
		})
	}
}

const onTaskStartStopChange = (val: any, row: any) => {
  // 先恢复开关状态，避免直接改变
  row.isMission = !val;
  ElMessageBox.confirm(`确定${val ? '启用' : '停用'}吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    UpdateMissionObjectives(row.id, {isMission: val}).then(() => {
      // 接口调用成功后更新状态
      row.isMission = val;
      ElMessage.success(val ? '启用成功' : '停用成功');
    }).catch(() => {
      // 接口调用失败后恢复状态
      row.isMission = !val;
      ElMessage.error('操作失败');
    });
  }).catch(() => {
    // 用户取消操作，恢复开关状态
    row.isMission = !val;
  });
}
</script>
<template>
	<div class="task-collaboration">
		<Block title="目标拆解" :enable-fixed-height="true" @heightChanged="onBlockHeightChanged">
			<template #expand>
				<div class="search">
					<Form
						:props="formProps"
						v-model="form"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						:loading="loading"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSubmit"
					></Form>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/newFeature/mission-objectives"
				v-model="tableData"
				:columns="columns"
				:req-params="reqParams"
				:enable-toolbar="false"
				:height="tableHeight"
				:enable-own-button="false"
				:buttons="[
					{
						code: 'view',
						label: '详情',
						type: 'primary',
					},
					{
						code: 'disassembly',
						label: '任务目标拆解',
						type: 'primary',
					},
				]"
				@loading="($event: boolean) => loading = $event"
				@click-button="onTableButtonClick"
				@completed="
					() => {
						pagination.total = tableRef.getTotal()
					}
				"
			>
				<template #taskName="scope">
					<el-link
						type="primary"
						@click="onTableButtonClick({btn: {code: 'view'}, row: scope.row})"
					>
						{{ scope.row?.planTask?.name }}
					</el-link>
				</template>
				<template #taskType="scope">
					{{ scope.row.planTask.taskType }}
				</template>
				<template #creator="scope">
					{{ scope.row.planTask.creatorDepartmentName }}
					-
					{{ scope.row.planTask.creatorUserName }}
				</template>
				<template #startTime="scope">
					{{ scope.row.planTask.executionStartTime }}
				</template>
				<template #executePeriod="scope">
					{{
						FlowExecutionCycle.filter(
							(v: any) =>
								v.value === scope.row.planTask.executionIntervalConfig?.interval
						)[0]?.label
					}}
				</template>
				<template #taskStatus="scope">
					{{ status.find((f: any) => f.value == scope.row.planTask.status)?.label }}
				</template>
				<template #executeFlow="scope">
					{{ scope.row.schemeName || '-' }}
				</template>
				<template #isMission="scoped">
					<!-- <FormItem
						v-model="scoped.row.isMission"
						:items="[{prop: 'isMission', type: 'switch'}]"
						@change="onTaskStartStopChange($event, scoped.row)"
						style="margin: 0"
					></FormItem> -->
					<el-switch
						:model-value="scoped.row.isMission"
						@change="onTaskStartStopChange($event, scoped.row)"
						style="margin: 0"
					/>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>
	</div>
</template>
<route>
    {
		meta: {
			title: '任务目标拆解',
		},
	}
</route>
<style scoped lang="scss"></style>
