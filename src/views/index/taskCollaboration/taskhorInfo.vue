<script setup lang="ts" name="taskhorinfo">
import {onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {updateLabelTitle} from '@/hooks/useLabels'

const router = useRouter()
const formProps = ref([
	{label: '任务名称', prop: 'taskName', type: 'text'},
	{label: '创建科室', prop: 'creatorDepartment', type: 'text'},
	{label: '创建人', prop: 'creator', type: 'text'},
	{label: '填报开始时间', prop: 'startTime', type: 'text'},
	{label: '填报部门', prop: 'endTime', type: 'text', full: true},
	{label: '填报周期', prop: 'executePeriod', type: 'text'},
	{label: '填报说明', prop: 'taskStatus', type: 'text'},
])
const form = ref({})

const reqParams = reactive({
	skipCount: 0,
	maxResultCount: 10,
})

onMounted(() => {
	updateLabelTitle({
		path: router.currentRoute.value.fullPath,
		title: '任务详情',
	})
})
</script>
<template>
	<div class="task-collaboration">
		<Block
			title="详情"
			:enable-fixed-height="false"
			:enable-expand-content="false"
			:enable-close-button="false"
		>
			<Form
				:props="formProps"
				v-model="form"
				:column-count="4"
				:grid="true"
				class="mg-bottom-20"
			></Form>
			<TableV2
				url=""
				:req-params="reqParams"
				:enable-toolbar="false"
				:auto-height="true"
			></TableV2>
		</Block>
	</div>
</template>
<route>
    {
		meta: {
			title: '任务详情',
		},
	}
</route>
<style scoped lang="scss"></style>
