<script setup lang="ts" name="taskremind">
import {reactive, ref} from 'vue'
import {PostAutoReminders, PutAutoReminders, DeleteAutoReminders} from '@/api/TaskCollaborationApi'
import {ElMessage} from 'element-plus'

const formRef = ref()
const formProps = ref([{label: '提醒标题', prop: 'filter', type: 'text'}])
const formSearch = ref({filter: null})

const columns = [
	{label: '提醒标题', prop: 'reminderName'},
	{label: '提醒类型', prop: 'reminderType'},
	{label: '提醒时间', prop: 'reminderTime'},
	{label: '提醒内容', prop: 'reminderContent'},
]

const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref({id: ''})
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams = reactive({
	filter: null,
	skipCount: 0,
	maxResultCount: 10,
})

const show = ref(false)
const dialogFormProps = ref([
	{label: '提醒标题', prop: 'reminderName', type: 'text'},
	{
		label: '提醒类型',
		prop: 'reminderType',
		type: 'select',
		options: [
			{label: '任务开始通知', value: '任务开始通知'},
			{label: '任务完成通知', value: '任务完成通知'},
			{label: '任务暂停通知', value: '任务暂停通知'},
			{label: '任务恢复通知', value: '任务恢复通知'},
			{label: '任务取消通知', value: '任务取消通知'},
		],
	},
	{label: '提醒时间', prop: 'reminderTime', type: 'datetime'},
	{label: '提醒内容', prop: 'reminderContent', type: 'textarea'},
])
const formRules = {
	reminderName: [{required: true, message: '请输入提醒标题', trigger: 'blur'}],
	reminderType: [{required: true, message: '请输入提醒类型', trigger: 'blur'}],
	reminderTime: [{required: true, message: '请输入提醒时间', trigger: 'blur'}],
	reminderContent: [{required: true, message: '请输入提醒内容', trigger: 'blur'}],
}
const form: any = ref({})
const isNew = ref(true)

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.maxResultCount = pagination.size
	reqParams.filter = formSearch.value.filter
}

const onTableButtonClick = ({btn, row}: any) => {
	currentRow.value = row
	if (btn.code == 'edit') {
		show.value = true
		isNew.value = false
		form.value = row
	} else if (btn.code == 'delete') {
		DeleteAutoReminders(row.id).then(() => {
			ElMessage.success('删除成功')
			tableRef.value.reload()
		})
	}
}

const onClickAdd = () => {
	isNew.value = true
	form.value = {}
	show.value = true
}

const onConfirm = () => {
	formRef.value.validate((valid: boolean) => {
		if (valid) {
			if (isNew.value) {
				PostAutoReminders(Object.assign(form.value, {isEnabled: true, sortOrder: 1})).then(
					() => {
						show.value = false
						ElMessage.success('添加成功')
						tableRef.value.reload()
					}
				)
			} else {
				PutAutoReminders(
					currentRow.value.id,
					Object.assign(form.value, {isEnabled: true, sortOrder: 1})
				).then(() => {
					show.value = false
					ElMessage.success('修改成功')
					tableRef.value.reload()
				})
			}
		}
	})
}
</script>
<template>
	<div class="taskremind">
		<Block title="自动提醒" :enable-fixed-height="true" @heightChanged="onBlockHeightChanged">
			<template #topRight>
				<el-button size="small" type="primary" @click="onClickAdd"> 新增提醒 </el-button>
			</template>
			<template #expand>
				<div class="search">
					<Form
						:props="formProps"
						v-model="formSearch"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					></Form>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/new-feature/auto-reminders"
				:columns="columns"
				:req-params="reqParams"
				:enable-toolbar="false"
				:height="tableHeight"
				:enable-own-button="false"
				:buttons="[
					{code: 'edit', label: '编辑', type: 'primary'},
					{code: 'delete', label: '删除', type: 'danger', popconfirm: '确认删除吗？'},
				]"
				@click-button="onTableButtonClick"
				@completed="
					() => {
						pagination.total = tableRef.getTotal()
					}
				"
			>
				<!-- <template #reminderTime="scope">
					任务截止前{{ scope.row.reminderTime }}天
				</template> -->
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>

		<Dialog v-model="show" :title="isNew ? '新增提醒' : '编辑提醒'" @click-confirm="onConfirm">
			<Form
				ref="formRef"
				:props="dialogFormProps"
				v-model="form"
				:rules="formRules"
				:enable-button="false"
			>
				<!-- <template #form-reminderTime="scope">
					任务截止前
					<el-input-number
						v-model="scope.form.reminderTime"
						:min="1"
						:max="365"
						class="mg-left-10 mg-right-10"
					></el-input-number>
					天
				</template> -->
			</Form>
		</Dialog>
	</div>
</template>
<route>
    {
		meta: {
			title: '自动提醒',
		},
	}
</route>
<style scoped lang="scss"></style>
