<script setup lang="ts" name="taskhor">
import {reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {FlowExecutionCycle} from '@/define/workFlowTask.define'
import {
	GetMissionObjectives,
	PostReportCollaboration,
	PutReportCollaboration,
	DeleteReportCollaboration,
} from '@/api/TaskCollaborationApi'
import dayjs from 'dayjs'
import {ElMessage} from 'element-plus'

const router = useRouter()
const loading = ref(false)
const loadingText = ref('')
const formProps = ref([{label: '任务名称', prop: 'taskName', type: 'text'}])
const form = ref({taskName: ''})

const show = ref(false)
const isNew = ref(true)
const formDialog = ref({taskName: ''})
const formDialogProps: any = ref([
	{
		prop: 'taskName',
		label: '任务名称',
		type: 'select',
		options: [],
	},
])
const curTaskItem: any = ref(null)

const columns = [
	{label: '任务名称', prop: 'taskName'},
	{label: '任务类型', prop: 'taskType'},
	{label: '创建部门-科室-人', prop: 'creatorDepartment', width: 205},
	{label: '开始时间', prop: 'startTime', width: 175},
	{label: '执行周期', prop: 'executionCycle'},
	{label: '任务状态', prop: 'taskStatus'},
	// {label: '执行流程', prop: 'executionProcess'},
]

const tableRef = ref()
const curTableRow: any = ref(null)
const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const reqParams: any = reactive({
	filter: null,
	skipCount: 0,
	maxResultCount: 10,
})
const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const onTableButtonClick = ({btn, row}: any) => {
	curTableRow.value = row
	if (btn.code === 'edit') {
		show.value = true
		isNew.value = false
		formDialog.value.taskName = row.taskName
	} else if (btn.code === 'delete') {
		DeleteReportCollaboration(row.id).then(() => {
			ElMessage.success('删除成功')
			tableRef.value.reload()
		})
	} else {
		router.push({
			path: '/flowTask/detail',
			query: {
				id: row.missionObjectives.reportTaskId,
				rid: row.missionObjectivesId,
				from: 'taskhor',
			},
		})
	}
}

const onAddClick = () => {
	show.value = true
	isNew.value = true
	formDialog.value.taskName = ''
}

const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.filter = form.value.taskName
}

const onFormChange = (val: any, prop: any) => {
	curTaskItem.value = formDialogProps.value[0].options.find((item: any) => item.value === val)
}

const onDialogOpen = () => {
	if (formDialogProps.value[0].options.length > 0) {
		return
	}
	loading.value = true
	loadingText.value = '正在获取任务列表...'
	GetMissionObjectives({skipCount: 0, maxResultCount: 9999})
		.then((res: any) => {
			console.log(res)
			const {items} = res.data
			items.forEach((item: any) => {
				formDialogProps.value[0].options.push({
					label: item.planTask.name,
					value: item.planTask.id,
					_raw: JSON.parse(JSON.stringify(item)),
				})
			})
		})
		.finally(() => {
			loading.value = false
		})
}

const onDialogConfirm = () => {
	if (!curTaskItem.value) {
		return
	}
	const {planTask} = curTaskItem.value._raw
	const reqData = {
		missionObjectivesId: curTaskItem.value._raw.id,
		taskName: planTask.name,
		taskType: planTask.taskType,
		creatorDepartment: planTask.creatorDepartmentName + '-' + planTask.creatorUserName,
		creatorName: planTask.creatorUserName,
		executionCycle: FlowExecutionCycle.filter(
			(v: any) => v.value === planTask.executionIntervalConfig?.interval
		)[0]?.label,
		executionProcess: curTaskItem.value._raw.schemeName,
		taskStatus: '执行中',
		remarks: '单元测试备注',
		isEnabled: true,
		sortOrder: 1,
		startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
	}

	if (isNew.value) {
		PostReportCollaboration(reqData).then(() => {
			show.value = false
			ElMessage.success('添加成功')
			curTableRow.value = null
			curTaskItem.value = null
			tableRef.value.reload()
		})
	} else {
		PutReportCollaboration(curTableRow.value.id, reqData).then(() => {
			show.value = false
			ElMessage.success('修改成功')
			curTableRow.value = null
			curTaskItem.value = null
			tableRef.value.reload()
		})
	}
}
</script>
<template>
	<div class="task-collaboration">
		<Block
			title="报表横向协同"
			:enable-fixed-height="true"
			@heightChanged="onBlockHeightChanged"
		>
			<template #topRight>
				<!-- <el-button size="small" type="primary" @click="onAddClick">新增采集任务</el-button> -->
			</template>
			<template #expand>
				<div class="search">
					<Form
						:props="formProps"
						v-model="form"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					></Form>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				url="/api/newFeature/report-collaboration"
				:columns="columns"
				:req-params="reqParams"
				:enable-own-button="false"
				:buttons="[
					{code: 'add', label: '查看', type: 'primary'},
					// {code: 'edit', label: '编辑', type: 'primary'},
					// {code: 'delete', label: '删除', type: 'danger', popconfirm: '确认删除吗？'},
				]"
				:enable-toolbar="false"
				:height="tableHeight"
				@click-button="onTableButtonClick"
				@completed="
					() => {
						pagination.total = tableRef.getTotal()
					}
				"
			></TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>
		<Dialog
			v-model="show"
			:title="isNew ? '新增采集任务' : '编辑采集任务'"
			:loading="loading"
			:loading-text="loadingText"
			@open="onDialogOpen"
			@click-confirm="onDialogConfirm"
		>
			<Form
				v-model="formDialog"
				:props="formDialogProps"
				:column-count="2"
				:label-width="74"
				:enable-reset="false"
				:enable-button="false"
				@change="onFormChange"
			></Form>
		</Dialog>
	</div>
</template>
<route>
    {
		meta: {
			title: '报表横向协同',
		},
	}
</route>
<style scoped lang="scss"></style>
