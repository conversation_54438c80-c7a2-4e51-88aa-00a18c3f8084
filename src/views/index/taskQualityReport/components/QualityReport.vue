<template>
	<div>
		<Dialog
			v-if="state.drawer"
			width="900"
			:loading="loading"
			height="500"
			v-model="state.drawer"
			title="任务质量报告水印"
			@clickConfirm="handleConfirm"
			size="50%"
			:with-header="false"
			:enable-confirm="false"
		>
			<div class="search">
				<div>报告水印名称:</div>
				<el-input
					style="width: 160px; margin: 0 12px"
					clearable
					v-model="reportName"
				></el-input>
				<el-button type="primary" @click="handleSearch">搜索</el-button>
			</div>
			<!-- 操作按钮 -->
			<div class="btn-box">
				<el-button type="primary" @click="goAdd">新增</el-button>
				<el-button
					type="primary"
					:disabled="!selectionReportList.length"
					@click="delReportData(2)"
					>删除水印</el-button
				>
				<el-button
					type="primary"
					:disabled="!selectionReportList.length"
					@click="cancelBind"
					>解绑报告</el-button
				>
			</div>
			<!-- 数据表格 -->
			<div style="padding: 0 12px">
				<el-table
					stripe
					v-loading="loading"
					default-expand-all
					ref="baseTable"
					:border="true"
					:data="tableData"
					:show-overflow-tooltip="true"
					:scrollbar-always-on="true"
					style="width: 100%; height: 300px"
					@selection-change="reportChange"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column label="序号" width="60" fixed="left" align="center">
						<template #default="scope">
							{{ state.pageSize * (state.currentPage - 1) + scope.$index + 1 }}
						</template>
					</el-table-column>
					<el-table-column
						:prop="item.prop"
						align="center"
						v-for="item in tableFields"
						:label="item.label"
						:key="item.prop"
						:width="item.width"
					>
						<template #default="{row}">
							<span v-if="item.prop === 'applyStatus'">{{
								typeOptions.find((typeItem) => typeItem.value == row[item.prop])
									?.label
							}}</span>
							<span v-else>{{ row[item.prop] }}</span>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" min-width="120">
						<template #default="{row}">
							<el-button
								link
								type="primary"
								size="small"
								@click="actionData(row, '1')"
							>
								查看水印
							</el-button>
							<el-button
								link
								type="primary"
								size="small"
								@click="actionData(row, '2')"
								>编辑</el-button
							>
							<el-button
								link
								type="primary"
								size="small"
								@click="delReportData(1, row.id)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<div style="display: flex; justify-content: flex-end; padding: 12px">
				<el-pagination
					background
					:page-sizes="[100, 200, 300, 400]"
					layout="total, sizes, prev, pager, next, jumper"
					:total="allTaskQualityData.length"
					v-model:current-page="state.currentPage"
					:page-size="state.pageSize"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</Dialog>
		<!-- 新增报告水印-->
		<Dialog
			v-model="state.showAddWaterText"
			:loading="loading"
			:title="state.addType == 'add' ? '新增报告水印' : '编辑报告水印'"
			width="850"
			height="600"
			@clickConfirm="applyConfirm"
			@close="() => (state.showAddWaterText = false)"
		>
			<div style="padding-left: 20px">
				<div style="margin: 20px 0; display: flex; align-items: center">
					<div style="width: 110px; text-align: left">报告水印名称：</div>
					<el-input
						style="width: 220px"
						clearable
						v-model="state.reportForm.reportName"
						placeholder="请输入"
					></el-input>
				</div>
				<div style="margin: 20px 0; display: flex; align-items: center">
					<div style="width: 110px; text-align: left">水印类型：</div>
					<el-radio-group v-model="state.reportForm.isType" size="large">
						<el-radio label="文字水印" value="1" />
					</el-radio-group>
				</div>
				<div style="display: flex; align-items: center">
					<div style="width: 110px">文字水印：</div>
					<el-input
						style="width: 220px"
						v-model="state.reportForm.isText"
						placeholder="请输入"
					></el-input>
				</div>
				<div class="btn-box">
					<span>绑定报告：</span>
					<el-button
						type="primary"
						:disabled="!selectionList.length"
						@click="batchAction('2')"
						>批量绑定</el-button
					>
					<el-button
						type="primary"
						:disabled="!selectionList.length"
						@click="batchAction('1')"
						>批量解绑</el-button
					>
				</div>
			</div>
			<div style="padding-left: 80px">
				<div class="search">
					<div>任务名称:</div>
					<el-input style="width: 160px; margin: 0 12px" v-model="taskName"></el-input>
					<el-button type="primary" clearable @click="searchTask">搜索</el-button>
				</div>
				<el-table
					stripe
					v-loading="loading"
					default-expand-all
					ref="baseTable"
					:border="true"
					:data="bindTableData"
					:show-overflow-tooltip="true"
					:scrollbar-always-on="true"
					style="width: 100%; height: 200px"
					@selection-change="handleSelectionChange"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column
						:prop="item.prop"
						align="center"
						v-for="item in bindTableFields"
						:label="item.label"
						:key="item.prop"
						:width="item.width"
					>
						<template #default="{row}">
							<span v-if="item.prop === 'status'">{{
								row[item.prop] == 1 ? '未绑定' : '已绑定'
							}}</span>
							<span v-else>{{ row[item.prop] }}</span>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" min-width="120">
						<template #default="{row}">
							<el-button
								v-if="row.status == 2"
								link
								type="primary"
								size="small"
								@click="bindActionData(row.id, '1')"
							>
								解绑
							</el-button>
							<el-button
								link
								v-if="row.status == 1"
								type="primary"
								size="small"
								@click="bindActionData(row.id, '2')"
								>绑定任务</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<!-- 分页 -->
			<!-- <Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onTableV2PaginationChange($event, 'page')"
				@size-change="onTableV2PaginationChange($event, 'size')"
			></Pagination> -->
		</Dialog>
		<!-- 查看水印 -->
		<Dialog
			v-model="state.seeDialog"
			title="查看水印"
			width="300"
			height="300"
			@clickConfirm="() => (state.seeDialog = false)"
			@close="() => (state.seeDialog = false)"
		>
			<div
				v-if="state.seeDialog"
				v-watermark="checkText"
				style="width: 260px; height: 220px; background: #bec1c6"
			></div>
		</Dialog>
	</div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, defineExpose} from 'vue'
import {useUserStore} from '@/stores/useUserStore'
import {all, Axios} from 'axios'
import util from '@/plugin/util'
import queryString from 'querystring'
const axios = inject<Axios>('#axios')
const userInfo = useUserStore().getUserInfo
const applyStatus = ref<string>('')
const currentIndex = ref(1)
const editId = ref(0)
// 表格每一项类型
interface DataItem {
	reportName: string
	isType: string
	createTime: string
	isText: string
	id?: number | string
}
// 表格每一项类型
interface bindDataItem {
	id: number
	reportName: string
	isType?: string
	createTime?: string
	isText?: string
	status: string
}
const loading = ref(false)
// 表格数据
let tableData = reactive<DataItem[]>([])
// 所有报告报告水印数据
let allTaskQualityData = reactive<DataItem[]>([])
const state = reactive({
	showAddWaterText: false,
	seeDialog: false,
	drawer: false,
	reportForm: {
		reportName: '',
		isType: '1',
		isText: '1',
		id: '',
	},
	currentPage: 1,
	pageSize: 10,
	addType: 'add',
})
const checkText = ref('')
const applyConfirm = () => {
	if (!state.reportForm.reportName || !state.reportForm.isText || !state.reportForm.isType) {
		return ElMessage.error('请完善数据')
	}
	loading.value = true
	// 新增数据
	if (state.addType === 'add') {
		const obj = {
			reportName: state.reportForm.reportName,
			isType: state.reportForm.isType,
			createTime: getCurrentTimeFormatted(),
			applyUser: userInfo.name,
			applyStatus: '1',
			isText: state.reportForm.isText,
			id: allTaskQualityData.length + 1,
		}

		localStorage.setItem('taskQualityReportData', JSON.stringify([...allTaskQualityData, obj]))
	}
	// 更新数据
	else {
		allTaskQualityData = allTaskQualityData.map((item) =>
			item.id === editId.value
				? Object.assign({}, item, {...state.reportForm, id: editId.value})
				: item
		)
		localStorage.setItem('taskQualityReportData', JSON.stringify([...allTaskQualityData]))
	}
	setTimeout(() => {
		state.showAddWaterText = false
		loading.value = false
		updateTableData()
	}, 1000)
}
const typeOptions = [
	{
		label: '待审核',
		value: '1',
	},
	{
		label: '已取消',
		value: '2',
	},
	{
		label: '通过',
		value: '3',
	},
	{
		label: '驳回',
		value: '4',
	},
]

const updateBindTableDataItem = (id: number, updatedData: Partial<bindDataItem>) => {
	// 更新 allBindTableData
	allBindTableData.value = allBindTableData.value.map((item) =>
		item.id === id ? Object.assign({}, item, updatedData) : item
	)

	// 更新 bindTableData，注意使用 bindTableData.value 作为数据源
	bindTableData.value = bindTableData.value.map((item) =>
		item.id === id ? Object.assign({}, item, updatedData) : item
	)

	localStorage.setItem('allBindTableData', JSON.stringify(allBindTableData.value))
}
// 查看 | 编辑 | 删除
const actionData = (row: any, type: string) => {
	if (type == '1') {
		state.seeDialog = true
		checkText.value = row.isText
	} else if (type == '2') {
		state.addType = 'edit'
		editId.value = row.id
		for (const key in state.reportForm) {
			state.reportForm[key] = row[key]
		}
		state.showAddWaterText = true
	}
}
const bindActionData = (id: number, type: string) => {
	ElMessageBox.confirm(`确定${type == '2' ? '绑定任务' : '解绑'}吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			updateBindTableDataItem(id, {status: type})
			setTimeout(() => {
				ElMessage.success('操作成功')
			}, 500)
		})
		.catch(() => {})
}

const getCurrentTimeFormatted = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始
	const day = String(now.getDate()).padStart(2, '0')
	const hours = String(now.getHours()).padStart(2, '0')
	const minutes = String(now.getMinutes()).padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}`
}
const tableFields = [
	{
		prop: 'reportName',
		label: '报告水印名称',
		width: 160,
	},
	{
		prop: 'isType',
		label: '水印类型',
		width: 160,
	},
	{
		prop: 'createTime',
		label: '创建时间',
		width: 160,
	},
]
const allBindTableData = ref<bindDataItem[]>([])
const bindTableData = ref<bindDataItem[]>([])
const bindTableFields = [
	{
		prop: 'taskName',
		label: '任务名称',
		width: 160,
	},
	{
		prop: 'status',
		label: '是否绑定',
		width: 160,
	},
]
const reportName = ref('')
const handleSearch = () => {
	if (!reportName.value) {
		tableData.splice(0, tableData.length, ...allTaskQualityData)
	} else {
		const filteredData = allTaskQualityData.filter(
			(item) => item.reportName === reportName.value
		)
		tableData.splice(0, tableData.length, ...filteredData)
	}
}
const taskName = ref('')
const searchTask = () => {
	if (!taskName.value) {
		bindTableData.value.splice(0, bindTableData.value.length, ...allBindTableData.value)
	} else {
		const filteredData = allBindTableData.value.filter((item) => {
			return item.taskName.indexOf(taskName.value) != -1
		})

		bindTableData.value.splice(0, bindTableData.value.length, ...filteredData)
	}
}
// 展示新增
const goAdd = () => {
	state.showAddWaterText = true
	state.reportForm = {
		reportName: '',
		isType: '',
		isText: '',
		id: '',
	}
}
const searchParams = ref<any>({
	Name: null,
	FillingPeriodType: undefined,
	PlanTaskStatus: undefined,
	isSelf: true,
})

const pageParams = ref({
	MaxResultCount: 10,
	SkipCount: 0,
})

const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
// 获取计划任务列表, 我创建的
const getTaskList = async (filterMode: number = 10) => {
	const params = util.extractNonEmptyValuesFromSearchParams({
		...searchParams.value,
	})
	loading.value = true
	axios
		?.get(
			`/api/filling/plan-task?${queryString.stringify(
				pageParams.value
			)}&${queryString.stringify(params)}`
		)
		.then((res: any) => {
			const {data} = res
			pagination.total = data.totalCount

			loading.value = false
		})
		.catch((error: any) => {
			if (error.response?.status === 500) {
				ElNotification.error('当前操作“报表创建”人员较多，请5分钟后再试')
			}
		})
}
const open = () => {
	state.drawer = true
}
defineExpose({
	open,
})

// 更新数据
const updateTableData = () => {
	state.currentPage = 1
	allTaskQualityData.splice(0, allTaskQualityData.length) // 清空原数组
	const newData = JSON.parse(localStorage.getItem('taskQualityReportData')) || []
	allTaskQualityData.push(...newData)

	if (newData && newData.length) {
		tableData.splice(0, tableData.length, ...newData.slice(0, state.pageSize))
	} else {
		tableData.splice(0, tableData.length)
	}
}
// 组件内部定义类型
const FlowNodeTypes = {
	Input: 'input',
	Output: 'output',
	Report: 'report',
	Review: 'review',
	Gateway: 'gateway',
	Condition: 'condition',
}

const seeDialog = () => {
	state.seeDialog = true
}

// 每页数量改变时触发
const handleSizeChange = (size: number) => {
	state.pageSize = size
	updateTableDataByPagination()
}

// 当前页码改变时触发
const handleCurrentChange = (page: number) => {
	state.currentPage = page
	updateTableDataByPagination()
}

// 根据分页参数更新表格数据
const updateTableDataByPagination = () => {
	const {currentPage, pageSize} = state
	const start = (currentPage - 1) * pageSize
	const end = start + pageSize

	// 截取当前页的数据
	const currentData = allTaskQualityData.slice(start, end)
	tableData.splice(0, tableData.length, ...currentData)
}

const selectionList = ref([])
const handleSelectionChange = (selection: any) => {
	selectionList.value = selection
}

// 批量绑定 | 批量取消绑定
const batchAction = (type: string) => {
	const dataId = selectionList.value.map((item: any) => item.id)
	dataId.forEach((item) => {
		updateBindTableDataItem(item, {status: type})
	})
}
const isData = [
	{
		taskName: '下发配置流程催办',
		status: '1',
		id: 1,
		createTime: '2025-07-17 14:58',
	},
	{
		taskName: '数据填报任务',
		status: '2',
		id: 2,
		createTime: '2025-07-17 14:58',
	},
	{
		taskName: '回访数据调查',
		status: '1',
		id: 3,
		createTime: '2025-07-17 14:58',
	},
	{
		taskName: '数据回填任务',
		status: '2',
		id: 4,
		createTime: '2025-07-17 14:58',
	},
]

const selectionReportList = ref([])
const reportChange = (selection: any) => {
	selectionReportList.value = selection
}
const delReportData = (type: number, rowId?: number) => {
	ElMessageBox.confirm('确定删除数据吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			if (type == 1) {
				let data = allTaskQualityData.filter((item) => item.id != rowId) || []
				tableData.splice(0, tableData.length, ...data)
				localStorage.setItem('taskQualityReportData', JSON.stringify(data))
			} else {
				// 批量删除
				const ids = selectionReportList.value.map((item: any) => item.id)
				let data = allTaskQualityData.filter((item) => !ids.includes(item.id)) || []
				tableData.splice(0, tableData.length, ...data)
				localStorage.setItem('taskQualityReportData', JSON.stringify(data))
			}

			setTimeout(() => {
				ElMessage.success('删除成功')
				updateTableData()
			}, 500)
		})
		.catch(() => {})
}
const cancelBind = () => {
	ElMessageBox.confirm('确定解绑数据吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			setTimeout(() => {
				ElMessage.success('解绑成功')
				updateTableData()
			}, 500)
		})
		.catch(() => {})
}
const handleConfirm = () => {
	ElMessage.success('操作成功')
	reportName.value = ''
	state.drawer = false
}
onMounted(() => {
	state.addType = 'add'
	updateTableData()
	localStorage.setItem('allBindTableData', JSON.stringify(isData))
	setTimeout(() => {
		allBindTableData.value.splice(0, allBindTableData.value.length) // 清空原数组
		const newData = JSON.parse(localStorage.getItem('allBindTableData')) || []
		allBindTableData.value.push(...newData)
		if (newData && newData.length) {
			bindTableData.value.splice(
				0,
				bindTableData.value.length,
				...newData.slice(0, state.pageSize)
			)
		}
	}, 300)
})
</script>

<style scoped lang="scss">
:deep(.el-drawer__body) {
	padding: 12px 0 12px 0 !important;
}

.header-title {
	font-size: 16px;
	font-weight: 500;
	padding-bottom: 16px;
	padding: 12px 12px 16px 12px;
	border-bottom: 1px solid #e9e9e9;
}
.search {
	align-items: center;
	border-bottom: var(--z-border);
	display: flex;
	padding-bottom: 12px;
	white-space: nowrap;

	span {
		font-size: 13px;
		i {
			margin-top: -1px;
		}
	}
}
.btn-box {
	display: flex;
	padding: 20px 0px;
	align-items: center;
}
</style>
