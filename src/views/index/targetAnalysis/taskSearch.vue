<script setup lang="ts" name="template">
import { ref, reactive, defineProps } from "vue";
import { useRoute } from "vue-router";
import { useSleep } from "@/hooks/useSleep";
import { ACTION_KEY, useLocalStorage } from "@/hooks/useLocalStorage";
import { ElMessageBox, ElMessage, dayjs } from "element-plus";
import { time } from "echarts";
const storage: any = useLocalStorage();
const route = useRoute()
// 搜索表单
const searchFormProp = ref([{label: "报告名称", prop: "name", type: "text",placeholder: "请输入报告名称" }]);
const searchForm = ref({ name: "" });
const props = defineProps({
  selectedRows: { type: Array, default: () => [] },
});
// 加载状态
const loading = ref(false);
const tableData = ref([]);
// 表格
const tableRef = ref()
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75 // Block 内容高度 - 分页高度
}
const currentRow = ref(null);
const buttons = [
  { label: "查看", type: "primary", code: "view" },
  { label: "编辑", type: "primary", code: "edit" },

]; // 操作按钮
const columns = [
  { prop: "name", label: "报告名称" },
  { prop: "time", label: "报告生成时间" },
  { prop: "taskName", label: "关联任务" },
  { prop: "operator", label: "操作用户" },

]; // 表头

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 列表请求参数
const reqParams = reactive({
  name: "",
  skipCount: 0,
  maxResultCount: 10,
});
const onOpenDialog = () => {
  loading.value = true;
  useSleep().then(() => {
    const historyData = storage.get("taskDevelopmentReportData");


    tableData.value = historyData || [];
    loading.value = false;
  });
};
const statisticsList = ref([])
const selectionChange = (selection: []) => {
	statisticsList.value = selection
}
const showDialogForm = ref(false); // 新增或编辑弹窗
const dialogFormRef = ref(); // 弹窗表单ref
// 弹窗表单数据
const dialogForm: any = ref({
  name: "",
  type: "文字水印",
  content: "设置默认值",
});
// 弹窗表单属性
const dialogFormProps = ref([
  { label: "报告名称", prop: "name", type: "text" },
  {
    label: "报告信息",
    prop: "message",
  },
  { label: "报告内容", prop: "content", type: "textarea" },
]);
// 弹框表单校验规则
const dialogFormRules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
};

// 查询
const onSearch = () => {
  pagination.page = 1;
  reqParams.skipCount = 0;
  reqParams.maxResultCount = pagination.size;
  // 其他查询参数
  reqParams.name = searchForm.value.name;
};

// 表格操作点击事件, row 当前行数据
const onTableClickButton = ({ row, btn }: any) => {
  if (btn.code === "edit") {
    currentRow.value = row;
    Object.assign(dialogForm.value, row);
    showDialogForm.value = true;

  } else if (btn.code === "delete") {
    useSleep().then(() => {
      tableData.value = tableData.value.filter((item: any) => currentRow.value);
      ElMessage.success("删除成功");
    });
  }
};

// 新增
const onClickAdd = () => {
  tableData.value.push({
    name: "",
    message: (Date.now().toString(36) + Math.random().toString(36).substr(2)).substr(0, 14),
    operator: storage.get("currentUserInfo")?.username,
    taskName: route.query.taskName,
    time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    editTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    content: "",
  })
};
const handleBatchBind = () => {
  console.log(tableData.value)
  statisticsList1.value.map((item: any) => {
    currentRow.value.bindArr.push({
      name: item.name,
    })
  })


  storage.save("reportWatermarkData", tableData.value)
}
const handleBatchUnbind = () => {
  statisticsList1.value.map((item: any) => {
    currentRow.value.bindArr.forEach((v:any)=>{
      if (v.name === item.name) {
        currentRow.value.bindArr.splice(currentRow.value.bindArr.indexOf(v), 1)
      }
    })
  })

  storage.save("taskDevelopmentReportData", tableData.value)

}
const handleBind = (row: any) => {
  currentRow.value.bindArr.push({
    name: row.name,
  })

}
const handleUnbind = (row: any) => {
  row.bindArr.forEach((v:any)=>{
    if (v.name === row.name) {
      row.bindArr.splice(row.bindArr.indexOf(v), 1)
    }
  })
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type == "page") {
    pagination.page = val;
    reqParams.skipCount = (val - 1) * pagination.size;
  } else {
    pagination.size = val;
    reqParams.maxResultCount = pagination.size;
  }
};



// 弹框表单提交
const onDialogConfirm = () => {
  // 处理新增或编辑
  loading.value = true;
  if (currentRow.value) {
    useSleep()
      .then(() => {
        ElMessage.success("编辑成功");
        showDialogForm.value = false;
        tableData.value.map((v, i) => {
          if (v.time === currentRow.value.time) {
            tableData.value[i] = dialogForm.value;
            tableRef.value.time = dayjs().format("YYYY-MM-DD HH:mm:ss");
          }
          storage.save("reportWatermarkData", tableData.value);
        });
      })

      .finally(() => {
        loading.value = false;
      });
  } else {
    useSleep()
      .then(() => {
        ElMessage.success("新增成功");
        showDialogForm.value = false;
        tableData.value.push({
          ...dialogForm.value,
          time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
          bindArr: [],
        });
        console.log(tableData.value);
        storage.save("reportWatermarkData", tableData.value);
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

const handleSelectionChange = (val: any) => {
  statisticsList1.value = val;
};
const statisticsList1 = ref([]);
const selectedRows = ref([]);
const handleClickDelete = (type: string) => {
  if (type === "all") {
    useSleep().then(() => {
      tableData.value =[]
      storage.save("reportWatermarkData", tableData.value);
      ElMessage.success("删除成功");
      tableRef.value.reload();
    });
  } else {
    useSleep().then(() => {
      tableData.value = tableData.value.filter((item: any) => !statisticsList.value.includes(item));
      storage.save("reportWatermarkData", tableData.value);
      ElMessage.success("删除成功");
      tableRef.value.reload();
    });
  }
};
const handleClickUnbind = (type: string) => {
  if (type === "all") {
    useSleep().then(() => {
      ElMessage.success("解绑成功");
      tableRef.value.reload();
    });
  } else {
    useSleep().then(() => {
      ElMessage.success("解绑成功");
      tableRef.value.reload();
    });
  }
};
onMounted(() => {

})
</script>
<template>
  <div class="template">
    <Block
      title="任务开展报告"
      :enable-fixed-height="true"
      :enable-expand-content="true"
      :enable-back-button="false"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button size="small" type="primary" @click="onClickAdd"
          >任务开展报告生成</el-button
        >
        <el-button size="small" type="primary" @click="onClickAdd"
          >任务开展批量导出</el-button
        >
        <el-button size="small" type="primary" @click="onClickAdd"
          >任务开展批量删除</el-button
        >
        <el-dropdown class="mg-left-10 mg-right-10">
          <el-button size="small" type="primary">
            更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleClickDelete('all')"
                >任务开展报告全部导出
              </el-dropdown-item>
              <el-dropdown-item @click="handleClickDelete('select')"
                >任务开展报告全部删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="2"
            :label-width="120"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          ></Form>
        </div>
      </template>
      <!-- 列表 -->
      <TableV2
        ref="tableRef"
        v-model="tableData"
        :columns="columns"
        :req-params="reqParams"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :height="tableHeight"
        :buttons="buttons"
        @loading="loading = $event"
        @click-button="onTableClickButton"
        @completed="
          () => {
            pagination.total = tableRef.getTotal();
          }
        "
        @selection-change="selectionChange"
      >
        <template #buttons="{ row }">
          <el-button type="primary" size="small" @click="handleView(row)"
            >更多操作</el-button
          >
        </template>
      </TableV2>
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      ></Pagination>
    </Block>

    <Dialog
      v-model="showDialogForm"
      :title="currentRow ? '编辑报告水印' : '新增报告水印'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="(currentRow = null), (dialogForm = { type: '文字水印', waterMark: '' })"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :enable-button="false"
      >
        <template #form-message="scoped">
          <div><span>报告编号:{{ scoped.form.message }}</span> <span>关联任务:{{ scoped.form.taskName }}</span></div>
          <div><span>创建人:{{ scoped.form.operator }}</span> <span>创建时间:{{ scoped.form.time }}</span></div>
          <div><span>编辑时间:{{ scoped.form.editTime }}</span></div>
        </template>
      </Form>
      <div style="margin-bottom: 10px" v-if="currentRow">
        绑定报告：
        <el-button type="primary" size="small" @click="handleBatchBind"
          >批量绑定</el-button
        >
        <el-button type="primary" size="small" @click="handleBatchUnbind"
          >批量解绑</el-button
        >
      </div>
      <div
        style="display: flex; align-items: center; margin-bottom: 10px"
        v-if="currentRow"
      >
        <el-input
          placeholder="请输入报告名称"
          style="width: 200px; margin-right: 10px"
        ></el-input>
        <el-button type="primary" size="small">搜索</el-button>
      </div>
    </Dialog>
  </div>
</template>
<route>
{
meta: {
title: '任务开展报告',
},
}
</route>
<style scoped lang="scss"></style>
