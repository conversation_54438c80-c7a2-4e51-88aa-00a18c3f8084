<route>
	{
		meta: {
			childTitle: '',
		},
	}
</route>
<script setup lang="ts" name="detail">
import {ElMessageBox, ElMessage, FormInstance} from 'element-plus'
import {onMounted, ref, reactive, watch, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {
	getDetailApi,
	getLedgerListApi,
	getLedgerTableFieldsApi,
	setPlanProgressConfigApi,
	queryPlanProgressConfigApi,
	updatePlanProgressConfigApi,
	savePlanProgressConfigApi,
	makeSurePlanProgressConfigApi,
	deleteLedgerApi,
	lookLedgerApi,
	headerLedgerApi,
} from '@/api/taskLinkApi'
import {
	allRegionNames, // 获取所在社区列表
} from '@/api/LedgerApi'
import {DEPARTMENT_OPTIONS_ENUM} from '@/define/organization.define'
import {updateLabelTitle} from '@/hooks/useLabels'

const router = useRouter()
const route = useRoute()

// 查看明细弹窗

// 产看查看明细表格的接口
const tableLookUrl: any = ref('')

const tableRefSonUrl: any = ref('')

let dialogLook: any = ref(false)

// 配置表单ref
const ruleFormRef = ref<FormInstance>()

// 业务表
const tableRefBus = ref()

// 详情的数据
let detailForm = ref<any>({})

//  表格单选的那一项
let chooseItem: any = ref({})

// 配置进度表单
let configForm: any = ref({
	rule: '', //插槽字段
	telecomTaskId: '', // 电信任务Id--这条详情的任务id
	// ledgerId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
	ledgerId: '', // 单选的台账id

	timeRangeType: 0, // 单选时间范围
	timeSelf: [], // 默认的
	timeCustom: [], // 自定义的时间范围
	startDateTime: '', // 时间范围开始
	endDateTime: '', // 结束

	regionNames: [], // 区域范围

	computeMode: undefined, // 计算方式 0 1 2 4

	dataVolume: undefined, // 0 数据量（枚举为数据量时）

	dataItem: undefined, // 1数据项
	dataItemValue: undefined, // 1 数据项枚举值

	accumulatedDataItem: undefined, // 2 累加值的数据项
	accumulatedValue: undefined, // 2 累加值

	conditionMode: undefined, // 4百分比计算条件
	totalPercentageValue: undefined, // 4数据量百分比值

	// timeEnd: '', // 结束时间

	comparisonValues: [
		{
			CalculationValueType: 0, // 0 1~4
			DataItem: '', // 数据项
			ComparisonType: '', // 数据项需要计算的类型 0：平均值，1：累加值
		},
	],

	specificValue: undefined, // 比较值计算方式设置的特定值
})

// 添加一项
function addWayCompare() {
	configForm.value.comparisonValues.push({
		CalculationValueType: '',
		DataItem: '',
		ComparisonType: '',
	})
}

let options1: any = ref([
	{
		label: '加(+)',
		value: 1,
	},
	{
		label: '减(-)',
		value: 2,
	},
	{
		label: '乘(x)',
		value: 3,
	},
	{
		label: '除(÷)',
		value: 4,
	},
])
let options2: any = ref([
	{
		label: 'vue',
		value: 'v1',
	},
	{
		label: 'react',
		value: 'r1',
	},
])
let options3: any = ref([
	{
		label: '累加值',
		value: 1,
	},
	{
		label: '平均值',
		value: 0,
	},
])
// 获取村社列表
const regionNamesOptions = ref<any>([])
function getRegionNames(bool: boolean) {
	allRegionNames().then((res) => {
		if (res.data.length > 0) {
			regionNamesOptions.value = res.data
			regionNamesOptions.value.unshift({
				id: 1,
				name: '本镇街',
			})

			if (bool) {
				configForm.value.regionNames.push('本镇街')
			}
		}
	})
}

function chooseStreetItem(item) {
	if (item.id !== 1) {
		// 选某个社区
		configForm.value.regionNames = configForm.value.regionNames.filter(
			(item) => item !== '本镇街'
		)
	} else {
		configForm.value.regionNames = ['本镇街']
	}
}

const tableHeight = ref(0)

const onBlockHeightChanged = (val: number) => {
	tableHeight.value = val - 75 - 120
}

// 任务状态 1-待关联，2-已关联
const FlowTaskStatus = [
	{label: '待关联', value: 1, color: '#ff8515'},
	{label: '已关联', value: 2, color: '#78bf4a'},
]

const taskFormProps = ref([
	{prop: 'targetName', label: '关联目标'},
	{prop: 'taskName', label: '任务名称'},
	{prop: 'block', label: '所属板块'},
	{prop: 'businessId', label: '核心业务'},

	{prop: 'leaders', label: '牵头领导'},
	{prop: 'date', label: '起止日期'},
	{prop: 'chargePersons', label: '责任人'},
	{prop: 'taskCompleteCon', label: '完成条件'},

	{prop: 'childCount', label: '子任务'},
	{prop: 'progress', label: '进度'},
	// 1-待关联，2-已关联
	{prop: 'status', label: '状态'},
	{
		prop: 'taskContent',
		label: '任务描述',
		full: true,
	},
])

const tableRef = ref()

// 业务表表格
const tableColumns: any = ref([
	{prop: 'name', label: '业务表名称', width: 200},
	{prop: 'departmentName', label: '发布部门', width: 320},
	{prop: 'block', label: '所属板块'},
	{prop: 'runway', label: '所属跑道'},
	{prop: 'reminderInterval', label: '更新周期'},
	{prop: 'endTime', label: '截止时间'},
])

const tableButtons = ref([
	{
		code: 'detail',
		label: '查看明细',
		type: 'primary',
	},
])

onMounted(() => {
	getInit()
})

// 配置过
const planProgressConfig = ref<any>({})
// 是否新增
const isAdd = ref(true)
const getInit = async () => {
	// 子任务请求时
	reqParamsSon.parentId = route.query.id
	// 表格数组
	tableRefBus.value.push([])

	const res = await getDetailApi(route.query.id as string)
	if (res.data) {
		detailForm.value = res.data
		console.log('本页数据detailForm--', res.data)
		updateLabelTitle({
			path: router.currentRoute.value.fullPath,
			title: `查看-${detailForm.value.taskName}`,
		})
		if (res.data.ledgerId) {
			getTableData(res.data.ledgerId)

			// 查看数据列表的接口
			tableLookUrl.value = `/api/ledger-service/plan-progress-config/${detailForm.value.planProgressConfig.id}/config-ledger-data`
		}

		// 详情有业务表时
		if (res.data?.ledger) {
			isAdd.value = false
			tableColumns.value = [
				{prop: 'ledgerName', label: '业务表名称', width: 200},
				{prop: 'departmentName', label: '发布部门'},
				{prop: 'belongBlock', label: '所属板块'},

				{prop: 'runwayName', label: '所属跑道'},
				{prop: 'interval', label: '更新周期'},
				{prop: 'weeklyDeadlineDayOfWeek', label: '截止时间'},
				{prop: 'processWay', label: '进度计算方式'},
			]

			chooseItem.value = res.data.ledger

			// 回显配置的下拉数据
			if (detailForm.value?.planProgressConfig) {
				// const data = {
				// 	id: chooseItem.value.id || chooseItem.value.ledgerId,
				// 	computeMode: detailForm.value.planProgressConfig.computeMode[0] == 2 ? 2 : 1,
				// }
				// const res2 = await getLedgerTableFieldsApi(data)
				// options.value = res2.data

				if (
					detailForm.value.planProgressConfig.computeMode[0] === 1 ||
					detailForm.value.planProgressConfig.computeMode[0] === 3 ||
					detailForm.value.planProgressConfig.computeMode[0] === 4
				) {
					const data = {
						id: chooseItem.value.id || chooseItem.value.ledgerId,
						computeMode: detailForm.value.planProgressConfig.computeMode[0],
					}
					const res = await getLedgerTableFieldsApi(data)
					options.value = res.data
				} else if (
					detailForm.value.planProgressConfig.computeMode[0] === 2 ||
					detailForm.value.planProgressConfig.computeMode[0] === 5
				) {
					// 2 累加 5 比较 2 5用的add
					let dataAdd = {
						id: chooseItem.value.id || chooseItem.value.ledgerId,
						computeMode: 2,
					}

					console.log('5555--', detailForm.value.planProgressConfig)

					if (detailForm.value.planProgressConfig.computeMode[0] === 5) {
						dataAdd.computeMode = 5
						console.log('66-', JSON.parse(res.data.planProgressConfig.comparisonValues))

						configForm.value.comparisonValues = JSON.parse(
							res.data.planProgressConfig.comparisonValues
						)

						console.log('77,', configForm.value.comparisonValues)

						configForm.value.specificValue = res.data.planProgressConfig.specificValue
					} else {
						configForm.value.comparisonValues = [
							{
								CalculationValueType: 0, // 0 1~4
								DataItem: '', // 数据项
								ComparisonType: '', // 数据项需要计算的类型 0：平均值，1：累加值
							},
						]
					}

					const resAdd = await getLedgerTableFieldsApi(dataAdd)
					optionsAdd.value = resAdd.data
				}
			} else {
				// 新增
				configForm.value.comparisonValues = [
					{
						CalculationValueType: 0, // 0 1~4
						DataItem: '', // 数据项
						ComparisonType: '', // 数据项需要计算的类型 0：平均值，1：累加值
					},
				]
			}

			const ledger = {
				...res.data.ledger,
				planProgressConfig: res.data.planProgressConfig,
			}

			nextTick(() => {
				tableRefBus.value.push(ledger)
			})
		}

		// 有配置过
		if (res.data.planProgressConfig) {
			getRegionNames(false)
			planProgressConfig.value = res.data.planProgressConfig

			configForm.value.timeRangeType = res.data.planProgressConfig.timeRangeType

			if (configForm.value.timeRangeType == 1) {
				configForm.value.timeCustom.push(
					res.data.planProgressConfig.startDateTime.slice(0, 10),
					res.data.planProgressConfig.endDateTime.slice(0, 10)
				)
			}

			configForm.value.regionNames = res.data.planProgressConfig.regionNames
			// 计算方式
			configForm.value.computeMode = res.data.planProgressConfig.computeMode[0]
			configForm.value.dataVolume = res.data.planProgressConfig.dataVolume

			configForm.value.dataItem = res.data.planProgressConfig.dataItem
			configForm.value.dataItemValue = res.data.planProgressConfig.dataItemValue

			configForm.value.accumulatedValue = res.data.planProgressConfig.accumulatedValue
			configForm.value.accumulatedDataItem = res.data.planProgressConfig.accumulatedDataItem

			configForm.value.conditionMode = res.data.planProgressConfig.conditionMode
			configForm.value.totalPercentageValue = res.data.planProgressConfig.totalPercentageValue

			if (
				res.data.planProgressConfig.regionNames === null ||
				res.data.planProgressConfig.regionNames.length === 0
			) {
				configForm.value.regionNames = ['本镇街']
			}

			//重要 判断id
			configForm.value.id = res.data.planProgressConfig.id
		}

		// configForm 设置
		configForm.value.telecomTaskId = res.data.id
		configForm.value.ledgerId = res.data.ledgerId

		// 进页面对 默认时间范围 赋值
		if (res.data.endTime) {
			configForm.value.timeSelf.push(res.data.startTime.slice(0, 10))
			configForm.value.timeSelf.push(res.data.endTime.slice(0, 10))
		} else {
			getRegionNames(true)
		}
		configForm.value.timeSelf = []

		// configForm 设置
		configForm.value.telecomTaskId = res.data.id
		configForm.value.ledgerId = res.data.ledgerId

		// 进页面对 默认时间范围 赋值
		if (res.data.endTime) {
			configForm.value.timeSelf.push(res.data.startTime.slice(0, 10))
			configForm.value.timeSelf.push(res.data.endTime.slice(0, 10))
		}
	}
}

// 关联弹窗
const viewModel = ref(false)
const dialogHeight = ref(0)
const heightChanged = (val: number) => {
	dialogHeight.value = val
}

// 搜索表单star

// 发布部门
const cascadeValue = ref([])

const oneSelectProps = ref({
	lazy: true,
	url: '/api/platform/department/generalDepartmentQuery',
	method: 'GET',
	multiple: false,
	checkStrictly: true,
	beforeCompleted: (node: any, data: any, query: any) => {
		const first = DEPARTMENT_OPTIONS_ENUM.map((f: any) => f.label)
		let grade = 2
		let parentId = ''

		if (first.includes(node.label)) {
			grade = node.value
		} else {
			grade = node.pathNodes[0].value
			parentId = node.value
		}

		query = {
			keyWord: '',
			grade,
			parentId,
		}
		return {data, query}
	},
})

const searchForm = ref<any>({})
const currentGrade: any = ref(null)

const onCascadeElChange = (val: any, nodes: any) => {
	currentGrade.value = val[0]
	if (val.length === 1) {
		searchForm.value.DepartmentId = ''
	} else {
		searchForm.value.DepartmentId = val[val.length - 1]
	}
}

const formItems = ref([
	{
		prop: 'filter',
		type: 'text',
		placeholder: '请输入业务表名称',
	},
	{
		prop: 'DepartmentId',
		type: 'select',
		placeholder: '请选择发布部门',
	},
	{
		prop: 'runway',
		type: 'select',
		placeholder: '请选择所属板块',
		options: [
			// 1-待关联，2-已关联
			{label: '党的建设', value: '党的建设'},
			{label: '经济发展', value: '经济发展'},
			{label: '民生服务', value: '民生服务'},
			{label: '平安法治', value: '平安法治'},
		],
	},
])

const reqParams = reactive({
	filter: '', // 党的建设、经济发展、民生服务、平安法治
	DepartmentId: '', // 任务名称
	runway: '', // 电信任务状态：1-待关联，2-已关联

	isOnline: true,
	skipCount: 0,
	maxResultCount: 10,
})

const onSearch = (type?: string) => {
	if (type === 'search') {
		reqParams.filter = searchForm.value?.filter
		reqParams.DepartmentId = searchForm.value?.DepartmentId
		reqParams.runway = searchForm.value?.runway
	}

	if (type === 'clear') {
		reqParams.filter = ''
		reqParams.DepartmentId = ''
		reqParams.runway = ''
	}
}
// 搜索表单end

// 关联弹窗的表格star
const tableRefLink = ref()
const tableHeightLink = ref(0)
const onBlockHeightChangedLink = (height: any) => {
	tableHeightLink.value = height - 75 // Block 内容高度 - 分页高度
}

const tableColumnsLink = ref([
	{prop: 'name', label: '业务表名称', width: 200},
	{prop: 'departmentName', label: '发布部门'}, //
	{prop: 'block', label: '所属板块'},
	{prop: 'runway', label: '所属跑道'},
	{prop: 'reminderInterval', label: '数据更新周期'}, //
	{prop: 'endTime', label: '截止时间'}, //
])

const paginationLink = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const onTableCompleted = () => {
	paginationLink.total = tableRef.value.getTotal()
}
const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		paginationLink.page = val
		reqParams.skipCount = (val - 1) * paginationLink.size
	} else {
		paginationLink.size = val
		reqParams.maxResultCount = paginationLink.size
	}
}

// 表格单选
const handleCurrentChange = (val: any) => {
	chooseItem.value = val
	viewModel.value = false
	// configForm.value.ledgerId = '3fa85f64-5717-4562-b3fc-2c963f66afa6' || val.id
	configForm.value.ledgerId = val.id
}

watch(
	chooseItem,
	(val: any) => {
		// tableData.value.push(val)
		if (chooseItem.value && chooseItem.value.id) {
			tableRefBus.value.push(val)
		}
	},
	{deep: true}
)

// 配置弹窗
const openConfigDialog = ref(false)

//配置表单
// {
//   "ledgerId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
//   "computeMode": 0,
//   "dataVolume": 0,
//   "dataItem": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
//   "dataItemValue": "string",
//   "accumulatedValue": 0,
//   "startDateTime": "2024-08-13T02:51:57.975Z",
//   "endDateTime": "2024-08-13T02:51:57.975Z"
// }

const computeModeOptions = [
	{label: '按数据量计算进度', value: 0},
	{label: '按枚举值计算进度', value: 1},
	{label: '按累加值计算进度', value: 2},
	{label: '按百分比计算进度', value: 4},
	{label: '按筛数法计算进度', value: 3},

	{label: '按比较值计算进度', value: 5},
]
// 计算类型 下拉选择
const conditionModeOptions = [
	{label: '等于=', value: 1},
	{label: '大于>', value: 2},
	{label: '大于等于>=', value: 3},
	{label: '小于<', value: 4},
	{label: '小于等于≤', value: 5},
]

const configFormItems = ref([
	{
		prop: 'timeRangeType',
		label: '时间范围：',
	},
	{
		prop: 'timeCustom',
		label: '',
		formShow: true,
	},
	{
		prop: 'areaIds',
		label: '区域范围：',
	},
	{
		prop: 'computeMode',
		label: '计算方式：',
	},
	{
		prop: 'rule',
		label: '计算规则：',
	},
])

function computeModeChange(e: any) {
	// 选后计算规则重置
	configForm.value.dataItem = undefined
	configForm.value.dataItemValue = undefined
	configForm.value.accumulatedDataItem = undefined
	configForm.value.accumulatedValue = undefined

	configForm.value.conditionMode = undefined
	configForm.value.specificValue = undefined
}
// 计算规则第一个下拉数据
let options: any = ref([])

// 计算规则第二个下拉数据
let optionsTwo: any = ref([])
// 累加值的 下拉数据
let optionsAdd: any = ref([])

// 下拉选择 计算方式
watch(
	() => configForm.value.computeMode,
	async (val) => {
		console.log('下拉选择', val)
		// 须调接口 1 枚举 2 累加 4 百分比 3 筛数 5 比较值
		if (chooseItem.value.id || chooseItem.value.ledgerId) {
			if (val === 1 || val === 3 || val === 4) {
				const data = {
					id: chooseItem.value.id || chooseItem.value.ledgerId,
					computeMode: val,
				}
				const res = await getLedgerTableFieldsApi(data)
				options.value = res.data
			} else if (val === 2 || val === 5) {
				// 2 累加 5 比较 2 5用的add
				let dataAdd = {
					id: chooseItem.value.id || chooseItem.value.ledgerId,
					computeMode: 2,
				}

				if (val === 5) {
					dataAdd.computeMode = 5
				}

				const resAdd = await getLedgerTableFieldsApi(dataAdd)
				optionsAdd.value = resAdd.data
			}
		}
	},
	{}
)
watch(
	// dataItem 当数量累计达到的下拉值
	() => configForm.value.dataItem,
	(val) => {
		//
		const arr = options.value.filter((item: any) => item.id === val)
		if (arr.length && arr[0].options && arr[0].options.length > 0) {
			arr[0].options.map((item: any) => {
				optionsTwo.value.push({
					value: item,
					label: item,
				})
			})
		}
	}
)

// 提交配置弹窗
async function submitConfig(formEl: any) {
	// const res = await setPlanProgressConfigApi(configForm.value)
	// openConfigDialog.value = false

	if (!formEl) return
	await formEl.validate((valid: any, fields: any) => {
		if (valid) {
			console.log('submit!')
			openConfigDialog.value = false
		} else {
			console.log('error submit!', fields)
		}
	})
}
// 顶部点击 保存按钮
async function saveConfig() {
	console.log('顶部点击 保存按钮detailForm', detailForm)
	console.log('顶部点击 保存按钮configForm', configForm)
	console.log('顶部点击 保存按钮chooseItem', chooseItem)

	// 有数据，编辑
	if (detailForm.value.ledger) {
		//  编辑了已有配置
		if (detailForm.value?.planProgressConfig?.id) {
			// 配置
			await updatePlanProgressConfigApi(
				configForm.value,
				detailForm.value.planProgressConfig.id
			)

			// 保存
			// await savePlanProgressConfigApi(detailForm.value.id, {
			// 	ledgerId: chooseItem.value.id,
			// 	// ledgerId: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
			// })

			ElMessage({
				type: 'success',
				message: '保存成功~',
			})
		} else if (configForm.value.computeMode >= 0) {
			// 删除配置后新增了配置
			// 配置
			const res = await setPlanProgressConfigApi(configForm.value)
			console.log('新增配置进度计算方式', res)
			ElMessage({
				type: 'success',
				message: '保存成功~',
			})
		} else {
			// 仅关联 没有配置
			// 保存
			// await savePlanProgressConfigApi(detailForm.value.id, {
			// 	ledgerId: chooseItem.value.id,
			// 	// ledgerId: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
			// })
			// return ElMessage({
			// 	type: 'success',
			// 	message: '保存成功~',
			// })
		}
		return
	}
	// 没有数据 是新增
	if (!chooseItem.value || !chooseItem.value.id) {
		return ElMessage({
			type: 'warning',
			message: '请先关联~',
		})
	}
	// 关联且配置了
	if (configForm.value.computeMode >= 0) {
		// 配置
		const res = await setPlanProgressConfigApi(configForm.value)
		console.log('新增配置进度计算方式', res)

		// 保存
		await savePlanProgressConfigApi(detailForm.value.id, {
			ledgerId: chooseItem.value.id,
			// ledgerId: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
		})

		ElMessage({
			type: 'success',
			message: '保存成功~',
		})
	} else {
		// 仅关联
		// 保存
		await savePlanProgressConfigApi(detailForm.value.id, {
			ledgerId: chooseItem.value.id,
			// ledgerId: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
		})
		return ElMessage({
			type: 'success',
			message: '保存成功~',
		})
	}
}

// 顶部点击 确定关联按钮
const onConfirmLinkBtnClick = () => {
	console.log('chooseItem.value', chooseItem.value)

	if (!chooseItem.value || !chooseItem.value.id) {
		return ElMessage({
			type: 'warning',
			message: '请先关联~',
		})
	} else if (!configForm.value || !(configForm.value.computeMode >= 0)) {
		return ElMessage({
			type: 'warning',
			message: '请配置进度计算方式~',
		})
	}
	ElMessageBox.confirm('确认关联后不可修改业务表及进度计算方式，是否确认关联？', '提示消息', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			// 配置
			const res = await setPlanProgressConfigApi(configForm.value)
			console.log('新增配置进度计算方式', res)

			// 保存
			await savePlanProgressConfigApi(detailForm.value.id, {
				ledgerId: chooseItem.value.id,
				// ledgerId: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
			})

			// 关联
			await makeSurePlanProgressConfigApi(detailForm.value.id, {
				status: 2, // 1待关联 2 已关联
			})
			ElMessage({
				type: 'success',
				message: '关联成功~',
			})
			setTimeout(() => {
				router.back()
			}, 800)
		})
		.catch(() => {})
}

// 表格end

// 子任务列表弹窗
let dialogSon: any = ref(false)

// 搜索表单star
const searchFormSon = ref<any>({})

const formItemsSon = ref([
	{
		prop: 'TaskName',
		type: 'text',
		placeholder: '请输入任务名称',
	},
	{
		prop: 'Status',
		type: 'select',
		placeholder: '请选择任务状态',
		options: [
			// 1-待关联，2-已关联
			{label: '待关联', value: 1},
			{label: '已关联', value: 2},
		],
	},
])

const reqParamsSon = reactive<any>({
	TaskName: '', // 任务名称
	Status: '', // 电信任务状态：1-待关联，2-已关联
	parentId: '',
	skipCount: 0,
	maxResultCount: 10,
})

const onSearchSon = (type?: string) => {
	if (type === 'search') {
		reqParamsSon.TaskName = searchFormSon.value?.TaskName
		reqParamsSon.Status = searchFormSon.value?.Status
	}

	if (type === 'clear') {
		reqParamsSon.TaskName = ''
		reqParamsSon.Status = ''
	}
}

// 搜索表单end

// 子任务表格
const tableRefSon = ref()

const tableColumnsSon = ref([
	{prop: 'taskType', label: '任务类别'},
	{prop: 'taskName', label: '任务名称'},
	{prop: 'endTime', label: '完成截止时间'}, //
	{prop: 'leaders', label: '创建人'}, //
	{prop: 'ledgerName', label: '关联业务表'},
	{prop: 'status', label: '关联状态'}, // 1-待关联，2-已关联
	{prop: 'progress', label: '进度'},
])

const tableButtonsSon = ref([
	{
		code: 'detail',
		label: '查看',
		show: `row.status ===2`,
	},
	{
		code: 'link',
		label: '关联业务表',
		type: 'primary',
		show: `row.status ===1`,
	},
])

const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const onTableCompletedSon = () => {
	pagination.total = tableRefSon.value.getTotal()
}

const paginationSon = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const onPaginationChangeSon = (val: any, type: any) => {
	if (type == 'page') {
		paginationSon.page = val
		reqParamsSon.skipCount = (val - 1) * paginationSon.size
	} else {
		paginationSon.size = val
		reqParamsSon.maxResultCount = paginationSon.size
	}
}

function openDialogSon() {
	dialogSon.value = true
}

const beforeDelete = async () => {
	// 表格的那项
	console.log(
		'tableRefBus.value',
		tableRefBus.value.tableData,
		detailForm.value?.planProgressConfig
	)
	// if (detailForm.value?.planProgressConfig?.id) {
	// 	await deletePlanProgressConfigApi(detailForm.value?.planProgressConfig?.id)
	// }

	// 移除台账
	await deleteLedgerApi(detailForm.value?.planProgressConfig?.id)

	// tableRefBus.value.tableData.pop()
	// 保存的表格这项
	chooseItem.value = {}
	console.log('移除chooseItem', chooseItem)
	console.log('移除configForm', configForm)

	configForm.value.computeMode = undefined
	configForm.value.dataVolume = undefined

	configForm.value.dataItem = ''
	configForm.value.dataItemValue = ''
	configForm.value.accumulatedValue = undefined
}

// 查看明细弹窗表格 star

// 点击业务表操作按钮
const handleClickTableButton = ({btn, row, index}: any) => {
	if (btn.code === 'detail') {
		dialogLook.value = true
	} else if (btn.code === 'config') {
		openConfigDialog.value = true
	}
}

const tableRefLook = ref()

const reqParamsLook = reactive({
	skipCount: 0,
	maxResultCount: 10,
})

const paginationLook = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const onPaginationChangeLook = (val: any, type: any) => {
	if (type == 'page') {
		paginationLook.page = val
		reqParamsLook.skipCount = (val - 1) * paginationLook.size
	} else {
		paginationLook.size = val
		reqParamsLook.maxResultCount = paginationLook.size
	}
}

const onTableCompletedLook = () => {
	nextTick(() => {
		paginationLook.total = tableRefLook.value.getTotal()
	})
}

const tableColumnsLook: any = ref([])

// 外层
const outTable: any = ref([])

async function getTableData(id) {
	console.log('获取表头------')

	const res = await headerLedgerApi(id)
	// const res = await headerLedgerApi('3a1466fc-c37a-137f-a904-dbc8f451951e')

	console.log('获取表头------', res)

	const obj = res.data.tableInfo
	const {fields, tableFieldGroups} = obj
	console.log('res---', fields, tableFieldGroups)

	// 多级
	if (tableFieldGroups.length > 0) {
		outTable.value.push(...tableFieldGroups)

		fields.map((e: any) => {
			e.tableFieldGroupId ? '' : outTable.value.push(e)
		})

		outTable.value = outTable.value.sort((a: any, b: any) => a.sort - b.sort)

		outTable.value.map((item: any) => {
			if (item.tableFields && item.tableFields.length > 0) {
				item.tableFields = item.tableFields.sort((a: any, b: any) => a.sort - b.sort)
			}
		})

		outTable.value.map((e: any) => {
			if (e.displayName) {
				tableColumnsLook.value.push({
					prop: e.name,
					label: e.displayName || undefined,
				})
			} else {
				let children: any = []

				e.tableFields.map((item: any) => {
					// console.log('1122item', item, tableColumnsLook)
					children.push({
						prop: item.name,
						label: item.displayName,
					})
				})
				tableColumnsLook.value.push({
					label: e.name,
					children,
				})
			}
		})
		// tableColumnsLook.value
		console.log('tableColumns.value---', tableColumnsLook.value)
	} else {
		// 普通表
		fields.map((item: any) => {
			tableColumnsLook.value.push({
				// label: e.name,
				label: item.displayName,
				prop: item.name,
			})
		})
	}
}

// 查看示例弹窗
const openExampleDialog = ref(false)

// 点击表格的操作按钮
const onTableButtonClickSon = ({btn, row, index}: any) => {
	let path = ''
	if (btn.code === 'detail') {
		path = '/tasklink/detailSon'
	} else if (btn.code === 'link') {
		path = '/tasklink/linkSon'
	}
	dialogSon.value = false
	router.push({
		path,
		query: {
			id: row.id,
			type: btn.code,
		},
	})
}
// 更改子任务配置
const openConfigSon = (id: string) => {
	ElMessageBox.confirm('更改配置后已计算的任务进度将会重新计算，是否更改？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			dialogSon.value = false
			router.push({
				path: '/tasklink/linkSon',
				query: {
					id,
				},
			})
		})
		.catch(() => {})
}
// 取消子任务关联
const openCancelSon = (id: string) => {
	ElMessageBox.confirm(
		'取消关联后已计算的任务进度将会清空，且状态变为待关联，是否取消？',
		'提示',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(async () => {
			// 绑定删除任务绑定台账和台账配置台账 taskId,
			await deleteLedgerApi(id)
			await makeSurePlanProgressConfigApi(id, {
				status: 1, // 1 取消关联 2 关联 -- id是页面详情的id
			})
			// 更新数据
			tableRefSon.value.reload()
			ElMessage({
				type: 'success',
				message: '操作成功',
			})
		})
		.catch(() => {
			ElMessage({
				type: 'info',
				message: '操作失败',
			})
		})
}
</script>
<template>
	<div class="task-link-detail">
		<Block :enableExpandContent="false" title="任务信息" @height-changed="onBlockHeightChanged">
			<template #topRight>
				<template>
					<el-button size="small" type="primary" @click="onConfirmLinkBtnClick"
						>确认关联</el-button
					>
					<el-button size="small" type="primary" @click="saveConfig">保存</el-button>
				</template>
			</template>

			<Form v-model="detailForm" :props="taskFormProps" :grid="true" :column-count="4">
				<!-- 所属板块 -->
				<template #form-block="{form}">
					<div v-if="form.businessName && form.businessName.indexOf('/') > 0">
						<span>{{ form.businessName.split('/')[0] }}</span>
					</div>
					<div v-else>-</div>
				</template>
				<!-- 核心业务 -->
				<template #form-businessId="{form}">
					<div v-if="form.businessName && form.businessName.indexOf('/') > 0">
						<span>{{ form.businessName.split('/')[1] }}</span>
						<span v-if="form.businessName.split('/')[2]"
							>/{{ form.businessName.split('/')[2] }}</span
						>
					</div>
					<div v-else>-</div>
				</template>

				<!-- 起止日期 -->
				<template #form-date="{form}">
					<div v-if="form.startTime">
						<el-tooltip placement="top" :content="form.startTime + '-' + form.endTime">
							<span>{{ form.startTime }}~{{ form.endTime }}</span>
						</el-tooltip>
					</div>
				</template>
				<!-- 责任人 -->
				<template #form-chargePersons="{form}">
					<!-- chargePersonName -->
					<div v-if="form.chargePersons && form.chargePersons.length">
						<span v-for="(item, idx) in form.chargePersons"
							>{{ item.chargePersonName }}
							<span
								v-if="
									form.chargePersons.length > 1 &&
									idx < form.chargePersons.length - 1
								"
								>、</span
							>
						</span>
					</div>
					<div v-else>-</div>
				</template>

				<!-- 完成条件  任务完成条件(1任意责任人完成即可 0全部责任人均需完成) -->
				<template #form-taskCompleteCon="{form}">
					<span v-if="form.taskCompleteCon === '1'">任意责任人完成即可</span>
					<span v-else-if="form.taskCompleteCon === '0'">全部责任人均需完成</span>
				</template>

				<!-- 子任务数量 -->
				<template #form-childCount="{form}">
					<div @click="openDialogSon">
						<u class="link-click">{{ form.childCount }}</u>
					</div>
				</template>

				<!-- 进度 -->
				<template #form-progress="{form}">
					<div>{{ form.progress }}%</div>
				</template>
				<!-- 状态 -->
				<!-- {label: '待关联', value: 1, color: '#ff8515'}, -->
				<!-- {label: '已关联', value: 2, color: '#78bf4a'}, {{ row.status }}--  -->
				<template #form-status="{form}">
					<div v-if="form.status" flex="~" items-center>
						<span
							mr-5px
							inline-block
							w-10px
							h-10px
							rounded-full
							:style="{
								backgroundColor: FlowTaskStatus.filter(
									(v) => v.value === form.status
								)[0].color,
							}"
						></span>
						<!-- v.value === form.status) -->
						<span
							:style="{
								color: FlowTaskStatus.filter((v) => v.value === form.status)[0]
									.color,
							}"
						>
							{{ FlowTaskStatus.filter((v) => v.value === form.status)[0].label }}
						</span>
					</div>
				</template>
			</Form>
		</Block>

		<Block
			:enableExpandContent="false"
			title="业务表"
			:enable-back-button="false"
			:enable-close-button="false"
		>
			<TableV2
				ref="tableRefBus"
				:auto-height="true"
				:columns="tableColumns"
				:buttons="tableButtons"
				:enable-toolbar="false"
				:enable-edit="false"
				:enable-delete="false"
				deleteText="移除"
				@beforeDelete="beforeDelete"
				@click-button="handleClickTableButton"
			>
				<!-- 所属跑道 -->
				<template #runwayName="{row}">
					<div v-if="detailForm.ledger && detailForm.ledger.ledgerRunway">
						<span
							>{{ detailForm.ledger.ledgerRunway.parent.businessName }}-{{
								detailForm.ledger.ledgerRunway.businessName
							}}</span
						>
					</div>
					<div v-else>-</div>
				</template>
				<!-- 详情时的 -->
				<template #interval="{row}" v-if="detailForm.ledger">
					<span v-if="row.interval === 0">不提醒</span>
					<span v-else-if="row.interval === 1">每天</span>
					<span v-else-if="row.interval === 2">每周</span>
					<span v-else-if="row.interval === 3">每月</span>
					<span v-else-if="row.interval === 4">每季度</span>
					<span v-else-if="row.interval === 5">每年</span>
				</template>
				<template #weeklyDeadlineDayOfWeek="{row}" v-if="detailForm.ledger">
					<span v-if="row.weeklyDeadlineDayOfWeek === 1">周一</span>
					<span v-else-if="row.weeklyDeadlineDayOfWeek === 2">周二</span>
					<span v-else-if="row.weeklyDeadlineDayOfWeek === 3">周三</span>
					<span v-else-if="row.weeklyDeadlineDayOfWeek === 4">周四</span>
					<span v-else-if="row.weeklyDeadlineDayOfWeek === 5">周五</span>
					<span v-else-if="row.weeklyDeadlineDayOfWeek === 6">周六</span>
					<span v-else-if="row.weeklyDeadlineDayOfWeek === 7">周日</span>
				</template>

				<template #processWay="{row}" v-if="1">
					<div @click="handleClickTableButton({btn: {code: 'config'}, row})">
						<span
							class="link-click"
							v-if="row.planProgressConfig.computeMode.includes(0)"
							>按数据量计算进度、
						</span>
						<span
							class="link-click"
							v-if="row.planProgressConfig.computeMode.includes(1)"
							>按枚举值计算进度、
						</span>
						<span
							class="link-click"
							v-if="row.planProgressConfig.computeMode.includes(2)"
							>按累加值计算进度
						</span>
						<span
							class="link-click"
							v-if="row.planProgressConfig.computeMode.includes(4)"
							>按百分比计算进度
						</span>
						<span
							class="link-click"
							v-if="row.planProgressConfig.computeMode.includes(3)"
							>按筛数法计算进度
						</span>

						<span
							class="link-click"
							v-if="row.planProgressConfig.computeMode.includes(5)"
							>按比较值计算进度
						</span>
					</div>
				</template>

				<template #empty>
					<div
						style="
							display: flex;
							justify-content: center;
							align-items: center;
							margin-top: -20px;
						"
					>
						暂无业务表，请
						<div class="link-click" @click="viewModel = true">点击关联</div>
					</div>
				</template>
			</TableV2>
		</Block>

		<!-- 配置弹窗 -->
		<Dialog
			v-model="openConfigDialog"
			title="配置进度计算方式"
			:enableButton="false"
			:width="'70%'"
			:enableConfirm="true"
			@heightChanged="heightChanged"
			@click-close="openConfigDialog = false"
		>
			<Form
				v-model="configForm"
				ref="ruleFormRef"
				:data="configFormItems"
				column-count="1"
				button-vertical="flowing"
				:enableReset="false"
				:enableClear="false"
				:enableButton="false"
				:inline="false"
				label-width="120"
			>
				<!-- 时间范围 radio -->
				<template #form-timeRangeType="{form}">
					<el-form-item
						prop="timeRangeType"
						label=" "
						:label-width="1"
						:rules="[
							{
								required: true,
								message: '请选择',
								trigger: 'blur',
							},
						]"
						style="margin: 0 16px"
					>
						<el-radio-group :disabled="true" v-model="configForm.timeRangeType">
							<el-radio :value="0" size="large">任务起止时间</el-radio>
							<el-radio :value="1" size="large">自定义时间范围</el-radio>
						</el-radio-group>
					</el-form-item>
				</template>

				<!-- 选日期 时间范围 -->
				<template #form-timeCustom="{form}">
					<el-form-item
						prop="timeCustom"
						label=" "
						:label-width="1"
						style="margin: 0 16px"
					>
						<el-date-picker
							style="max-width: 400px"
							v-if="!configForm.timeRangeType"
							:disabled="true"
							v-model="configForm.timeSelf"
							type="daterange"
							format="YYYY-MM-DD"
							value-format="YYYY-MM-DD"
							range-separator="至"
							start-placeholder="开始时间"
							end-placeholder="结束时间"
						/>
						<el-date-picker
							style="max-width: 400px"
							v-else
							v-model="configForm.timeCustom"
							type="daterange"
							format="YYYY-MM-DD"
							value-format="YYYY-MM-DD"
							range-separator="至"
							start-placeholder="开始时间"
							end-placeholder="结束时间"
						/>
					</el-form-item>
				</template>

				<!-- 区域范围 -->
				<template #form-areaIds="{form}">
					<el-form-item
						prop="areaIds"
						label=" "
						:label-width="1"
						style="min-width: 252px; margin: 0 16px"
					>
						<el-select
							disabled
							style="max-width: 400px"
							v-model="configForm.regionNames"
							placeholder="请选择"
							multiple
							collapse-tags
							collapse-tags-tooltip
							:max-collapse-tags="3"
						>
							<el-option
								v-for="item in regionNamesOptions"
								:key="item.id"
								:label="item.name"
								:value="item.name"
								@click="chooseStreetItem(item)"
							/>
						</el-select>
					</el-form-item>
				</template>

				<!-- 计算方式 -->
				<template #form-computeMode="{form}">
					<div>
						<div style="display: flex; align-items: center">
							<el-form-item
								prop="computeMode"
								label=" "
								:label-width="1"
								:rules="[
									{
										required: true,
										message: '请选择',
										trigger: 'blur',
									},
								]"
								style="min-width: 252px; margin: 0 16px"
							>
								<el-select
									disabled
									v-model="configForm.computeMode"
									placeholder="请选择"
									@change="computeModeChange"
								>
									<el-option
										v-for="item in computeModeOptions"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							<el-button type="primary" @click="openExampleDialog = true"
								>查看示例</el-button
							>
						</div>
					</div>
				</template>
				<!-- 计算规则 -->
				<template #form-rule="{form}">
					<div v-if="configForm.computeMode != undefined">
						<!-- 0 数据量 -->
						<div
							v-if="configForm.computeMode === 0"
							style="display: flex; align-items: start"
						>
							当数量累计达到
							<el-form-item
								prop="dataVolume"
								label=" "
								:label-width="6"
								style="width: 200px; height: 60px; margin-left: 16px"
								:rules="[
									{
										required: true,
										message: '请输入',
										trigger: 'blur',
									},
								]"
							>
								<el-input
									disabled
									v-model.number="configForm.dataVolume"
									type="number"
									style="width: 180px"
									placeholder="请输入数字"
								/>
							</el-form-item>
							条时，则完成任务
						</div>

						<!-- 1 枚举值 -->
						<div
							v-else-if="configForm.computeMode === 1"
							style="display: flex; align-items: start"
						>
							<div>当</div>
							<el-form-item
								prop="dataItem"
								label=" "
								:label-width="1"
								style="width: 200px; height: 60px; margin-left: 16px"
								:rules="[
									{
										required: true,
										message: '请输入',
										trigger: 'blur',
									},
								]"
							>
								<el-select
									disabled
									v-model="configForm.dataItem"
									placeholder="请选择数据项"
									@change="changeDataItem"
								>
									<el-option
										v-for="item in options"
										:key="item.id"
										:label="item.displayName"
										:value="item.id"
									/>
								</el-select>
							</el-form-item>
							<div style="margin-left: 8px">全为</div>
							<el-form-item
								prop="dataItemValue"
								label=" "
								:label-width="6"
								style="width: 200px; height: 60px; margin-left: 16px"
								:rules="[
									{
										required: true,
										message: '请输入',
										trigger: 'blur',
									},
								]"
							>
								<el-select
									disabled
									v-model="configForm.dataItemValue"
									placeholder="请选择枚举值"
									style="width: 180px"
								>
									<el-option
										v-for="item in optionsTwo"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
							时，则完成任务
						</div>

						<!-- 2 累加值 -->
						<div
							v-else-if="configForm.computeMode === 2"
							style="display: flex; align-items: start"
						>
							当
							<el-form-item
								prop="accumulatedDataItem"
								label=" "
								:label-width="1"
								style="width: 200px; height: 60px; margin-left: 16px"
								:rules="[
									{
										required: true,
										message: '请选择',
										trigger: 'change',
									},
								]"
							>
								<el-select
									disabled
									v-model="configForm.accumulatedDataItem"
									placeholder="请选择数据项"
									style="width: 180px"
									@change="changeDataItemTwo"
								>
									<el-option
										v-for="item in optionsAdd"
										:key="item.id"
										:label="item.displayName"
										:value="item.id"
									/>
								</el-select>
							</el-form-item>
							<div style="margin-left: 8px">累加值达到</div>

							<el-form-item
								prop="accumulatedValue"
								label=" "
								:label-width="6"
								style="width: 200px; height: 60px; margin-left: 16px"
								:rules="[
									{
										required: true,
										message: '请输入',
										trigger: 'change',
									},
								]"
							>
								<el-input
									disabled
									v-model.number="configForm.accumulatedValue"
									type="number"
									style="width: 180px"
									placeholder="请输入数字"
								/>
							</el-form-item>
							时，则完成任务
						</div>

						<!-- 4 百分比 -->
						<div v-else-if="configForm.computeMode === 4">
							<div style="display: flex; align-items: start">
								<div>当</div>
								<el-form-item
									prop="dataItem"
									label=" "
									:label-width="1"
									style="width: 200px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请输入',
											trigger: 'blur',
										},
									]"
								>
									<el-select
										disabled
										v-model="configForm.dataItem"
										placeholder="请选择数据项"
										@change="changeDataItem"
									>
										<el-option
											v-for="item in options"
											:key="item.id"
											:label="item.displayName"
											:value="item.id"
										/>
									</el-select>
								</el-form-item>
								<div style="margin-left: 8px">枚举值为</div>
								<el-form-item
									prop="dataItemValue"
									label=" "
									:label-width="6"
									style="width: 200px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请输入',
											trigger: 'blur',
										},
									]"
								>
									<el-select
										disabled
										v-model="configForm.dataItemValue"
										placeholder="请选择枚举值"
										style="width: 180px"
									>
										<el-option
											v-for="item in optionsTwo"
											:key="item.value"
											:label="item.label"
											:value="item.value"
										/>
									</el-select>
								</el-form-item>
							</div>

							<div style="display: flex; align-items: start">
								的数据量
								<el-form-item
									prop="dataItem"
									label=" "
									:label-width="1"
									style="width: 200px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请选择',
											trigger: 'change',
										},
									]"
								>
									<el-select
										disabled
										v-model="configForm.conditionMode"
										placeholder="请选择计算类型"
										style="width: 180px"
									>
										<el-option
											v-for="item in conditionModeOptions"
											:key="item.value"
											:label="item.label"
											:value="item.value"
										/>
									</el-select>
								</el-form-item>
								<div style="margin-left: 8px">总数据量的</div>

								<el-form-item
									prop="totalPercentageValue"
									label=" "
									:label-width="6"
									style="width: 200px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请输入',
											trigger: 'change',
										},
									]"
								>
									<el-input
										disabled
										v-model.number="configForm.totalPercentageValue"
										type="number"
										style="width: 180px"
										placeholder="请输入数字"
									/> </el-form-item
								>% 时，则完成任务
							</div>
						</div>

						<!-- 3 按筛数法计算 -->
						<div v-else-if="configForm.computeMode === 3">
							<div style="display: flex; align-items: start">
								<div>当</div>
								<el-form-item
									prop="dataItem"
									label=" "
									:label-width="1"
									style="width: 200px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请输入',
											trigger: 'blur',
										},
									]"
								>
									<el-select
										disabled
										v-model="configForm.dataItem"
										placeholder="请选择数据项"
										@change="changeDataItem"
									>
										<el-option
											v-for="item in options"
											:key="item.id"
											:label="item.displayName"
											:value="item.id"
										/>
									</el-select>
								</el-form-item>
								<div style="margin-left: 8px">选项为</div>
								<el-form-item
									prop="dataItemValue"
									label=" "
									:label-width="6"
									style="width: 400px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请输入',
											trigger: 'blur',
										},
									]"
								>
									<el-select
										disabled
										v-model="configForm.dataItemValue"
										placeholder="请选择枚举值"
										style="width: 180px"
									>
										<el-option
											v-for="item in optionsTwo"
											:key="item.value"
											:label="item.label"
											:value="item.value"
										/>
									</el-select>
									<div style="margin-left: 8px">的数据量累计达到</div>
								</el-form-item>
							</div>

							<div style="display: flex; align-items: start">
								<el-form-item
									prop="dataVolume"
									label=" "
									:label-width="6"
									style="width: 200px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请输入',
											trigger: 'change',
										},
									]"
								>
									<el-input
										disabled
										v-model.number="configForm.dataVolume"
										type="number"
										style="width: 180px"
										placeholder="请输入数字"
									/> </el-form-item
								>条时，则完成任务
							</div>
						</div>

						<!-- 5 比较值 -->
						<div v-else-if="configForm.computeMode === 5">
							<!-- 2行 -->
							<div
								v-if="
									configForm.comparisonValues &&
									configForm.comparisonValues.length
								"
							>
								<div
									style="display: flex; align-items: start"
									v-for="(item, idx) in configForm.comparisonValues"
									:key="idx"
								>
									<div v-if="idx === 0">当</div>
									<el-form-item
										v-if="idx > 0"
										prop=""
										label=" "
										:label-width="1"
										style="width: 200px; height: 60px; margin-left: 16px"
										:rules="[
											{
												required: true,
												message: '请选择',
												trigger: 'blur',
											},
										]"
									>
										<el-select
											disabled
											v-model="
												configForm.comparisonValues[idx]
													.CalculationValueType
											"
											placeholder="请选择数据项"
											@change="changeDataItem"
										>
											<el-option
												v-for="item in options1"
												:key="item.value"
												:label="item.label"
												:value="item.value"
											/>
										</el-select>
									</el-form-item>

									<el-form-item
										prop=""
										label=" "
										:label-width="1"
										style="width: 200px; height: 60px; margin-left: 16px"
										:rules="[
											{
												required: true,
												message: '请选择',
												trigger: 'blur',
											},
										]"
									>
										<el-select
											disabled
											v-model="configForm.comparisonValues[idx].DataItem"
											placeholder="请选择数据项"
											@change="changeDataItem"
										>
											<el-option
												v-for="item in optionsAdd"
												:key="item.id"
												:label="item.displayName"
												:value="item.id"
											/>
										</el-select>
									</el-form-item>

									<div style="margin-left: 8px">的</div>
									<el-form-item
										prop=""
										label=" "
										:label-width="6"
										style="width: 200px; height: 60px; margin-left: 16px"
										:rules="[
											{
												required: true,
												message: '请输入',
												trigger: 'blur',
											},
										]"
									>
										<el-select
											disabled
											v-model="
												configForm.comparisonValues[idx].ComparisonType
											"
											placeholder="请选择枚举值"
											style="width: 180px"
										>
											<el-option
												v-for="item in options3"
												:key="item.value"
												:label="item.label"
												:value="item.value"
											/>
										</el-select>
									</el-form-item>
								</div>
							</div>

							<!-- 最后行 -->
							<div style="display: flex; align-items: start">
								<el-form-item
									prop="conditionMode"
									label=" "
									:label-width="1"
									style="width: 200px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请选择',
											trigger: 'change',
										},
									]"
								>
									<el-select
										disabled
										v-model="configForm.conditionMode"
										placeholder="请选择比较方式"
										style="width: 200px"
									>
										<el-option
											v-for="item in conditionModeOptions"
											:key="item.value"
											:label="item.label"
											:value="item.value"
										/>
									</el-select>
								</el-form-item>
								<!-- <div style="margin-left: 8px">总数据量的</div> -->

								<el-form-item
									prop="specificValue"
									label=" "
									:label-width="1"
									style="width: 200px; height: 60px; margin-left: 16px"
									:rules="[
										{
											required: true,
											message: '请输入',
											trigger: 'change',
										},
									]"
								>
									<el-input
										disabled
										v-model.number="configForm.specificValue"
										type="number"
										style="width: 100%"
										placeholder="请输入数字"
									/>
								</el-form-item>
								<div style="margin-left: 8px">时，则完成任务</div>
							</div>
						</div>
					</div>
				</template>
			</Form>

			<template #footer-button>
				<div style="padding: 10px">
					<el-button @click="openConfigDialog = false">关闭</el-button>
					<!-- <el-button type="primary" @click="submitConfig(ruleFormRef)"> 确定 </el-button> -->
				</div>
			</template>
		</Dialog>

		<!-- 查看示例弹窗 -->
		<Dialog
			v-model="openExampleDialog"
			title="查看示例"
			:enableButton="true"
			:width="'60%'"
			:enableConfirm="false"
			@click-close="openExampleDialog = false"
		>
			<div>
				<div>方式一：按数据量计算</div>
				<div style="margin: 15px 20px">
					如年度重点工作目标为完成户厕改造65户，则业务表《户厕改造到户》中2024年的数据量>65时，则任务完成。
				</div>
				<img
					class="bg"
					src="../../../assets/image/example1.png"
					alt=""
					style="width: 100%; height: 100%; object-fit: cover"
				/>
			</div>

			<div style="margin: 20px 0">
				<div>方式二：按枚举值计算</div>
				<div style="margin: 15px 20px">
					如年度重点工作目标为农村生活垃圾分类覆盖率达100%，则可从业务表《【贯通融合】农村生活垃圾分类》取字段“是否覆盖”的值，当全部为是，即任务完成。
				</div>
				<img
					class="bg"
					src="../../../assets/image/example2.png"
					alt=""
					style="width: 100%; height: 100%; object-fit: cover"
				/>
			</div>

			<div>
				<div>方式三：按累加值计算</div>
				<div style="margin: 15px 20px">
					如年度重点工作目标为一般公共预算收入达445万元，则可从业务表《【贯通融合】一般公共预算收入》取字段“(增值)实际总计”的值，累加到445，则任务完成。
				</div>
				<img
					class="bg"
					src="../../../assets/image/example3.png"
					alt=""
					style="width: 100%; height: 100%; object-fit: cover"
				/>
			</div>
		</Dialog>
		<!-- 子任务列表弹窗 -->
		<Dialog
			:destroy-on-close="true"
			title="子任务"
			:enableButton="false"
			:width="'70%'"
			v-model="dialogSon"
			@heightChanged="heightChanged"
			@click-close="dialogSon = false"
		>
			<!-- 搜索表单 -->
			<div class="search">
				<Form
					v-model="searchFormSon"
					:data="formItemsSon"
					:enable-reset="false"
					label-width="0"
					column-count="4"
					button-vertical="flowing"
					confirm-text="查询"
					@submit="onSearchSon('search')"
					@clear="onSearchSon('clear')"
				>
				</Form>
			</div>

			<TableV2
				ref="tableRefSon"
				url="/api/ledger/telecom-task"
				:headers="{Urlkey: 'base'}"
				:req-params="reqParamsSon"
				:columns="tableColumnsSon"
				:enable-edit="false"
				:enable-delete="false"
				:enable-selection="false"
				:enable-latest-data="false"
				:height="tableHeight"
				:enable-toolbar="false"
				:form-label-width="100"
				:auto-height="true"
				@completed="onTableCompletedSon"
				@click-button="onTableButtonClickSon"
			>
				<template #taskName="{row}">
					<span
						class="link-click"
						@click="onTableButtonClickSon({btn: {code: 'detail'}, row})"
						>{{ row.taskName }}</span
					>
				</template>
				<!-- 责任人 -->
				<template #chargePersons="{row}">
					<div v-if="row.chargePersons.length">
						<!-- chargePersonName -->
						<span v-for="(item, idx) in row.chargePersons"
							>{{ item.chargePersonName }}、</span
						>
					</div>
					<div v-else></div>
				</template>

				<template #date="{row}">
					<div v-if="row.startTime">
						<span>{{ row.startTime }}~{{ row.endTime }}</span>
					</div>
					<!-- <div v-else>--</div> -->
				</template>

				<template #status="{row}">
					<div flex="~" items-center>
						<span
							mr-5px
							inline-block
							w-10px
							h-10px
							rounded-full
							:style="{
								backgroundColor: FlowTaskStatus.filter(
									(v) => v.value === row.status
								)[0].color,
							}"
						></span>
						<span
							:style="{
								color: FlowTaskStatus.filter((v) => v.value === row.status)[0]
									.color,
							}"
						>
							{{ FlowTaskStatus.filter((v) => v.value === row.status)[0].label }}
						</span>
					</div>
				</template>

				<template #progress="{row}">
					<span>{{ row.progress }}%</span>
				</template>

				<template #buttons="{row}">
					<el-button
						v-if="row.status === 1"
						type="primary"
						size="small"
						@click="onTableButtonClickSon({btn: {code: 'link'}, row})"
					>
						关联业务表
					</el-button>

					<el-button
						v-if="row.status === 2"
						type="primary"
						size="small"
						@click="onTableButtonClickSon({btn: {code: 'detail'}, row})"
					>
						查看
					</el-button>

					<el-dropdown v-if="row.status === 2" style="margin-left: 12px">
						<el-button size="small">
							更多
							<el-icon class="el-icon--right">
								<arrow-down />
							</el-icon>
						</el-button>

						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item>
									<span @click="openConfigSon(row.id)">更改配置</span>
								</el-dropdown-item>

								<el-dropdown-item>
									<span @click="openCancelSon(row.id)">取消关联</span>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChangeSon($event, 'page')"
				@size-change="onPaginationChangeSon($event, 'size')"
			></Pagination>
		</Dialog>

		<Dialog
			title="查看明细"
			:enableButton="false"
			:width="'70%'"
			v-model="dialogLook"
			@heightChanged="heightChanged"
			@click-close="dialogLook = false"
		>
			<!-- 查看明细弹窗 -->

			<TableV2
				ref="tableRefLook"
				:auto-load="true"
				:url="tableLookUrl"
				:headers="{Urlkey: 'base'}"
				:req-params="reqParamsLook"
				:columns="tableColumnsLook"
				:enable-edit="false"
				:enable-delete="false"
				:enable-selection="false"
				:enable-latest-data="false"
				:height="tableHeight"
				:enable-toolbar="false"
				:form-label-width="100"
				:auto-height="true"
				@completed="onTableCompletedLook"
			>
			</TableV2>
			<Pagination
				:total="paginationLook.total"
				:current-page="paginationLook.page"
				:page-size="paginationLook.size"
				@current-change="onPaginationChangeLook($event, 'page')"
				@size-change="onPaginationChangeLook($event, 'size')"
			></Pagination>
		</Dialog>
	</div>
</template>
<style lang="scss" scoped>
.item {
	align-items: center;
	display: flex;
	:deep(.el-form-item) {
		margin: 0 10px !important;
		width: 200px !important;

		.el-input-number {
			width: 100% !important;
		}
	}
}

.way-add {
	margin-bottom: 20px;
	margin-left: 30px;
	display: flex;
	justify-content: center;
	align-items: center;

	color: #7193ec;
	cursor: pointer;
	border: 1px dashed #7193ec;
}
.search {
	margin-bottom: 20px;
}
</style>
