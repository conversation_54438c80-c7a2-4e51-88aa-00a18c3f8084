<script setup lang="ts" name="reporttaskdetail">
import {
	computed,
	h,
	inject,
	nextTick,
	onBeforeUnmount,
	onDeactivated,
	onMounted,
	onUnmounted,
	reactive,
	ref,
	toRaw,
	watch,
} from 'vue'
import {Request} from '#/interface'
import {useRoute, useRouter} from 'vue-router'
import {
	ElMessage,
	ElMessageBox,
	ElNotification,
	FormInstance,
	FormRules,
	TabsPaneContext,
} from 'element-plus'
import {useUserStore} from '@/stores/useUserStore'
import {useViewStore} from '@/stores/useViewStore'
import {FillerFillingState, GetBatch} from '@/define/statement.define'
import util from '@/plugin/util'
import ReportFilter from '@/components/report-filter.vue'
import {STAFFROLEARRAY} from '@/define/organization.define'
import {useArrayToTree, useLedgerDN} from '@/hooks/useConvertHook'
// =====================
import {
	getLedgerListByUser,
	getDetailByLedgerId,
	saveLedgerConfig,
	getLedgerConfig,
	deleteLedgerConfig,
	getLedgerTableList,
	getLedgerTableListByLedgerConfig,
	usersByIds,
	GetMyBindLedgers,
} from '@/api/LedgerApi'
import {
	getReportTableList,
	ReportRevokeInnerWrite,
	detaileRevok,
	getLinkedListByReportTabkeId,
	getReportTableDataByReportTableId,
	deleteReportListByReportId,
	deleteReportDataAllByReportId,
	deleteLedgerFieldLinkedById,
	saveLedgerFieldLinked,
	changeReportTableType,
	GetLockReprotTable,
	SetLockReprotTable,
	DeleteLockReprotTable,
	updateFillerStatus,
	PushReportFlowTask,
	SetInternalStaff,
	IssueDataLeader,
} from '@/api/ReportApi'

import departmentFavoriteComp from '@/components/common/department-favorite-comp.vue'
import {uploadFile} from '@/plugin/upload'
import {useTaskManageStore} from '@/stores/taskManageStore'
// import scheduleProgressComp from '@/components/common/schedule-progress-comp.vue'
import {FlowAuditCode, FlowType} from '@/define/Workflow'
import {PushWorkflowProcessAudit, GetReportLogList} from '@/api/WorkflowApi'
import {request as defHttp} from '@/api/index'
import {ReportsFlowStatusEnum} from '@/define/statement.define'
import {GetWorkflowProcess} from '@/api/workflowTaskApi'
import {useFlowRecord} from '@/hooks/useFlowRecord'
import {USER_ROLES_ENUM} from '@/define/organization.define'
import {GetPlanTaskProcessDetail, GetReportFillProcess} from '@/api/ReportApi'
import {deleteLabel, updateLabelTitle} from '@/hooks/useLabels'

enum ReportType {
	DETAIL = 0, // 明细表
	STATISTICS = 1, // 统计表
}

enum Tips {
	Personnel = '人员',
	Department = '部门',
}

const router = useRouter()
const axios = inject('#axios') as Request
const route = useRoute()
const userStore = useUserStore()
const viewStore = useViewStore()
const isLader = computed(() => userStore.getUserInfo?.staffRole.includes('数据领导'))
const reportId = route.query.reportTaskId as string
const organizationId = route.query.areaOrganizationUnitId as string
const Id = route.query.id as string
const lockReportTable: any = ref(null)
const lockReportParams: any = ref(null)
const isLockReport = ref(false)
const blockRef = ref()
const blockHistoryRef = ref()
const areaOrganizationUnitId = route.query.areaOrganizationUnitId as string
const currLedgerScrollCount = ref(1)
const currentReportIds: any = ref([])
const currentReportData: any = ref(null)
const isPushing = ref(false)

const _excel_canview = computed(() =>
	_detailInfo.value ? _detailInfo.value.status !== 4 && _detailInfo.value.status !== 7 : false
)

const sheets = ref([])

// 详情组件title参数 可变化
const titleList = ref([
	{
		width: '25%',
		title: '任务名称',
		key: 'taskName',
	},
	{
		width: '25%',
		title: '创建部门',
		key: 'creationOrganizationUnitName',
	},
	{
		width: '25%',
		title: '创建人',
		key: 'creatorName',
	},
	{
		width: '25%',
		title: '填报截止时间',
		key: 'endDate',
	},
	{
		width: '50%',
		title: '填报说明',
		key: 'description',
	},
	// {
	// 	width: '50%',
	// 	title: '所属批次',
	// 	key: 'batch',
	// },
])
// 领导审核弹窗
const auditModalIsVisible = ref(false)
// 是否通过
const isPass = ref(false)
// 详情信息
const _detailInfo = ref<any>({})

let message: any = null

/**
 * 获取报表模版集合
 */
const curReport = ref(null as any)
const reportHeadList = ref([])
const reportHeaderEndRow = ref(0)
const getReportList = async () => {
	let sheet
	if (reportHeadList.value.length !== 0) {
		sheet = reportHeadList.value.find((item: any) => item.id === currentReportTableId.value)
		await getReportListById(currentReportTableId.value)
	} else {
		const res = await axios.get(
			`/api/filling/report-task-ou/${reportId}/${areaOrganizationUnitId}/report-tables`
		)
		const {data} = res
		// 选项卡
		sheet = data[0] // 默认显示第一个
		reportHeadList.value = data
		currentReportTableId.value = sheet.id
		// 明细表获取列表数据
		await getReportListById(sheet.id)
	}

	const celldata = []
	for (let i = 0; i < reportList.length; i++) {
		const item = reportList[i]
		const cells = JSON.parse(item.rawData)
		// 根据后端排序修复当前行列 r
		celldata.push(xlsxPlusRef.value?.$fixCellIndex(cells, item.rowNum - 1))
	}

	reportFilterRefs.value.curLedgerType = sheet.tableType
		? ReportType.STATISTICS
		: ReportType.DETAIL
	if (reportFilterRefs.value.curLedgerType === ReportType.STATISTICS) {
		// onToggleReportType()
	}

	const h = JSON.parse(sheet.header || '[]')
	// Xlsx - plus sheets
	sheets.value = [
		{
			name: sheet.name,
			head: h,
			data: celldata.flat(),
			config: JSON.parse(sheet.globalStyle || '{}'),
		},
	] as any
	importType.value = true
	reportHeaderEndRow.value = h[h.length - 1].r
	xlsxPlusRef.value?.$reload()
	curReport.value = sheet
}

// 获取报表任务所属子任务
const colData: any = [
	{
		title: '填报部门',
		field: 'department',
	},
	{
		title: '数据量',
		field: 'totalDataRows',
	},
	{
		title: '提交时间',
		field: 'submitTime',
	},
	{
		title: '汇总时间',
		field: 'auditTime',
	},
	{
		title: '补充说明',
		field: 'fillingDescription',
	},
]
const tableData = ref<any>()
const totalCount = ref(10)
const buttons = [
	// {
	// 	type: 'default',
	// 	code: 'revoke',
	// 	title: '撤回',
	// 	icon: '<i i-majesticons-revoke-line></i>',
	// 	verify: 'row.submitTime === null',
	// },
	{
		type: 'default',
		code: 'detail',
		title: '查看',
		icon: '<i i-majesticons-eye-line></i>',
		verify: 'true',
	},
]
const clickButton = (btn: {
	btn: {
		code: string
		title: string
		verify: string
	}
	scope: any
}) => {
	if (btn.btn.code === 'detail') {
		router.push({
			path: '/statementTodo/report-task-detail',
			query: {
				reportTaskId: btn.scope.reportTaskId,
				areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
				type: 'detail',
			},
		})
	}
	if (btn.btn.code === 'revoke') {
		// 撤回操作
		ElMessageBox({
			title: '提示',
			showCancelButton: true,
			message: () =>
				h('div', {style: 'margin-left:10px'}, [
					h('p', {style: 'font-size:16px'}, '您正在撤回您的已转发任务'),
					h(
						'p',
						{style: 'margin-top:10px;color:#9b9b9b;font-size:14px;'},
						'撤回后填报部门将无法填报该任务，请确认操作。'
					),
				]),
		})
			.then(() => {
				axios
					.delete(
						`/api/filling/report-task-ou/${btn.scope.reportTaskId}/${btn.scope.areaOrganizationUnitId}`
					)
					.then(async (res) => {
						ElNotification.success({
							title: '通知',
							message: '已撤回该任务',
						})
						reRequest.value = false
						await getReportInfo()
						if (!_excel_canview.value) {
							await getChildrenReportTask({
								reportTaskId: _detailInfo.value.reportTaskId,
								areaOrganizationUnitId: _detailInfo.value.areaOrganizationUnitId,
								id: _detailInfo.value.id,
							})
						}
						setTimeout(() => {
							reRequest.value = false
						}, 1000)
					})
					.catch((err: any) => {
						window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
					})
			})
			.catch(() => {})
	}
}
const getChildrenReportTask = async (data: {
	reportTaskId: string
	areaOrganizationUnitId: string
	id: string
}) => {
	await axios
		?.get(`/api/filling/report-task-ou/${data.id}/child-report-tasks`)
		.then((res) => {
			tableData.value = res.data.items.map((re: any) => ({
				...re,
				totalDataRows: re.totalDataRows === null ? 0 : re.totalDataRows,
			}))
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}

const setLabelTitle = () => {
	const type = route.query.type
	let head = ''
	if (type === 'detail') {
		head = '查看'
	} else if (type === 'audit') {
		head = '审核'
	}
	updateLabelTitle({
		path: router.currentRoute.value.fullPath,
		title: `${head}-任务待办-${currentReportData.value.reportTask.name}`,
	})
}

// 获取报表任务详情
const getReportInfo = async () => {
	const url = Id
		? `/api/filling/report-task-ou/detail?id=${Id}`
		: `/api/filling/report-task-ou/detail?reportId=${reportId}&areaOrganizationUnitId=${areaOrganizationUnitId}`
	await axios
		.get(url)
		.then((res) => {
			const {data} = res
			currentReportData.value = data
			if (data) {
				const {
					reportTaskDataAuditProcessId,
					transpondAuditProcessId,
					dataAuditWorkflowSchemeCode,
				} = data
				currentInfo.value = data
				_detailInfo.value = {
					id: data.id,
					fillingMode: data.fillingMode,
					taskName: data.reportTask.name,
					creationOrganizationUnitName: data.createdDepartment,
					organizationUnitName: '-',
					creatorName: data.creatorName,
					endDate: data.reportTask.newToDay,
					description: data.reportTask.description,
					fillingDescription:
						data.fillingDescription === null
							? data.auditOverview
							: data.fillingDescription,
					batch: GetBatch(
						data.reportTask.fillingPeriodType,
						data.reportTask.creationTime
					),
					status: data.status,
					rejectDescription: data.rejectDescription,
					auditOverview: data.auditOverview,
					actualFillerName: data.actualFillerName,
					reportTaskId: data.reportTaskId,
					areaOrganizationUnitId: data.areaOrganizationUnitId,
					submitTime: data.submitTime,
					waitingSendAreaOrganizationUnitNames: data.waitingSendAreaOrganizationUnitNames,
					parentAreaOrganizationUnitId: data.parentAreaOrganizationUnitId,
					attachments: data.reportTask.attachments,
					fileInfos: data.planTask?.fileInfos,
					reportTaskAreaOrganizationUnitFillers:
						data.reportTaskAreaOrganizationUnitFillers,
					stop: data.stop,
				}

				if (data.planTask?.fileInfos && data.planTask?.fileInfos.length !== 0) {
					titleList.value.push({
						width: '100%',
						title: '相关附件',
						key: 'fileInfos',
					})
				}
				// if (res.data.status !== 12) {
				imgFile.value =
					data.reportTask.attachments && data.reportTask.attachments.length !== 0
						? data.reportTask.attachments.map((v) => ({
								...v,
								name: v.name.includes('-')
									? v.name.split('-').slice(1).join()
									: v.name,
						  }))
						: []
				// }
				switch (route.query.type) {
					case 'detail':
						setTimeout(() => {
							// if (res.data.status !== 0) {
							// 	titleList.value.push({
							// 		width: '100%',
							// 		title: data.status !== 3 ? '审核意见' : '驳回原因',
							// 		key: data.status !== 3 ? 'auditOverview' : 'rejectDescription',
							// 	})
							// }
							if (
								res.data.status === 4 &&
								!titleList.value.some((v) => v.title === '填报部门')
							) {
								titleList.value.push({
									width: '100%',
									title: '填报部门',
									key: 'waitingSendAreaOrganizationUnitNames',
								})
							}

							titleList.value.push({
								width: '50%',
								title: '填报附件',
								key: 'attachments',
							})
						}, 200)
						break
					case 'audit':
						if (route.query.currentIndex === '2') {
							titleList.value.push(
								{
									width: '25%',
									title: '填报部门',
									key: 'waitingSendAreaOrganizationUnitNames',
								},
								{
									width: '25%',
									title: '填报人',
									key: 'actualFillerName',
								},
								{
									width: '50%',
									title: '补充说明',
									key: 'fillingDescription',
								}
							)
						}
						if (route.query.currentIndex === '4') {
							titleList.value.push(
								{
									width: '25%',
									title: '填报部门',
									key: 'waitingSendAreaOrganizationUnitNames',
								},
								{
									width: '25%',
									title: '填报人',
									key: 'actualFillerName',
								},
								{
									width: '50%',
									title: '补充说明',
									key: 'fillingDescription',
								},
								{
									width: '50%',
									title: '填报附件',
									key: 'attachments',
								}
							)
						}

						break
					case 'edit':
						setTimeout(() => {
							console.log(res.data.status)

							if (res.data.status !== 0) {
								// titleList.value.push({
								// 	width: '100%',
								// 	title: data.status !== 3 && data.status !== 12 ? '审核意见' : '驳回原因',
								// 	key:
								// 		data.status !== 3 && data.status !== 12 ? 'auditOverview' : 'rejectDescription',
								// })
							}
							// if (res.data.status === 12) {
							titleList.value.push({
								width: '50%',
								title: '填报附件',
								key: 'attachments',
							})
							// }
						}, 200)

						break
				}

				// 填报记录跳转, 流程记录
				const queryBusinessType = route.query.businessType
				if (queryBusinessType === 'ReportTaskDataAudit') {
					reportTaskDataAuditProcessId && getLogList(reportTaskDataAuditProcessId)
				} else if (queryBusinessType === 'ReportTaskTranspondAudit') {
					transpondAuditProcessId && getLogList(transpondAuditProcessId)
				} else if (
					currentReportData.value.status === 0 ||
					currentReportData.value.status === 1 ||
					currentReportData.value.status === 2 ||
					currentReportData.value.status === 3 ||
					currentReportData.value.status === 4 ||
					currentReportData.value.status === 14 ||
					currentReportData.value.status === 16
				) {
					getReportLogList(dataAuditWorkflowSchemeCode)
				}
			}
			setLabelTitle()
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}
// 审核表单数据
const auditformArray = ref([
	{
		type: 'textarea',
		title: isPass.value ? '审核意见' : '驳回原因',
		field: 'auditReason',
		filterable: true,
	},
])
const auditformRules = ref({
	auditReason: [
		{
			required: true,
			message: `请输入${isPass.value ? '审核意见' : '驳回原因'}`,
			trigger: 'blur',
		},
	],
})
watch(
	() => auditModalIsVisible.value,
	(val) => {
		if (val === true) {
			auditformArray.value = [
				{
					type: 'textarea',
					title: isPass.value ? '审核意见' : '驳回原因',
					field: 'auditReason',
					filterable: true,
				},
			]
			auditformRules.value = {
				auditReason: [
					{
						required: true,
						message: `请输入${isPass.value ? '审核意见' : '驳回原因'}`,
						trigger: 'blur',
					},
				],
			}
		}
	}
)
// 提交审核
const auditformSubmit = (val: any, formRef: FormInstance | undefined) => {
	// 表单验证
	formRef?.validate().then((validate: boolean) => {
		isPushing.value = true
		PushWorkflowProcessAudit(route.query.taskId as string, {
			code: passCode.value,
			des: val.auditReason,
		})
			.then(() => {
				ElMessage.success(
					`${
						passCode.value === FlowAuditCode.Agree
							? '已同意审核'
							: passCode.value === FlowAuditCode.Disagree
							? '已驳回审核'
							: ''
					} `
				)
				router.push({path: '/taskPending'})
				deleteLabel({path: router.currentRoute.value.fullPath})
			})
			.catch((err) => {
				window.errMsg(err, '审核')
			})
			.finally(() => {
				isPushing.value = false
			})
	})
}

/**
 * 根据报表模版id获取列表数据
 * @param id
 */
const exportRef = ref()
let reportList: any = []
const getReportListById = async (id: string, loading: boolean = true) => {
	const result = await axios.batch(`/api/filling/report-table/${id}/data`)
	reportList = result
	exportRef.value?.refreshList(result)
}

// 删除当前模版所有数据
const delAllReportTableList = () => {
	return new Promise((resolve, reject) => {
		axios
			.delete(`/api/filling/report-table/${currentReportTableId.value}/all-row`)
			.then((res) => {
				console.log('删除所有历史报表数据')
				reportList = []
				resolve(res)
			})
			.catch((err: any) => {
				window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
			})
	})
}
const passCode = ref()
// 领导审核报表任务
const audit = (code: FlowAuditCode) => {
	passCode.value = code
	isPass.value = code === FlowAuditCode.Agree
	auditModalIsVisible.value = true
	// getTaskList(1)
}
const wthdrawClick = () => {
	ElMessageBox({
		title: '提示',
		showCancelButton: true,
		message: () =>
			h('div', {style: 'margin-left:10px'}, [
				h('p', {style: 'font-size:16px'}, '您正在撤回您的填报任务。'),
				h(
					'p',
					{style: 'margin-top:10px;color:#9b9b9b;font-size:14px;'},
					'你正在进行任务撤回操作，原任务的审批记录将被清除，请确认该操作。'
				),
			]),
	}).then(() => {
		let parmas = {
			reportTaskId: route.query.reportTaskId,
			areaOrganizationUnitId: route.query.areaOrganizationUnitId,
			id: currentReportData.value.id,
			types: [0, 1],
		}
		axios.put('/api/filling/report-task-ou/detail-revok', parmas).then(() => {
			ElMessage.success('已撤回任务')
			deleteLabel({path: router.currentRoute.value.fullPath})
		})
	})
}

// Xlsx plus =====================
const xlsxPlusRef = ref()
const saving = ref(false)

const onAutoSave = async (
	data: any,
	sheetConfig: any,
	isInsert: boolean = false,
	isDelete: boolean = false
) => {
	if (data.length === 0) {
		ElMessage.warning('没有数据可保存')
		return
	}

	if (saving.value) {
		ElMessage.warning('正在保存中，请稍后')
		return
	}

	saving.value = true

	let result: any = []
	const len = data.length

	for (let i = 0; i < len; i++) {
		if (isInsert) {
			// 新增数据自动保存
			result.push({
				rowId: util._guid(),
				actionType: 1,
				rowNum: data[i][0].r + 1,
				rawData: JSON.stringify(data[i]),
			})
		} else if (isDelete) {
			result = data
		} else {
			// 修改数据自动保存
			const dl = data[i].length
			const rd = reportList.find((f: any) => f.rowNum === data[i][0].r + 1)

			result.push({
				rowId: rd ? rd.rowId || rd.id : util._guid(),
				actionType: rd ? 2 : 1,
				rowNum: rd ? rd.rowNum : dl > 0 ? data[i][0].r + 1 : i + 2,
				rawData: JSON.stringify(data[i]),
			})
		}
	}

	const promise = []

	// 更新当前报表模版的gloableStyle
	if (sheetConfig) {
		promise.push(
			axios.put(`/api/filling/report-table/${currentReportTableId.value}/global-style`, {
				globalStyle: JSON.stringify(sheetConfig),
			})
		)
	}

	await deleteReportListByReportId(currentReportTableId.value)
	await axios.put(`/api/filling/report-table/${currentReportTableId.value}/data`, result)

	ElMessage.success('保存成功')
	saving.value = false
	getReportTabs()
	xlsxPlusRef.value.$saveDone()
}

const onXlsxPlusSave = (sheets: Array<any>) => {
	if (isLockReport.value) {
		return
	}

	const sheet = sheets[0]
	// console.log(sheet)
	// const rawData = RemoveLastEmpty(sheet.rawData)
	// function RemoveLastEmpty(list: any[]) {
	// 	if (!list[list.length - 1].some((v: any) => v.v.v)) {
	// 		list.pop()
	// 		RemoveLastEmpty(list)
	// 	} else {
	// 		return list
	// 	}
	// }
	// console.log(rawData)
	console.log(sheet.rawData)
	onAutoSave(sheet.rawData, sheet.config)
}

const onXlsxPlusAddRow = (rows: any, sheetConfig: any) => {
	// onAutoSave(rows, sheetConfig, true)
}

const onXlsxPlusDeleteRow = (range: any, headerEndRow: number, sheetConfig: any) => {
	const rows = []
	for (let i = range[0]; i <= range[1]; i++) {
		if (i >= 0 && i < reportList.length) {
			const r = reportList[i - headerEndRow]
			if (r) {
				rows.push({
					rowNum: i + headerEndRow,
					rowId: reportList[i - headerEndRow].id,
					actionType: 3,
				})
			}
		}
	}
	// onAutoSave(rows, sheetConfig, false, true)
}

// 业务表数据筛选/统计维度 =================================================
const reportFilterRefs = ref({
	visible: false,
	curLedger: null as any,
	curLedgerType: ReportType.DETAIL,

	list: [],
	filterItems: [],
	dimensionItems: [],
	calculationItems: [],
})

const fixedRule = ref([])
const onReportFilterRuleSave = (rules: any, fr: any) => {
	reportFilterRefs.value.visible = false
	const {filter, dimension, calculation} = rules

	dimension.forEach((f: any) => (f.type = 'dimension'))
	calculation.forEach((f: any) => (f.type = 'calculation'))

	const has: any = reportFilterRefs.value.list.find(
		(f: any) =>
			f.reportTableId === currentReportTableId.value &&
			f.ledgerId === reportFilterRefs.value.curLedger.id
	)
	const param = {
		ledgerId: reportFilterRefs.value.curLedger.id,
		reportTableId: currentReportTableId.value,
		filterRule: JSON.stringify(filter),
		statisticRule: JSON.stringify(dimension),
		calculateRule: JSON.stringify(calculation),
		staffId: userStore.userInfo?.id,
	}
	const promise = has
		? axios.put(`/api/filling/table-ledger-rule-config/${has.id}`, param)
		: axios.post('/api/filling/table-ledger-rule-config', param)
	promise.then((res) => {
		fixedRule.value = fr
		// onToggleReportType()
		ElNotification.success('规则保存成功')
	})
}

const getRpeortFilterRules = () => {
	if (!reportFilterRefs.value.curLedger) {
		return
	}
	return new Promise((resolve, reject) => {
		axios
			.get(
				`/api/filling/table-ledger-rule-config?ReportTableId=${currentReportTableId.value}&LedgerId=${reportFilterRefs.value.curLedger?.id}`
			)
			.then((res) => {
				reportFilterRefs.value.list = res.data.items

				const curItem = res.data.items.find(
					(f: any) =>
						f.reportTableId === currentReportTableId.value &&
						f.ledgerId === reportFilterRefs.value.curLedger?.id
				)

				reportFilterRefs.value.filterItems = curItem ? JSON.parse(curItem.filterRule) : []
				reportFilterRefs.value.dimensionItems = curItem
					? JSON.parse(curItem.statisticRule)
					: []
				reportFilterRefs.value.calculationItems = curItem
					? JSON.parse(curItem.calculateRule)
					: []

				//TODO: 业务表固定的街道社区
				const isDistrict = userStore.casualUser.district !== ''
				const isStreet = userStore.casualUser.street !== ''

				const fixed = [
					{title: '街道', field: 'ssjd', type: ''},
					{title: '社区', field: 'sssq', type: ''},
				]
				const rfrvc = reportFilterRefs.value.calculationItems.map((m: any) => ({
					title: m.item?.zwm,
					field: m.item?.zdm,
					type: m.type,
				}))
				const rfr = reportFilterRefs.value.dimensionItems.map((m: any) => ({
					title: m.item?.zwm,
					field: m.item?.zdm,
					type: m.type,
				}))

				if (isDistrict && !isStreet) {
					rfr.unshift(fixed[0])
				} else if (isDistrict || isStreet) {
					rfr.unshift(fixed[1])
				}

				// linkedRefs.value.tjFields = [...rfr, ...rfrvc]

				resolve('')
			})
	})
}

// =================================================
let dragField: any = null
let ledgerTypeHistory = ReportType.STATISTICS

const isFull = ref(false)
const reportTabs = ref([{displayName: '请稍等...', id: 'loading'}])
const reportTabId = ref('loading')
const headerEndRow = ref(0)
const currentReportTableId = ref('')
const currentCheckedLedger: any = ref(null)

const importType = ref(true)
const ledgerType = ref(ReportType.STATISTICS)
const ledgerTypeOptions = [
	{label: '明细数据', value: 0},
	{label: '统计数据', value: 1},
]

const ledgerFillConfigRef = ref()
const ledgerList: any = ref([])
const ledgerFields: any = ref([])
const statisticsFields: any = ref([])
const ledgerConfig: any = ref(null)
const ledgerConfigCurrent: any = ref(null)
const ledgerConfigDefaultFilter: any = ref([])
const ledgerConfigDefaultDimension: any = ref([])
const ledgerConfigDefaultCalculation: any = ref([])
const ledgerConfigMappping: any = ref(null)
const linkedFieldRaw: any = ref([])
const linkedFields: any = ref([])
const linkedLedger: any = ref(null)

const infoOverViewHeight = ref(0)
let reportTabIdHistory: any = ''

const isAlien = ref(false)
// 1
const getReportTabs = () => {
	const {id, reportTaskId, areaOrganizationUnitId}: any = route.query
	let promise = null
	if (id) {
		promise = getReportTableList(id)
	} else {
		promise = getReportTableList(reportTaskId, areaOrganizationUnitId)
	}
	promise.then(async (res: any) => {
		const {data} = res

		const first =
			reportTabId.value === 'loading'
				? data[0]
				: data.find((f: any) => f.id === reportTabId.value)

		reportTabs.value = data
		reportTabId.value = first.id
		reportTabIdHistory = first.id
		ledgerType.value = first.tableType
		ledgerTypeHistory = first.tableType
		headerEndRow.value = Math.max(...JSON.parse(first.header || '[]').map((m: any) => m.r))

		const datas = await getReportTableDataByReportTableId(reportTabId.value)

		const celldata: any = []
		for (const element of datas) {
			const item = element
			let cells: any

			if (element.id === '41db9d57-550f-d8d3-bbd2-a05b525a0e5e') {
				const data = JSON.parse(item.rawData)
				data.forEach((x) => {
					// if(x.v.v === '国防教育'){
					x.v.mc = {
						r: 0,
						c: 0,
						cs: 0,
						rs: 0,
					}
					// }
				})
				item.rawData = JSON.stringify(data)
				console.log(33, JSON.parse(item.rawData))
			}
			//TODO 兼容乡镇（街道）切坡建设农村住房边坡调查明细表 数据量大导致报表打不开
			// JSON.parse(item.rawData).forEach((d: any,index) => {
			// 	console.log(2323,item,index)

			// })
			if (reportTabId.value === '3a13fa29-eaab-9656-7bd9-7ff54f3e4be1')
				cells = JSON.parse(item.rawData).filter((d) => d.v.v && d.v.v !== null)
			else {
				cells = JSON.parse(item.rawData)
			}

			// 根据后端排序修复当前行列 r
			celldata.push(xlsxPlusRef.value?.$fixCellIndex(cells, item.rowNum - 1))
		}

		const h = JSON.parse(first.header || '[]')
		console.log(23, h)
		// h.forEach((m: any) => {
		// 	console.log(m.v.mc);

		// })

		isAlien.value = first.isAlien
		// Xlsx - plus sheets
		sheets.value = [
			{
				name: first.name,
				head: h,
				data: celldata.flat(),
				config: JSON.parse(first.globalStyle || '{}'),
			},
		] as any

		xlsxPlusRef.value?.$reload()

		currentReportTableId.value = first.id
		// if (importType.value && route.query?.type === 'edit' && !isAlien.value)
		if (route.query.type === 'edit' && !isAlien.value) {
			ledgerList.value = []
			ledgerFields.value = []
			if (ledgerType.value === ReportType.STATISTICS) {
				getStatisticsLinked(() => getLedgerList())
			} else {
				getLinkedList((reports: Array<any>) => getLedgerList(reports))
			}
		}
	})
}
const filterValue = ref()
// 2
const getLedgerList = (reports?: Array<any>) => {
	return new Promise((resolve, reject) => {
		getLedgerListByUser({
			isOnline: true,
			maxResultCount: 20,
			skipCount: (currLedgerScrollCount.value - 1) * 20,
			ledgerIds: reports || currentReportIds.value,
			filter: filterValue.value,
		})
			.then(async (res: any) => {
				const promise: any = []
				const hasReports = reports && reports.length > 0
				let currItems = res.data.items
				if (hasReports) {
					console.log(111, reports, currentReportIds.value)

					const bindLedger = await GetMyBindLedgers(reports || currentReportIds.value)
					currItems.unshift(...bindLedger.data)
					ledgerList.value = currItems
				} else {
					if (ledgerList.value.length === 0) {
						ledgerList.value = currItems
					} else {
						ledgerList.value.push(...currItems)
					}
				}

				currItems.forEach((f: any) => {
					promise.push(getDetailByLedgerId(f.ledger.id))
				})

				const fields = await Promise.all(promise)
				const _ledgerFields = fields.map((m: any) => m.data.tableInfo)

				_ledgerFields.forEach((f: any) => {
					try {
						f.fields = useLedgerDN(
							JSON.parse(
								JSON.stringify(
									f?.fields.filter(
										(ff: any) => ff.isListField && ff.isDepartmentField
									)
								) || '[]'
							),
							toRaw(f?.tableFieldGroups)
						)
					} catch (e) {
						console.log('错误', e)
					}
				})

				if (hasReports) {
					ledgerFields.value = _ledgerFields
				} else {
					if (ledgerFields.value.length === 0) {
						ledgerFields.value = _ledgerFields
					} else {
						ledgerFields.value.push(..._ledgerFields)
					}
				}

				resolve('')
			})
			.catch((err: any) => {
				window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
			})
	})
}

// 3
const getLinkedList = (fn: Function) => {
	const reportTableId = (reportTabs.value.find((f: any) => f.id === reportTabId.value) as any)?.id
	getLinkedListByReportTabkeId(reportTableId)
		.then(async (res: any) => {
			const {items} = res.data
			const reportIds = Array.from(new Set(items.map((f: any) => f.ledgerId)))
			currentReportIds.value = reportIds

			fn && (await fn(reportIds))

			linkedFields.value.length = 0
			linkedFieldRaw.value = JSON.parse(JSON.stringify(items))
			RenderBinding(items)
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}

// 4
const getStatisticsLinked = async (fn: Function) => {
	// 统计表, 获取每个业务表的配置信息
	const statisticsPromise: any = []
	ledgerList.value.forEach((f: any) => {
		statisticsPromise.push(getCurrentLedgerConfig(f.ledger.id))
	})

	const allStatistics = await Promise.all(statisticsPromise)
	const __statisticsFields: any = []

	currentCheckedLedger.value = null

	allStatistics.forEach((res: any) => {
		const currentFields: any = []
		if (res.data?.length > 0) {
			console.log('res.data', res.data)

			// 组装统计表配置字段
			const data = res.data[0]
			const ledger: any = ledgerList.value.find((f: any) => f.ledgerId === data.tableInfoId)
			const fields = ledgerFields.value.find((f: any) => f.id === ledger.ledgerId).fields
			const {selectByModel} = data

			selectByModel.forEach((item: any) => {
				let raw = null
				let name = item[0]
				let displayName = ''
				if (typeof name === 'string') {
					raw = fields.find((f: any) => f.name === name)
					displayName = raw.displayName
				} else if (typeof name === 'object') {
					name = item[1]
					const __name = name.replace(/(\[.+\].*)/g, '')
					raw = fields.find((f: any) => f.name === __name)
					displayName = `${raw.displayName}${name.match(/(\[.+\].*)/g) ?? ''}`
				}

				const axis = data.bindMappings[name]
				const field = {
					name,
					displayName,
					tableInfoId: ledger.ledgerId, // 业务表id
					isListField: true,
					__config: data,
					__checked: axis ? true : false,
					__position: {r: 0, c: 0},
					__column: '',
					__columnRaw: '',
					__type: '',
				}

				if (axis) {
					const {row, column} = JSON.parse(axis)[0]

					if (typeof name === 'string') {
						// 维度
						field.__type = 'dimension'
					}

					if (typeof item[0] === 'object' && name.includes('[')) {
						// 多维度
						field.__type = 'dimension'
					}

					if (typeof item[0] === 'object' && !name.includes('[')) {
						// 计数
						field.__type = 'calculation'
					}

					const color = field.__type === 'dimension' ? '#f78989' : '#66b1ff'

					field.__position.r = row[0]
					field.__position.c = column[0]
					field.__column = xlsxPlusRef.value.$getRangeAxis({row, column})[0]
					field.__columnRaw = axis

					setTimeout(() => {
						xlsxPlusRef.value.$setCellValue(row[0], column[0], {bg: color}, false)
					}, 200)
				}
				currentFields.push(field)
			})

			if (Object.keys(data.bindMappings).length > 0) {
				linkedLedger.value = ledger
				currentCheckedLedger.value = ledger
				ledgerConfigMappping.value = data.bindMappings
				ledgerFillConfigRef.value.setChecked(ledger)
			}
		}
		__statisticsFields.push({
			fields: currentFields,
		})
	})

	statisticsFields.value = __statisticsFields
	fn && fn()
}

const RenderBinding = (items: any) => {
	let isFindLedger = false
	let findLedger: any = null
	ledgerFields.value.forEach((ledger: any) => {
		ledger?.fields.forEach((field: any) => {
			const linked = items.find((f: any) => f.ledgerField === field.id)
			if (linked) {
				setTimeout(() => {
					const columnName = JSON.parse(linked.columnName)
					const {row, column} = columnName[0]
					const color = '#eebe77'
					field.__checked = true
					field.__position = {r: row[0], c: column[0]}
					field.__column = xlsxPlusRef.value.$getRangeAxis({row, column})[0]
					field.__columnRaw = columnName
					linkedFields.value.push(field)
					xlsxPlusRef.value.$setCellValue(row[0], column[0], {bg: color}, false)
					if (!isFindLedger) {
						isFindLedger = ledgerList.value.some((item: any) => {
							if (item.ledger.id === linked.ledgerId) {
								findLedger = item
								return true
							}
						})
					}
					if (isFindLedger && findLedger) {
						ledgerFillConfigRef.value.setChecked(findLedger)
					}
				}, 101)
			}
		})
	})
}

const getCurrentLedgerConfig = (ledgerId: string) => {
	return getLedgerConfig(ledgerId, userStore.getUserInfo.id, {
		TargetId: currentReportTableId.value,
		QueryType: ledgerType.value,
	})
}

const onChangeReport = (pane: TabsPaneContext, ev: Event) => {
	console.log('onChangeReport', pane, ev)
	console.log(route.query.type)

	if (route.query.type === 'edit') {
		ElMessageBox.confirm(`请确认已经保存过数据再切换`, '提示', {
			confirmButtonText: '确认',
			cancelButtonText: '取消',
			type: 'info',
		})
			.then(async () => {
				reportTabIdHistory = reportTabId.value
				getReportTabs()
			})
			.catch(() => {
				reportTabId.value = reportTabIdHistory
			})
	} else {
		reportTabIdHistory = reportTabId.value
		getReportTabs()
	}
}

const onToggleReportType = () => {
	if (!currentCheckedLedger.value) {
		ElMessage.warning('请选择业务表')
		ledgerType.value = ledgerTypeHistory
		return
	}

	ElMessageBox.confirm(`切换类型会清空列表数据、关联关系,是否继续?`, '警告', {
		confirmButtonText: '切换',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await changeReportTableType(currentReportTableId.value, ledgerType.value)
			clearAllDataByReportId(false, () => {
				xlsxPlusRef.value.clear()
			})
		})
		.catch(() => {
			ledgerType.value = ledgerTypeHistory
		})
}

const onLedgerChecked = (ledger: any, checked: boolean) => {
	console.log('onLedgerChecked', ledger, checked)
	currentCheckedLedger.value = checked ? ledger : null
}

const onLedgerFieldDragStart = (item: any) => {
	dragField = toRaw(item)
	console.log('dragField', dragField)
}

// 拖动关联字段到表头
const onLedgerFieldDragToCell = async (
	axis: any,
	position: any,
	inHeader: boolean,
	data?: any,
	bindMappingstype: boolean = true
) => {
	if (data) {
		dragField = data
	}
	if (!inHeader) {
		ElMessage.warning('请拖动到表头以内单元格关联')
		return
	}
	const {r, c} = position
	// 明细
	if (ledgerType.value === ReportType.DETAIL) {
		// 字段是否被关联过
		const isLinked =
			linkedFields.value.some((field: any) => field.id === dragField?.id) ||
			dragField?.__checked
		// 关联的字段是否在同一个业务表
		const isSameLedger = linkedFields.value.some(
			(field: any) => field.tableInfoId !== dragField.tableInfoId
		)
		if (isLinked) {
			ElMessage.warning('该字段已被关联过, 请重新选择')
			return
		}

		if (isSameLedger && linkedFields.value.length > 0) {
			ElMessage.warning('请选择同一个业务表的字段进行关联')
			return
		}

		// 判断是否同一个位置
		const isSamePosition = linkedFields.value.some(
			(f: any) => f.__position.r === r && f.__position.c === c
		)

		if (isSamePosition && linkedFields.value.length > 0) {
			if (data) {
				return
			}
			ElMessage.warning('该位置已被关联, 请重新选择')
			return
		}
		const reportTableId = (reportTabs.value.find((f: any) => f.id === reportTabId.value) as any)
			.id
		await saveLedgerFieldLinked({
			ledgerId: dragField.tableInfoId,
			ledgerField: dragField.id,
			reportTableId: reportTableId,
			columnName: JSON.stringify([{row: [r, r], column: [c, c]}]),
		}).then((res: any) => {
			ElMessage({
				type: 'success',
				dangerouslyUseHTMLString: true,
				message: `<div text='13px #333'><strong text='#f00'>${
					dragField.displayName
				}</strong> 关联到第 <strong>${c + 1}</strong> 列</div>`,
			})

			const color = '#eebe77'
			xlsxPlusRef.value.$setCellValue(r, c, {bg: color}, false)
			linkedFields.value.push(dragField)
			ledgerFields.value.forEach((ledger: any) => {
				ledger.fields.some((field: any) => {
					if (field.id === dragField.id) {
						field.__checked = true
						field.__position = {r, c}
						field.__column = axis
						field.__columnRaw = [{row: [r, r], column: [c, c]}]
						return true
					}
				})
			})
			dragField = null
		})
	}

	// 统计
	if (ledgerType.value === ReportType.STATISTICS) {
		console.log(axis, position, inHeader, dragField)

		const mappings: any = {}
		const ledgerId = dragField.__config.tableInfoId
		const oldMappingKeys = Object.keys(dragField.__config.bindMappings)

		if (oldMappingKeys.includes(dragField.name)) {
			ElMessage.warning('该字段已被关联过, 请重新选择')
			return
		}

		if (ledgerId !== linkedLedger.value?.ledgerId && linkedLedger.value !== null) {
			ElMessage.warning('请选择同一个业务表的字段进行关联')
			return
		}

		mappings[dragField.name] = JSON.stringify([{row: [r, r], column: [c, c]}])
		if (!bindMappingstype) {
			Object.assign(bindMappings.value, dragField.__config.bindMappings, mappings)
		} else {
			await onLedgerConfigSave(
				{
					targetId: currentReportTableId.value,
					queryType: ledgerType.value,
					selectByModel: dragField.__config.selectByModel,
					conditionalByModel: dragField.__config.conditionalByModel,
					groupByModel: dragField.__config.groupByModel,
					orderByModel: dragField.__checked.orderByModel,
					bindMappings: Object.assign(
						{},
						dragField.__config.bindMappings,
						mappings,
						bindMappings.value
					),
				},
				true
			)
		}
	}
}
const bindMappings: any = ref({})
const bindMappingstype = ref(false)
const onDeleteLinkedField = (field: any) => {
	console.log('onDeleteLinkedField', field)
	if (ledgerType.value === ReportType.DETAIL) {
		const {r, c} = field.__position
		xlsxPlusRef.value.$setCellValue(r, c, {bg: '#eee'}, false)

		field.__checked = false
		field.__position = null
		field.__column = null
		field.__columnRaw = null

		if (Object.keys(linkedFieldRaw.value).length > 0) {
			deleteLedgerFieldLinkedById(
				linkedFieldRaw.value.find((f: any) => f.ledgerField === field.id).id
			)
		}

		linkedFields.value = linkedFields.value.filter((f: any) => f.id !== field.id)
	} else if (ledgerType.value === ReportType.STATISTICS) {
		const config = toRaw(field.__config)
		delete config.bindMappings[field.name]
		xlsxPlusRef.value.$setCellValue(field.__position.r, field.__position.c, {bg: '#eee'}, false)
		onLedgerConfigSave(config, true)
	}
}

const onSettingLedger = async (ledger: any) => {
	getCurrentLedgerConfig(ledger.ledgerId)
		.then((res: any) => {
			const currentConfig = res.data[0]
			if (currentConfig) {
				const fields = ledgerFields.value.find((f: any) => f.id === ledger.ledgerId)

				// 筛选规则数据处理 ==============================================
				ledgerConfigDefaultFilter.value =
					currentConfig.conditionalByModel[0].ConditionalList.map((item: any) => {
						const value = item.Value
						const raw = toRaw(fields.find((f: any) => f.name === value.FieldName))

						return {
							field: value.FieldName,
							value: value.FieldValue.includes(',')
								? value.FieldValue.split(',')
								: [value.FieldValue],
							conditionType: value.ConditionalType,
							type: value.CSharpTypeName,
							raw,
						}
					})

				// 统计维度 ==============================================
				const dimensionTemporary: any = []
				currentConfig.selectByModel
					.filter(
						(f: any) => f[0].hasOwnProperty('BetweenCount') || typeof f[0] === 'string'
					)
					.forEach((item: any) => {
						if (Array.isArray(item)) {
							let current = item[0]
							const row: any = {
								field: null,
								value: null,
								default: 0,
								raw: null,
							}

							if (typeof current === 'string') {
								row.raw = toRaw(fields.find((f: any) => f.name === current))
								row.value = [[]]
							} else if (typeof current === 'object') {
								const itemValue: any = Object.values(item[0])[0]
								current = item[1].replace(/(\[.+\].*)/g, '')
								row.raw = toRaw(fields.find((f: any) => f.name === current))
								if (row?.raw?.type === 'dete' || row?.raw?.type === 'datetime') {
									row.value = [[itemValue[1]]]
								} else {
									row.value = [itemValue[1]]
								}
								row.default = 1
							}

							row.field = row.raw.displayName

							const isRepeat = dimensionTemporary.some((s: any) => {
								if (s.field === row.raw?.displayName) {
									s.value.push(row.value.flat())
									return true
								}
							})

							if (!isRepeat) {
								dimensionTemporary.push(row)
							}
						}
					})
				ledgerConfigDefaultDimension.value = dimensionTemporary

				// 计算规则 ==============================================
				const calculationTemporary: any = []
				currentConfig.selectByModel
					.filter(
						(f: any) => !f[0].hasOwnProperty('BetweenCount') && typeof f[0] !== 'string'
					)
					.forEach((item: any) => {
						const raw = fields.find((f: any) => f.name === item[1])
						console.log(1111, raw, item)

						calculationTemporary.push({
							field: raw.name,
							flag: Object.keys(item[0])[0],
							raw,
						})
					})
				ledgerConfigDefaultCalculation.value = calculationTemporary
			} else {
				ledgerConfigDefaultFilter.value = []
				ledgerConfigDefaultDimension.value = []
				ledgerConfigDefaultCalculation.value = []
			}

			ledgerConfigCurrent.value = ledger
			ledgerConfig.value = ledgerFields.value.find((f: any) => f?.id === ledger.ledgerId)
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}

const onLedgerConfigClose = () => {
	ledgerConfig.value = null
	ledgerConfigCurrent.value = null
}

const onLedgerConfigSave = async (config: any, isUpdate: boolean = false) => {
	console.log('业务表规则配置: ', config, currentCheckedLedger.value)

	let {filterFields, dimensionFields, calculationFields, bindMappings} = config
	const data: any = {
		targetId: currentReportTableId.value,
		queryType: ledgerType.value,
		selectByModel: [],
		conditionalByModel: [],
		groupByModel: [],
		orderByModel: [],
	}

	if (bindMappings) {
		data.bindMappings = bindMappings
	}

	if (!isUpdate) {
		// 筛选规则数据处理 ==============================================
		if (
			filterFields.some(
				(item: any) =>
					!item.value ||
					!item.value[0] ||
					typeof item.conditionType !== 'number' ||
					!item.field
			)
		) {
			if (filterFields.length === 1) {
				filterFields = []
			} else {
				ElMessage.warning('请完善筛选规则')
				return
			}
		}
		const conditions = filterFields.map((field: any, index: number) => {
			return {
				Key: index < 1 ? -1 : 0,
				Value: {
					FieldName: field.field,
					FieldValue: Array.isArray(field.value) ? field.value.join() : field.value,
					ConditionalType: field.conditionType,
					CSharpTypeName: field.type,
				},
			}
		})

		// 统计维度处理 ==============================================
		const isDimension = dimensionFields.some((item: any) => {
			if (item.default === 1 && item.value.some((sub: any) => sub.length === 0)) {
				return true
			}
		})

		if (isDimension) {
			ElMessage.warning('请完善统计维度')
			return
		}
		const dimensionList: any = []
		dimensionFields.forEach((field: any) => {
			if (field.default === 0) {
				dimensionList.push([field.raw.name])
			} else {
				field.value.forEach((val: any, index: number) => {
					let between = null
					if (val.length === 1 && Array.isArray(val[0])) {
						between = val[0]
					} else {
						between = val
					}
					dimensionList.push([
						{
							BetweenCount: [field.raw.name, between],
						},
						`${field.raw.name}[${between[0]}/${between[1]}]`,
					])
				})
			}
		})

		data.groupByModel = dimensionFields
			.map((item: any) => {
				if (item.default === 0) {
					return {FieldName: item.raw.name}
				}
				return null
			})
			.filter(Boolean)

		// 计算规则处理 ==============================================
		calculationFields.forEach((field: any) => {
			if (field.field) {
				let item: any = null
				const data: any = {}
				if (field.flag === 'AggregateCount') {
					data[field.flag] = [`{int}:1`]
				} else {
					data[field.flag] = [field.field]
				}
				item = [data, field.field]
				dimensionList.push(item)
			}
		})
		// bindmapping ==============================================
		if (ledgerConfigMappping.value) {
			// 当前业务表有历史关联
			const keys = dimensionList
				.map((item: any) => {
					const key = item[0]
					if (typeof key === 'string') {
						return key
					}

					if (typeof key === 'object' && item[1].includes('[')) {
						return item[0].BetweenCount[0]
					}

					if (typeof key === 'object' && !item[1].includes('[')) {
						return item[1]
					}
					return null
				})
				.filter(Boolean)
			Object.keys(ledgerConfigMappping.value).forEach((key: any) => {
				if (!keys.includes(key)) {
					const {row, column} = JSON.parse(ledgerConfigMappping.value[key])[0]
					xlsxPlusRef.value.$setCellValue(row[0], column[1], {bg: '#eee'}, false)
					delete ledgerConfigMappping.value[key]
				}
			})

			data.bindMappings = toRaw(ledgerConfigMappping.value)
		}

		// 保存数据 ==============================================
		data.selectByModel = dimensionList
		data.conditionalByModel.push({ConditionalList: conditions})

		console.log('data', data)
	}

	await saveLedgerConfig(
		isUpdate ? currentCheckedLedger.value.ledgerId : ledgerConfig.value.id,
		userStore.getUserInfo.id,
		isUpdate ? config : data
	).then(async (res: any) => {
		ElMessage.success('保存成功')
		ledgerConfig.value = null
		ledgerConfigCurrent.value = null

		if (ledgerType.value === ReportType.STATISTICS) {
			await getStatisticsLinked()
		}
	})
}

const clearAllDataByReportId = (bool: boolean = true, fn?: Function) => {
	const next = () => {
		dragField = null
		linkedLedger.value = null
		ledgerConfig.value = null
		ledgerConfigCurrent.value = null
		ledgerConfigMappping.value = null

		ledgerList.value.length = 0
		ledgerFields.value.length = 0
		linkedFieldRaw.value.length = 0
		linkedFields.value.length = 0
		statisticsFields.value.length = 0
		ledgerConfigDefaultFilter.value.length = 0
		ledgerConfigDefaultDimension.value.length = 0
		ledgerConfigDefaultCalculation.value.length = 0

		deleteLedgerConfig(currentCheckedLedger.value?.ledgerId, userStore.getUserInfo.id, {
			TargetId: currentReportTableId.value,
			QueryType: ledgerType.value,
		})
		deleteReportDataAllByReportId(currentReportTableId.value)
		getReportTabs()

		fn && fn()
	}

	if (!currentCheckedLedger.value && bool) {
		// ElMessage.warning('请选择业务表')
		xlsxPlusRef.value.$clear()
		return
	}

	if (bool) {
		ElMessageBox.confirm(`确认清空所有列表数据、关联关系, 以及当前业务表的配置吗?`, '警告', {
			confirmButtonText: '清空',
			cancelButtonText: '取消',
			type: 'warning',
		}).then(() => {
			next()
		})
	} else {
		next()
	}
}

const onToggleImportType = (val: boolean) => {
	ElMessageBox.confirm(`切换导入类型会清空Excel已有数据, 是否切换?`, '警告', {
		confirmButtonText: '清空',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			importType.value = val
			if (val) {
				getReportTabs()
				xlsxPlusRef.value.$clear()
			} else {
				xlsxPlusRef.value?.$reload()
			}
		})
		.catch(() => {
			importType.value = !val
			if (importType.value) {
				getReportTabs()
				xlsxPlusRef.value.$clear()
			}
		})
}

let xlsxPlusParent: any = null
const onFull = () => {
	isFull.value = !isFull.value

	const xlsxPlus = document.querySelector('.xlsx-plus-component') as HTMLElement
	if (isFull.value) {
		xlsxPlusParent = xlsxPlus.parentElement
		xlsxPlus.classList.add('xlsx-plus-component-full')
		document.body.appendChild(xlsxPlus)
	} else {
		xlsxPlus.classList.remove('xlsx-plus-component-full')
		xlsxPlusParent.appendChild(xlsxPlus)
		xlsxPlusParent = null
	}

	xlsxPlusRef.value.$resize()
}

const showProgress = ref(true)

const onToggleInfoOverViewVisible = (hide: boolean, height: number) => {
	if (hide) {
		infoOverViewHeight.value = height
	} else {
		infoOverViewHeight.value = 0
	}
	xlsxPlusRef.value.$resize()
	setTimeout(() => {
		showProgress.value = !hide
	}, 100)
}

const getFillData = async () => {
	if (!currentCheckedLedger.value) {
		ElMessage.warning('请选择业务表')
		return
	}

	if (ledgerType.value === ReportType.STATISTICS) {
		if (!ledgerConfigMappping.value) {
			ElMessage.warning('请先配置统计表的维度和计算规则后再填充数据')
			return
		}
	}

	const el = document.querySelector('.el-main.component-view') as HTMLElement

	let reslut = null
	const ledgerId = currentCheckedLedger.value.ledgerId
	const ledgerConfig = await getCurrentLedgerConfig(ledgerId)

	if (ledgerConfig.data.length > 0) {
		// 有配置
		reslut = await getLedgerTableListByLedgerConfig(ledgerId, {
			QueryConfigId: ledgerConfig.data[0].id,
		})
	} else {
		// 无配置
		const selectFields: any = {}
		linkedFields.value.forEach((f: any) => {
			selectFields[f.name] = f.name
		})
		reslut = await getLedgerTableList({ledgerId, selectFields})
	}

	// 处理数据
	let linked: any = []
	const celldata: any = []
	if (ledgerType.value === ReportType.DETAIL) {
		const len = linkedFields.value.length
		for (let i = 0; i < len; i++) {
			const field = linkedFields.value[i]
			const {r, c} = field.__position
			const values = reslut.map((m: any, number: number) => ({
				r: number + headerEndRow.value,
				c,
				v: m[field.name],
			}))
			celldata.push(...values)
		}
		linked = toRaw(linkedFields.value)
	} else if (ledgerType.value === ReportType.STATISTICS) {
		const keys = Object.keys(ledgerConfigMappping.value)
		for (const key of keys) {
			const postion = JSON.parse(ledgerConfigMappping.value[key])
			const {_row, column} = postion[0]
			const values = reslut.map((m: any, number: number) => ({
				r: number + headerEndRow.value,
				c: column[0],
				v: m[key],
			}))
			celldata.push(...values)
			linked.push({
				__columnRaw: postion,
			})
		}
	}

	if (linked.length === 0) {
		ElMessage.warning('请先关联字段后再填充数据')
		return
	}

	if (celldata.length === 0) {
		ElMessage.warning('没有可以填充的数据')
		return
	}

	setTimeout(() => {
		xlsxPlusRef.value.$setData(celldata, linked, () => {
			// if (!isFull.value) {
			// 	el.style.zIndex = '2'
			// }
		})
	}, 451)
}
console.log(colData, tableData.value)

const onClickAutoBind = async () => {
	if (!currentCheckedLedger.value) {
		ElMessage.warning('请选择业务表')
		return
	}
	// console.log(
	// 	currentCheckedLedger.value,// 选中业务表信息
	// 	currentReportTableId.value,// 表格ID
	// 	ledgerFields.value, // 表格ID
	// 	statisticsFields.value, // 获取业务表列表fields
	// 	ledgerType.value, // 0:明细;1:统计
	// 	sheets.value,
	// 	curReport.value
	// )
	let fieldsArr = ledgerType.value == 0 ? ledgerFields.value : statisticsFields.value
	let fieldsIndex = ledgerFields.value.findIndex(
		(item: any) => item.id === currentCheckedLedger.value.ledgerId
	)
	let data = fieldsArr[fieldsIndex].fields
	let uppercaseLetters = Array.from({length: 26}, (_, index) => String.fromCharCode(index + 65))
	const sheet = (sheets.value[0] as any).head
	bindMappingstype.value = false
	for (let i = 0; i < data.length; i++) {
		dragField = null
		for (let v = 0; v < sheet.length; v++) {
			var index = data[i].displayName.indexOf('【')
			if (index !== -1) {
				data[i]._displayName = data[i].displayName.substring(0, index) // 返回从开头到指定字符之前的子字符串
			} else {
				data[i]._displayName = data[i].displayName
			}
			if (data[i]._displayName == (sheet[v] as any).v.m && !data[i].__checked) {
				if (i + 1 == data.length) {
					bindMappingstype.value = true
				}
				await onLedgerFieldDragToCell(
					uppercaseLetters[(sheet[v] as any).c] + 1,
					sheet[v],
					true,
					data[i],
					bindMappingstype.value
				)
			}
		}
	}
}

const onDeleteDimension = (field: any) => {
	console.log('onDeleteDimension', field)
}
// 提交操作弹窗控制
const submitModalIsVisible = ref(false)
// 提交
const dataAuditWorkflowSchemeCode = ref('')
const dataAuditProps = ref([
	{
		title: '数据审核流程',
		field: 'dataAuditWorkflowSchemeCode',
	},
])
const submitformArray = ref([
	// {
	// 	type: 'select',
	// 	title: '分管领导',
	// 	field: 'auditorId',
	// 	data: checkLeaderList.value,
	// },

	{
		type: 'textarea',
		title: '情况说明',
		field: 'description',
		filterable: true,
	},
])
function submit() {
	if (isLockReport.value) {
		return
	}
	const isDataLeader = userStore.getUserInfo.staffRole.includes(USER_ROLES_ENUM.DATA_LEADER)
	ElMessageBox({
		title: '确认',
		message: `${
			isDataLeader
				? '当前是数据管理岗填报环节，数据提交后将直接汇总到上一级部门，无需本部门审核。'
				: ''
		}未保存的表格内容和未上传成功的附件将不会提交，请确认当前所有表格均已点击“保存”，且所有附件已上传成功。`,
		showCancelButton: true,
	}).then(() => {
		submitModalIsVisible.value = true
	})
}
const realSubmit = async (val: any) => {
	// if (isLader) {
	// 	if (!dataAuditWorkflowSchemeCode.value) {
	// 		ElMessage.warning('请选择数据审核流程')
	// 		return
	// 	} else {
	// 		val.dataAuditWorkflowSchemeCode = dataAuditWorkflowSchemeCode.value
	// 	}
	// }

	// if (
	// 	(_detailInfo.value.status === 10 || _detailInfo.value.status === 3) &&
	// 	_detailInfo.value.parentAreaOrganizationUnitId !== null
	// ) {
	// 	// 内部填报提交
	// 	axios
	// 		?.post(
	// 			`/api/filling/report-task-ou/Inner-audit?ReportTaskId=${reportId}&AreaOrganizationUnitId=${areaOrganizationUnitId}`,
	// 			{
	// 				description: val.description,
	// 				attachments: imgFile.value,
	// 				// dataAuditWorkflowSchemeCode: val.dataAuditWorkflowSchemeCode,
	// 			}
	// 		)
	// 		.then(async (res: any) => {
	// 			ElNotification.success({
	// 				message: '已提交至分管领导审核',
	// 				duration: 2000,
	// 			})
	// 			submitModalIsVisible.value = false
	// 			router.back()
	// 		})
	// 		.catch((err) => {
	// 			window.errMsg(err, '提交')
	// 		})
	// } else {
	// 	// 提交
	// 	axios
	// 		?.post(`/api/filling/report-task-ou/${reportId}/${areaOrganizationUnitId}/submit`, {
	// 			description: val.description,
	// 			attachments: imgFile.value,
	// 		})
	// 		.then(async (res: any) => {
	// 			ElNotification.success({
	// 				message: '提交成功',
	// 				duration: 2000,
	// 			})
	// 			submitModalIsVisible.value = false
	// 			router.back()
	// 		})
	// 		.catch((err) => {
	// 			window.errMsg(err, '提交')
	// 		})
	// }

	PushReportFlowTask(
		{description: val.description, attachments: imgFile.value},
		currentInfo.value.reportTaskId,
		currentInfo.value.areaOrganizationUnitId
	)
		.then(() => {
			ElMessage.success('提交成功')
			submitModalIsVisible.value = false
			router.go(-1)
		})
		.catch((err: any) => window.errMsg(err, '提交'))
}
const formSubmit = async (val: any, formRef: FormInstance | undefined) => {
	// 获取内部填报还是下发填报
	// 内部填报
	// await realSubmit(val)
	// if (
	// 	_detailInfo.value.reportTaskAreaOrganizationUnitFillers.length !== 0 &&
	// 	_detailInfo.value.reportTaskAreaOrganizationUnitFillers.findIndex(
	// 		(v: any) => v.reportTaskAreaOrganizationUnitFillerStatus !== 2
	// 	) !== -1
	// ) {
	// 	const updateParams = {
	// 		reportTaskId: reportId,
	// 		areaOrganizationUnitId,
	// 		reportTaskAreaOrganizationUnitFillerStatus: 2,
	// 		attachments: imgFile.value,
	// 	}
	// 	const res = await updateFillerStatus(updateParams)

	// 	console.log(res)
	// 	if (res.status === 200 || res.status === 204) {
	// 		// 获取内部多人填报列表
	// 		const reportTaskAreaOrganizationUnitFillers =
	// 			_detailInfo.value.reportTaskAreaOrganizationUnitFillers
	// 		// 获取未填人员的列表
	// 		const penddingWriteUser = reportTaskAreaOrganizationUnitFillers.filter(
	// 			(item: any) =>
	// 				item.reportTaskAreaOrganizationUnitFillerStatus == FillerFillingState.PendingWrite ||
	// 				item.reportTaskAreaOrganizationUnitFillerStatus == FillerFillingState.Rejected
	// 		)
	// 		// 判断是否只有一条未填数据，且当条未填报数据的人员是当前登录人
	// 		if (
	// 			penddingWriteUser.length === 1 &&
	// 			penddingWriteUser[0].fillerId ===
	// 				JSON.parse(localStorage.getItem('currentUserInfo') as string).id
	// 		) {
	// 			await realSubmit(val)
	// 		} else {
	// 			ElNotification.success({
	// 				message: '提交成功',
	// 				duration: 2000,
	// 			})
	// 			router.back()
	// 		}
	// 		submitModalIsVisible.value = false
	// 	}
	// } else {
	// 	await realSubmit(val)
	// }
	await realSubmit(val)
}

const departmentShow = ref(false)
const getReForwardList = ref<any[]>([])
const reForword = () => {
	departmentShow.value = true
}
const handleCheckReForward = (e: any[]) => {
	getReForwardList.value = e
}
const reRequest = ref(false)
const submitToReForward = () => {
	if (getReForwardList.value.length === 0) {
		return ElNotification.warning('请选择要转发的部门')
	}
	const params = getReForwardList.value.map((v) => v.id)
	axios
		.post(
			`/api/filling/report-task-ou/again-issue?ReportTaskId=${reportId}&AreaOrganizationUnitId=${areaOrganizationUnitId}`,
			[...params]
		)
		.then(async (res) => {
			departmentShow.value = false
			reRequest.value = true
			await getReportInfo()
			if (!_excel_canview.value) {
				await getChildrenReportTask({
					reportTaskId: _detailInfo.value.reportTaskId,
					areaOrganizationUnitId: _detailInfo.value.areaOrganizationUnitId,
					id: _detailInfo.value.id,
				})
			}

			setTimeout(() => {
				reRequest.value = false
			}, 1000)
		})
}
const tableOffsetHeight = ref(0)

const deleteLockReportTable = () => {
	if (lockReportTable.value?.userId === userStore.getUserInfo?.id) {
		DeleteLockReprotTable(lockReportParams.value).then(() => {
			lockReportTable.value = null
			lockReportParams.value = null
		})
	}
}

const showViewFlow = ref(false)
const viewFlowCode = ref('')
const onViewFlow = (code: string | null) => {
	if (!code) {
		ElMessage.warning('请选择一个流程')
		return
	}
	showViewFlow.value = true
}

const onJumpCreateFlow = () => {
	router.push({
		path: '/flow/step',
		query: {
			category: '临时报表流程',
		},
	})
}

const onTurnFormChange = (val: any, formItem: any) => {
	console.log(val, formItem)

	if (formItem.prop === 'model') {
		treeSelectValue.value = []
		turnFormRef.value.setValue('fillUsers', [])
		turnFormProps.value[1].label = `填报${val === 1 ? Tips.Personnel : Tips.Department}`
	}

	if (formItem.prop === 'turnFlow' || formItem.prop === 'auditFlow') {
		viewFlowCode.value = val
	}
}

const filterMethod = (query: string) => {
	if (query) {
		loading.value = true
		defHttp
			.request({
				method: 'get',
				url: `/api/platform/department/department-bind-users?filter=${query}`,
				headers: {
					Urlkey: 'iframeCode',
				},
			})
			.then((res: any) => {
				const {data} = res

				const arr = data.items
					.map((v: any) => ({
						...v,
						label: v.name,
						name: v.department?.parent?.name + '-' + v.department?.name + '-' + v.name,
						value: v?.department?.id + '/' + v.id,
						departmentId: v?.department?.id,
						isLeaf: true,
						disabled: false,
					}))
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
				treeSelectData.value = useArrayToTree(
					arr,
					'id',
					'parentId',
					'name',
					true,
					'children'
				) as any
				loading.value = false
			})
	} else {
		GetDepartmentChildrenFromTurnTask()
	}
}

const GetDepartmentChildrenFromTurnTask = async () => {
	defHttp
		.request({
			method: 'get',
			url: '/api/platform/departmentInternal/get-department-children',
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((res) => {
			const {data} = res
			const arr = data.map((v: any) => ({
				...v,
				disabled: v?.department?.departmentId ? false : true,
				children: v.children === null ? [] : v.children,
			}))
			treeSelectData.value = useArrayToTree(
				arr,
				'id',
				'parentId',
				'name',
				true,
				'children'
			) as any
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}

const onTreeSelectChange = (val: any) => {
	turnFormRef.value.setValue('fillUsers', val)
}

const departmentListChange = (e: any) => {
	turnFormRef.value.setValue(
		'fillUsers',
		e.map((v: any) => v.id)
	)
}

const onScrollBottom = () => {
	currLedgerScrollCount.value += 1
	getLedgerList()
}
const onSearch = (val: any) => {
	currLedgerScrollCount.value = 1
	filterValue.value = val
	// getLedgerList()
	// getReportTabs()

	ledgerList.value.length = 0
	ledgerFields.value.length = 0
	if (route.query.type === 'edit' && !isAlien.value) {
		if (ledgerType.value === ReportType.STATISTICS) {
			getStatisticsLinked(() => getLedgerList())
		} else {
			getLinkedList((reports: Array<any>) => getLedgerList(reports))
		}
	}
}

const logList: any = ref([])
const getLogList = async (processId: string) => {
	const res = await GetWorkflowProcess(processId)

	const logs = res.data.logs
	const tasks = res.data.tasks.filter(
		(v: any) =>
			(v.type === 1 || v.type === 5 || v.type === 9) &&
			(v.state === 1 || v.state === 5 || v.state === 3)
	)

	if (tasks.length > 0) {
		logList.value = useFlowRecord().toList(
			[
				...logs,
				{
					unitName: tasks[0].unitName,
					des: '正在审核',
					userName: Array.from(new Set(tasks.map((v: any) => v.userName))).join(','),
					creationTime: tasks[0].creationTime,
				},
			],
			['unitName', 'des', 'userName', 'creationTime']
		) as any
		console.log('流程记录', res, logList.value)
	}
}

const getReportLogList = (code: string) => {
	if (code) {
		GetReportLogList(code).then((res) => {
			const {data} = res
			const flowList = [
				{
					label: '数据审核',
					text: `审核人员: ${data.map((x: any) => x.name).join(',')}`,
					user: '',
					time: '',
				},
			]
			const stauts: any = {
				1: '待填写',
				2: '已提交',
				3: '已驳回',
			}
			const auditList = currentReportData.value.reportTaskAreaOrganizationUnitFillers.map(
				(x: any) => ({
					label: stauts[x.reportTaskAreaOrganizationUnitFillerStatus],
					text: '',
					time: x.creationTime,
					user: x.filler.name,
				})
			)

			logList.value = [...auditList, ...flowList]
		})
	}
}

const businessProcessData: any = ref({})
const getBusinessProcess = () => {
	const from = route.query.from
	if (from && from === 'taskPending') {
		GetReportFillProcess({
			id: Id,
		})
			.then((res: any) => {
				businessProcessData.value = res.data
			})
			.catch((err: any) => {
				window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
			})
	} else {
		GetPlanTaskProcessDetail({
			reportTaskId: reportId,
			areaOrganizationUnitId: organizationId,
			id: Id,
			isShowNextUnit: false,
		})
			.then((res: any) => {
				businessProcessData.value = res.data
			})
			.catch((err: any) => {
				window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
			})
	}
}
const wthdraw = ref()
const wthdrawState = () => {
	axios
		?.get(
			`/api/filling/report-task-ou/canbe-revoke?ReportTaskId=${route.query.reportTaskId}&AreaOrganizationUnitId=${route.query.areaOrganizationUnitId}`
		)
		.then((res) => {
			wthdraw.value = res.data
		})
}

// 上传附件功能
const uploadModelVisible = ref(false)
const uploadModelOpen = () => {
	if (isLockReport.value) {
		return
	}
	uploadModelVisible.value = true
}
const imgFile = ref<any[]>([])
const upload = ref()
const beforeUpload = (file: any) => {
	console.log(222, '上传之前')

	// debugger
	const limitSize = 20971520
	// console.log(imgFile.value)
	const arr = JSON.parse(JSON.stringify(imgFile.value))
	console.log(arr)
	console.log(file)

	const alSize =
		arr.length === 0
			? file.size
			: arr.length === 1
			? arr[0].size + file.size
			: arr.map((v: any) => v.size).reduce((a: any, b: any) => a + b) + file.size

	console.log(alSize)
	if (alSize > limitSize) {
		ElNotification.warning('上传文件大小不能超过20M')
		upload.value?.handleRemove(file)
		return false
	}
	// return true
}
const customUpload = async (options: any) => {
	console.log(111, '开始上传')

	const res = await uploadFile(options.file, 'reportTask')
	if (res) {
		imgFile.value.push({
			objectId: options.file.uid.toString(),
			name: options.file.uid.toString() + '-' + options.file.name,
			path: `/api/files/public/p${res}`,

			extension: options.file.type,
			size: options.file.size,
		})
		console.log(imgFile.value)
	}
}
const uploadModelConfirm = () => {
	uploadModelVisible.value = false
}
const uploadModelCancel = () => {
	// imgFile.value = []
	// upload.value?.clearFiles()
	uploadModelVisible.value = false
}
const onRemoveFile = (file: any) => {
	console.log('删除文件', file, imgFile.value)

	const findIndex = imgFile.value.findIndex(
		(item: any) => item.objectId == file.uid || item.uid == file.uid
	)

	if (findIndex !== -1) imgFile.value.splice(findIndex, 1)
	// debugger
}

// 转发功能
const loading = ref(false)
const turnFormRef: any = ref()
const turnForm: any = ref({model: 1, fillUsers: []})
const treeSelectData = ref([])
const treeSelectValue = ref([])
const currentInfo: any = ref(null)
const turnFormProps = ref([
	{
		prop: 'model',
		label: '填报模式',
		type: 'select',
		options: [
			{label: '内部填报', value: 1},
			{label: '下发填报', value: 2},
		],
	},
	{prop: 'fillUsers', label: '填报人员'},
])
const turnFormPropsOther = [
	// {
	// 	prop: 'turnFlow',
	// 	label: '转发流程',
	// 	type: 'selectRemote',
	// 	remoteUrl: '/api/workflow/workflowSchemeInfo',
	// 	remoteParams: {enabledMark: true, category: FlowType.Fill},
	// 	remoteFilterKey: 'keyword',
	// 	remoteValue: 'code',
	// 	remoteInit: true,
	// },
	{
		prop: 'auditFlow',
		label: '本部门数据审核流程',
		type: 'selectRemote',
		remoteUrl: '/api/workflow/workflowSchemeInfo',
		remoteParams: {enabledMark: true, category: FlowType.Fill},
		remoteFilterKey: 'keyword',
		remoteValue: 'code',
		remoteInit: true,
		labelWidth: 165,
	},
]

const forwardModalIsVisible = ref(false)
const staffId = ref<any>(null)
const store = useUserStore()
const taskManageStore = useTaskManageStore()
const visibleForwordButton = computed(() => {
	return (
		store.userInfo?.staffRole.includes(STAFFROLEARRAY[4]) &&
		_detailInfo.value?.status !== 10 &&
		_detailInfo.value?.status !== 9 &&
		_detailInfo.value?.status !== 4 &&
		!_detailInfo.value?.stop
	)
})
// 下发填报列表
const checkDepartmentList = computed(() => taskManageStore.checkDepartmentList)
// 审核领导列表
const checkLeaderList = ref([])
// 填报员工列表
const checkStaffList = ref([])
const selectDepartmentGroup = ref<any[]>([])
const forwardformArray = ref([
	{
		full: true,
		type: 'select',
		title: '填报选择',
		field: 'fillingMode',
		data: [
			{label: '内部填报', value: 1, disabled: false},
			{
				label: '下发填报',
				value: 2,
				disabled: userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[0]),
			},
		] as any,
		showCheckBox: false,
		filterable: true,
		default: null,
	},
])
const forwardFormRules = reactive<FormRules>({
	fillingMode: [{required: true, message: '请选择填报方式', trigger: 'change'}],
	select: [{required: true, message: '请选择', trigger: 'change'}],
	// staffs: [{required: true, message: '请选择', trigger: 'blur'}],
	auditorId: [{required: true, message: '请选择审核领导', trigger: 'change'}],
})
const getSameOrganizationStaff = () => {
	axios
		.request({
			method: 'get',
			url: `/api/platform/departmentInternal/department-extend-bind-users`,
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((users: any) => {
			checkStaffList.value =
				users.data
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
					.map((res: any) => ({
						label: res.name,
						value: res.id,
					})) ?? []
			checkLeaderList.value =
				users.data
					.filter((user: any) => user.staffRole.includes(STAFFROLEARRAY[1]))
					.map((res: any) => ({
						label:
							res.department?.region?.name +
							'-' +
							res.department?.parentName +
							'-' +
							res.department?.name +
							'- ' +
							res.name,
						value: res?.department?.id + '/' + res.id,
					})) ?? []
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}
const getDepartmentChildren = async () => {
	axios
		.request({
			method: 'get',
			url: '/api/platform/departmentInternal/get-department-children',
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((res: any) => {
			const {data} = res
			const arr = data.map((item: any) => ({
				...item,
				value: item?.department?.departmentId + '/' + item.id,
				checked: false,
				children: item.children === null ? [] : item.children,
				disabled: item?.department?.departmentId ? false : true,
			}))
			selectDepartmentGroup.value = useArrayToTree(
				arr,
				'id',
				'parentId',
				'name',
				true,
				'children'
			)
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}
const treeRefs = ref()
const props = {
	label: 'label',
	value: 'value',
	isLeaf: 'isLeaf',
	id: 'id',
}
const loadNode = (node: any, resolve: any) => {
	if (node.data.length === 0) return
	axios
		.request({
			method: 'get',
			url: `/api/platform/departmentInternal/${node.data.id}/bind-users`,
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((users: any) => {
			const userList =
				users.data.items
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
					.map((res: any) => ({
						label: res.name,
						value: res?.department?.id + '/' + res.id,
						id: res?.department?.id + '/' + res.id,
						departmentId: res?.department?.id,
						isLeaf: true,
						disabled: res?.department?.id ? false : true,
					})) ?? []
			resolve(node.data.children.concat(userList))
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}
const handleCurrentChange = (node: any) => {
	console.log(node)
	console.log(forwardformArray.value)

	// currentCheckUsersDepartment.value = node.departmentId

	forwardformArray.value.forEach((e) => {
		if (e.field === 'staffs') {
			e.default = node.id
		}
	})
}
const forWord = () => {
	forwardModalIsVisible.value = true
}
// 设置转发-内部转发和下发转发
const forwardFormChange = (val: any, type: string) => {
	if (type === 'fillingMode' && val === 1) {
		selectDepartmentGroup.value = []
		getDepartmentChildren()

		// 内部填报
		forwardformArray.value = [
			{
				full: true,
				type: 'select',
				title: '填报选择',
				field: 'fillingMode',
				showCheckBox: false,
				data: [
					{label: '内部填报', value: 1, disabled: false},
					{
						label: '下发填报',
						value: 2,
						disabled: userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[0]),
					},
				],
				filterable: true,
				default: val,
			},
			{
				full: true,
				type: 'select',
				title: '填报人员',
				field: 'staffs',
				showCheckBox: false,
				data: checkStaffList.value,
				filterable: true,
				default: null,
			},
			{
				full: true,
				type: 'select',
				title: '数据审核人',
				field: 'auditorId',
				showCheckBox: false,
				data: checkLeaderList.value,
				filterable: true,
				default: null,
			},
		]
		// getDepartmentChildren()
	}
	if (type === 'fillingMode' && val === 2) {
		// 下发填报
		forwardformArray.value = [
			{
				full: true,
				type: 'select',
				title: '填报选择',
				field: 'fillingMode',
				showCheckBox: false,
				data: [
					{label: '内部填报', value: 1, disabled: false},
					{
						label: '下发填报',
						value: 2,
						disabled: userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[0]),
					},
				],
				filterable: true,
				default: val,
			},
			{
				full: true,
				type: 'departmentSelect',
				title: '填报部门',
				field: 'select',
				showCheckBox: true,
				data: checkDepartmentList.value,
				filterable: true,
				default: null,
			},
			// {
			// 	type: 'select',
			// 	title: '审核领导',
			// 	field: 'auditorId',
			// 	showCheckBox: false,
			// 	data: checkLeaderList.value,
			// 	filterable: true,
			// 	default: null,
			// },
			{
				full: true,
				type: 'textarea',
				title: '填报说明',
				field: 'description',
				filterable: true,
				showCheckBox: false,
				data: null,
				default: null,
			},
		]
	}
	// if(Array.isArray(val)){

	// }
}
const forwardFormClean = () => {
	staffId.value = null
}

const forwardSubmit = (val: any, formRef: FormInstance | undefined) => {
	// // 表单验证
	// turnFormRef.value
	// 	?.validate()
	// 	.then(async (validate: boolean) => {
	// 		console.log(validate, val)

	// 		// 内部填报
	// 		if (val.modal === 1) {
	// 			// 判断填报人员是否有值
	// 			if (staffId.value === null || staffId.value.length === 0 || staffId.value === undefined) {
	// 				return ElNotification.warning('请选择填报人员')
	// 			}
	// 			console.log(staffId.value)
	// 			let col: any = {}
	// 			staffId.value.forEach((e: any) => {
	// 				col[e.split('/')[1]] = e.split('/')[0]
	// 			})
	// 			const res = await axios?.put(
	// 				`/api/filling/report-task-ou/${reportId}/${areaOrganizationUnitId}/set-internal-staff`,
	// 				{
	// 					staffs: col,
	// 					auditorId: val.auditorId.split('/')[1],
	// 					// ActualAreaOrganizationUnitId: val.staffs.split('/')[0],
	// 					ActualAuditorAreaOrganizationUnitId: val.auditorId.split('/')[0],
	// 				}
	// 			)
	// 			if (res) {
	// 				ElNotification.success({
	// 					message: '转发成功',
	// 					duration: 2000,
	// 				})
	// 				store.getTodoListCount()
	// 				router.go(-1)
	// 			}
	// 		} else {
	// 			// 下发填报
	// 			const res = await axios?.post(
	// 				`/api/filling/report-task-ou/DataLeader-issue?ReportTaskId=${reportId}&AreaOrganizationUnitId=${areaOrganizationUnitId}`,
	// 				{
	// 					areaOrganizationUnitIds: val.select.map((v: any) => v.id),
	// 					// auditorId: val.auditorId,
	// 					auditDescription: val.description,
	// 				}
	// 			)
	// 			if (res) {
	// 				ElNotification.success({
	// 					message: '转发成功',
	// 					duration: 2000,
	// 				})
	// 				store.getTodoListCount()
	// 				router.go(-1)
	// 			}
	// 		}
	// 		forwardModalIsVisible.value = false
	// 	})
	// 	.catch(() => {
	// 		console.log('验证失败')
	// 	})

	if (turnForm.value.fillUsers.length === 0) {
		ElMessage.warning(
			`请选择填报${turnForm.value.model === 1 ? Tips.Personnel : Tips.Department}`
		)
		return
	}

	if (!currentInfo.value) return

	const staffs: any = {}
	const {reportTaskId, areaOrganizationUnitId} = currentInfo.value
	const success = () => {
		ElMessageBox.confirm(
			'你已将任务转发其他部门填报,可在"临时报表-报表创建-我的转发"查看任务填报情况',
			'已转发',
			{
				confirmButtonText: '前往查看',
				cancelButtonText: '确认',
				type: 'success',
			}
		)
			.then(() => {
				router.push({
					path: '/statementTask',
					query: {
						from: 'turn',
					},
				})
			})
			.catch(() => {
				router.go(-1)
			})
	}

	turnForm.value.fillUsers.forEach((v: any) => {
		const split = v.split('/')
		staffs[split[1]] = split[0]
	})

	if (turnForm.value.model === 1) {
		// 内部填报
		let hasWorkFlow: any = {}
		if (currentInfo.value.hasDataAuditWorkflow) {
			hasWorkFlow = {
				transpondAuditWorkflowSchemeCode: turnForm.value.turnFlow,
				dataAuditWorkflowSchemeCode: turnForm.value.auditFlow,
			}
		}

		SetInternalStaff({staffs, ...hasWorkFlow}, reportTaskId, areaOrganizationUnitId)
			.then(() => {
				ElMessage.success('转发成功')
				forwardModalIsVisible.value = false
				success()
			})
			.catch((err: any) => window.errMsg(err, '转发'))
	} else if (turnForm.value.model === 2) {
		// 下发填报
		IssueDataLeader({
			data: {
				areaOrganizationUnitIds: Object.values(turnForm.value.fillUsers),
				auditDescription: '',
				transpondAuditWorkflowSchemeCode: turnForm.value.turnFlow,
				dataAuditWorkflowSchemeCode: turnForm.value.auditFlow,
			},
			params: {
				reportTaskId,
				areaOrganizationUnitId,
			},
		})
			.then((res: any) => {
				ElMessage.success('转发成功')
				forwardModalIsVisible.value = false
				success()
			})
			.catch((err: any) => window.errMsg(err, '转发'))
	}
}

const exportData = ref()

// 导出表格模版
const downloadCurrentSheetTemplate = () => {
	// ;
	// if (_detailInfo.value?.reportTaskAreaOrganizationUnitFillers.length <= 1) {
	// 	;
	// }
	;(sheets.value[0] as any).data = []
	exportData.value = sheets.value
	console.log(sheets.value)
	exportRef.value?.onClick(exportData.value)
	setTimeout(() => {
		exportData.value = null
	}, 300)
}
onMounted(async () => {
	// TODO: 取消报表填报锁定
	lockReportParams.value = {
		reportTaskId: route.query.reportTaskId,
		areaOrganizationUnitId: route.query.areaOrganizationUnitId,
	}

	const lock = await GetLockReprotTable(lockReportParams.value)

	if (
		lock.data &&
		lock.data?.userId !== userStore.getUserInfo.id &&
		route.query.type === 'edit'
	) {
		isLockReport.value = true
		message = ElMessage.warning({
			duration: 0,
			message: `用户 ${lock.data.userName}/${lock.data.name} 正在编辑, 报表已被锁定且无法保存`,
		})
	} else if (route.query.type === 'edit') {
		await SetLockReprotTable(lockReportParams.value)
		isLockReport.value = false
	}

	lockReportTable.value = lock.data
	if (!lockReportTable.value) {
		lockReportTable.value = {userId: userStore.getUserInfo.id}
	}

	userStore.setBeforeLogoutHandle({
		handle: () => {
			return new Promise((resolve) => {
				console.log(
					'setBeforeLogoutHandle',
					lockReportTable.value,
					userStore.getUserInfo.id
				)

				if (lockReportTable.value?.userId === userStore.getUserInfo.id) {
					DeleteLockReprotTable(lockReportParams.value).then(() => {
						lockReportTable.value = null
						lockReportParams.value = null
						resolve(true)
					})
				} else {
					resolve(true)
				}
			})
		},
	})

	window.addEventListener('beforeunload', deleteLockReportTable)

	tableOffsetHeight.value = Math.ceil(titleList.value.length / 4) * 40 + 15
	await getReportInfo()
	getSameOrganizationStaff()
	if (!_excel_canview.value) {
		await getChildrenReportTask({
			reportTaskId: _detailInfo.value.reportTaskId,
			areaOrganizationUnitId: _detailInfo.value.areaOrganizationUnitId,
			id: _detailInfo.value.id,
		})
	}

	getReportTabs()
	getBusinessProcess()
	if (!route.query.from) return
	wthdrawState()
})

onBeforeUnmount(() => {
	message && message.close()
	deleteLockReportTable()
	window.removeEventListener('beforeunload', deleteLockReportTable)
})

onUnmounted(() => {})
</script>
<template>
	<div class="report-task-detail">
		<div
			v-show="!showProgress"
			class="open-history"
			@click=";(showProgress = true), xlsxPlusRef.$resize()"
		>
			<span
				>展开流程记录
				<i
					class="icon i-ic-baseline-keyboard-double-arrow-right"
					style="position: relative; left: -1px"
				></i
			></span>
		</div>

		<div v-show="showProgress" class="right">
			<Block
				ref="blockHistoryRef"
				title="流程记录"
				:enable-back-button="false"
				:enable-expand-content="false"
				:enable-fixed-height="true"
				:enable-close-button="false"
			>
				<template #topRight>
					<span
						style="
							cursor: pointer;
							display: flex;
							align-items: center;
							color: var(--z-main);
						"
						@click=";(showProgress = false), xlsxPlusRef.$resize()"
					>
						<i class="icon i-ic-baseline-keyboard-double-arrow-left"></i>
						收起
					</span>
				</template>
				<BusinessProcess
					:data="businessProcessData"
					:isDetail="true"
					:isTodo="true"
					:type="route.query.type"
				></BusinessProcess>

				<!-- <Record v-if="logList.length > 0" sort="asc" :data="logList"></Record>
				<div v-else class="df aic jcc pd-20" style="line-height: 1.5">
					未设置数据审核流程<br />数据直接提交至收集部门
				</div> -->

				<!-- <scheduleProgressComp
					:request="reRequest"
					:reportTaskId="reportId"
					:areaOrganizationUnitId="organizationId"
					v-if="
						(route.query.type === 'detail' || route.query.type === 'edit') &&
						_detailInfo?.status !== 10 &&
						_detailInfo?.status !== 9 &&
						_detailInfo?.status !== 4
					"
					:type="2"
					:title="'内部填报'"
					:fillingMode="2"
				></scheduleProgressComp>
				<scheduleProgressComp
					:request="reRequest"
					v-if="!(route.query.type === 'edit')"
					:reportTaskId="reportId"
					:areaOrganizationUnitId="organizationId"
					:type="2"
					:title="'下发填报'"
					:fillingMode="1"
				></scheduleProgressComp> -->
			</Block>
		</div>

		<div class="left" style="width: calc(100% - 300px)">
			<Block
				ref="blockRef"
				title="任务详情"
				:expandContent="false"
				:enableExpandContent="false"
			>
				<template
					#topRight
					v-if="route.query.type === 'edit' && !_detailInfo?.stop && !isLockReport"
				>
					<el-badge
						v-if="imgFile.length > 0"
						:value="imgFile.length"
						:max="99"
						class="item mg-right-10"
					>
						<el-button size="small" type="primary" @click="uploadModelOpen"
							>上传附件</el-button
						>
					</el-badge>
					<el-button
						class="mg-left-10"
						size="small"
						v-else
						type="primary"
						@click="uploadModelOpen"
					>
						上传附件
					</el-button>

					<el-button size="small" type="primary" @click="submit">提交</el-button>
					<el-button
						size="small"
						v-if="
							visibleForwordButton &&
							currentInfo?.status !== ReportsFlowStatusEnum.Issued
						"
						type="primary"
						@click="forWord"
						>转发</el-button
					>
				</template>
				<template #topRight v-if="wthdraw">
					<el-button
						size="small"
						type="warning"
						@click="wthdrawClick"
						:loading="isPushing"
					>
						撤回
					</el-button>
				</template>
				<template #topRight v-if="route.query.type === 'audit' && !isLockReport">
					<el-button
						ml-10px
						size="small"
						type="warning"
						@click="audit(FlowAuditCode.Disagree)"
						:loading="isPushing"
						>驳回</el-button
					>
					<el-button
						size="small"
						type="primary"
						@click="audit(FlowAuditCode.Agree)"
						:loading="isPushing"
						>通过
					</el-button>
				</template>

				<div class="info-over-view">
					<InfoOverViewComp
						:data="_detailInfo"
						:titles="titleList"
						@onToggleVisible="onToggleInfoOverViewVisible"
					/>
				</div>

				<el-tabs
					v-model="reportTabId"
					type="card"
					@tab-click="onChangeReport"
					v-if="_excel_canview && _detailInfo?.status !== 4 && _detailInfo?.status !== 7"
				>
					<el-tab-pane
						v-for="item of (reportTabs as any)"
						:label="item?.displayName"
						:name="item?.id"
					></el-tab-pane>
				</el-tabs>

				<div
					class="excel-box"
					v-if="_excel_canview"
					:class="{full: isFull}"
					:style="{height: `calc(100% - 192px + ${infoOverViewHeight}px)`}"
				>
					<XlsxPlusComp
						class="xlsx-plus-component"
						v-if="_detailInfo?.status !== 4 && _detailInfo?.status !== 7"
						ref="xlsxPlusRef"
						:isAlien="isAlien"
						:enableSave="true"
						:enableAutoSave="false"
						:enableImport="!importType"
						:showAside="importType && route.query.type === 'edit' && !isAlien"
						:sheets="sheets"
						:linked="linkedFieldRaw"
						@onAutoSave="onAutoSave"
						@onImportFinish="delAllReportTableList"
						@onAddRow="onXlsxPlusAddRow"
						@onDeleteRow="onXlsxPlusDeleteRow"
						@onDragToCell="onLedgerFieldDragToCell"
						@onSave="onXlsxPlusSave"
						:viewonly="route.query.type === 'detail' || route.query.type === 'audit'"
						style="background-color: #fff; height: 800px"
					>
						<template #headerLeft>
							<div class="toggle-import-type" v-if="!isAlien">
								<!-- <el-switch
									v-model="importType"
									size="large"
									width="85"
									inline-prompt
									active-text="业务表导入"
									inactive-text="Excel导入"
									style="--el-switch-on-color: ; --el-switch-off-color: #337ecc"
									@change="onToggleImportType"
								/>
							 -->

								<el-button-group>
									<el-button
										@click="onToggleImportType(true)"
										size="small"
										:type="importType ? 'primary' : 'default'"
										>业务表导入</el-button
									>
									<el-button
										@click="onToggleImportType(false)"
										size="small"
										:type="importType ? 'default' : 'primary'"
									>
										Excel导入
									</el-button>
								</el-button-group>
								<span></span>
							</div>
						</template>
						<template #headerCenter>
							<template v-if="!isAlien">
								<el-button
									v-if="importType"
									type="primary"
									size="small"
									@click="onClickAutoBind"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="15"
										height="15"
										viewBox="0 0 24 24"
										mr-5px
									>
										<path
											fill="currentColor"
											d="M8.09 17H7v-4h3.69c.95-.63 2.09-1 3.31-1h6c.34 0 .67.04 1 .09V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h3.81C8.3 20.12 8 19.09 8 18c0-.34.04-.67.09-1zM13 7h4v4h-4V7zM7 7h4v4H7V7z"
										/>
										<path
											fill="currentColor"
											d="M12.03 17.66c.16-.98 1.09-1.66 2.08-1.66H15c.55 0 1-.45 1-1s-.45-1-1-1h-.83c-2.09 0-3.95 1.53-4.15 3.61A3.998 3.998 0 0 0 14 22h1c.55 0 1-.45 1-1s-.45-1-1-1h-1c-1.21 0-2.18-1.09-1.97-2.34zm7.8-3.66H19c-.55 0-1 .45-1 1s.45 1 1 1h.89c1 0 1.92.68 2.08 1.66c.21 1.25-.76 2.34-1.97 2.34h-1c-.55 0-1 .45-1 1s.45 1 1 1h1c2.34 0 4.21-2.01 3.98-4.39c-.2-2.08-2.06-3.61-4.15-3.61z"
										/>
										<path
											fill="currentColor"
											d="M15 19h4c.55 0 1-.45 1-1s-.45-1-1-1h-4c-.55 0-1 .45-1 1s.45 1 1 1z"
										/>
									</svg>
									自动绑定
								</el-button>
								<!-- 填充数据 -->
								<el-button
									v-if="importType"
									size="small"
									type="primary"
									class="fill-btn mg-right-10"
									@click="getFillData"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="15"
										height="15"
										viewBox="0 0 16 16"
										mr-5px
									>
										<g fill="currentColor">
											<path
												d="M12.5 16a3.5 3.5 0 1 0 0-7a3.5 3.5 0 0 0 0 7Zm.354-5.854l1.5 1.5a.5.5 0 0 1-.708.708L13 11.707V14.5a.5.5 0 0 1-1 0v-2.793l-.646.647a.5.5 0 0 1-.708-.708l1.5-1.5a.5.5 0 0 1 .708 0ZM8 1c-1.573 0-3.022.289-4.096.777C2.875 2.245 2 2.993 2 4s.875 1.755 1.904 2.223C4.978 6.711 6.427 7 8 7s3.022-.289 4.096-.777C13.125 5.755 14 5.007 14 4s-.875-1.755-1.904-2.223C11.022 1.289 9.573 1 8 1Z"
											/>
											<path
												d="M2 7v-.839c.457.432 1.004.751 1.49.972C4.722 7.693 6.318 8 8 8s3.278-.307 4.51-.867c.486-.22 1.033-.54 1.49-.972V7c0 .424-.155.802-.411 1.133a4.51 4.51 0 0 0-4.815 1.843A12.31 12.31 0 0 1 8 10c-1.573 0-3.022-.289-4.096-.777C2.875 8.755 2 8.007 2 7Zm6.257 3.998L8 11c-1.682 0-3.278-.307-4.51-.867c-.486-.22-1.033-.54-1.49-.972V10c0 1.007.875 1.755 1.904 2.223C4.978 12.711 6.427 13 8 13h.027a4.552 4.552 0 0 1 .23-2.002Zm-.002 3L8 14c-1.682 0-3.278-.307-4.51-.867c-.486-.22-1.033-.54-1.49-.972V13c0 1.007.875 1.755 1.904 2.223C4.978 15.711 6.427 16 8 16c.536 0 1.058-.034 1.555-.097a4.507 4.507 0 0 1-1.3-1.905Z"
											/>
										</g>
									</svg>
									填充
								</el-button>
								<el-select
									v-if="importType"
									v-model="ledgerType"
									size="small"
									@change="onToggleReportType"
									style="width: 100px"
								>
									<el-option
										v-for="option of ledgerTypeOptions"
										:label="option.label"
										:value="option.value"
									></el-option>
								</el-select>
								<div
									class="linked-tips"
									v-if="importType && ledgerType === ReportType.STATISTICS"
								>
									<span class="span1"></span>
									<span clas="span2">维度</span>
									<span class="span3"></span>
									<span class="span4">计算</span>
								</div>
							</template>
						</template>
						<template #headerRight>
							<el-button
								v-if="importType && route.query.type === 'edit' && !isAlien"
								size="small"
								type="primary"
								@click="clearAllDataByReportId"
								class="fill-btn"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="15"
									height="15"
									viewBox="0 0 20 20"
								>
									<path
										fill="currentColor"
										fill-rule="evenodd"
										d="M9 2a1 1 0 0 0-.894.553L7.382 4H4a1 1 0 0 0 0 2v10a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V6a1 1 0 1 0 0-2h-3.382l-.724-1.447A1 1 0 0 0 11 2H9ZM7 8a1 1 0 0 1 2 0v6a1 1 0 1 1-2 0V8Zm5-1a1 1 0 0 0-1 1v6a1 1 0 1 0 2 0V8a1 1 0 0 0-1-1Z"
										clip-rule="evenodd"
									/>
								</svg>
								清除数据
							</el-button>

							<el-button
								type="primary"
								size="small"
								style="display: flex; align-items: center"
								class="mg-left-10"
								@click="onFull"
							>
								<template v-if="isFull">
									<i
										i-ic-sharp-fullscreen-exit
										mr-5px
										style="font-size: 18px"
									></i>
									<span>收起</span>
								</template>
								<template v-else>
									<i i-ic-baseline-fullscreen mr-5px style="font-size: 18px"></i>
									<span>全屏</span>
								</template>
							</el-button>

							<el-button
								v-if="!importType && !isAlien"
								type="primary"
								size="small"
								@click="downloadCurrentSheetTemplate"
							>
								<el-icon mr-5px><Download /></el-icon>
								模版下载</el-button
							>
							<div class="excel_tips" v-if="false">
								<!-- <span v-if="route.query.type === 'edit'"
									>提示: 修改后会自动保存, 请勿离开或关闭页面.</span
								> -->
							</div>
						</template>
						<template #aside>
							<!-- 数据关联 -->
							<LedgerFillConfig
								ref="ledgerFillConfigRef"
								:data="ledgerList"
								:fields="ledgerFields"
								:statisticsFields="statisticsFields"
								:ledgerType="ledgerType"
								@onChecked="onLedgerChecked"
								@onDragStart="onLedgerFieldDragStart"
								@onDeleteField="onDeleteLinkedField"
								@onClickSetting="onSettingLedger"
								@onScrollBottom="onScrollBottom"
								@onSearch="onSearch"
							/>
						</template>
					</XlsxPlusComp>

					<ReportFilter
						:visible="ledgerConfig !== null && route.query.type !== 'detail'"
						:title="ledgerConfigCurrent?.ledger?.name"
						:ledger="ledgerConfig"
						:ledgerType="ledgerType"
						:defaultFilterFields="ledgerConfigDefaultFilter"
						:defaultDimensionFields="ledgerConfigDefaultDimension"
						:defalutCalculationFields="ledgerConfigDefaultCalculation"
						@close="onLedgerConfigClose"
						@click-close="onLedgerConfigClose"
						@output="onLedgerConfigSave"
						@deleteDimension="onDeleteDimension"
					></ReportFilter>

					<el-skeleton
						v-if="
							ledgerList === null &&
							route.query.type !== 'audit' &&
							route.query.type !== 'detail'
						"
						:rows="12"
						animated
						style="margin: 15px; width: calc(100% - 30px)"
					/>
				</div>

				<BaseTableComp
					v-if="!_excel_canview"
					:colData="colData"
					:data="tableData"
					:offsetHeight="tableOffsetHeight"
					:checkbox="true"
					:buttons="buttons"
					:total="totalCount"
					:visible-setting="false"
					:visible-search="false"
					:visible-header="true"
					:visible-page="true"
					@click-button="clickButton"
				>
					<!-- <template v-slot:header>
						<div
							flex="~"
							items-center
							w-full
							justify-start
							v-if="
								!route.query?.status || (route.query?.status !== '2' && route.query?.status !== '4')
							"
						>
							<el-button type="primary" size="small" @click="reForword">重新转发</el-button>
						</div>
					</template> -->
					<template #department="{rowData}">
						{{ rowData.department.parent.name + '-' + rowData.department.name }}
					</template>
					<template #filter="{rowData}">
						{{ rowData.department.parent.name + '-' + rowData.department.name }}
					</template>
				</BaseTableComp>
			</Block>
		</div>
		<ExportComp
			ref="exportRef"
			:sheets="exportData"
			:fileName="_detailInfo?.name"
			style="visibility: hidden; position: absolute; z-index: -1"
		></ExportComp>
		<Dialog
			v-model="auditModalIsVisible"
			title="提交审核"
			width="800"
			:enableButton="false"
			v-if="auditModalIsVisible"
			@close="auditModalIsVisible = false"
		>
			<FormComp
				:form="auditformArray"
				:rules="auditformRules"
				:showResetButton="false"
				:loading="isPushing"
				submitText="提交"
				@onSubmit="auditformSubmit"
			>
			</FormComp>
		</Dialog>

		<!-- 提交 -->
		<Dialog v-model="submitModalIsVisible" title="提交" width="800" :enableButton="false">
			<div text="12px red" mb-10px>
				*未保存的表格内容和未上传成功的附件将不会提交，请确认当前所有表格均已点击“保存”，且所有附件已上传成功。
			</div>
			<!-- :form="isLader ? [...dataAuditProps, ...submitformArray] : submitformArray" -->
			<FormComp
				:form="submitformArray"
				:showResetButton="false"
				submitText="提交"
				@onSubmit="formSubmit"
			>
				<!-- <template #dataAuditWorkflowSchemeCode>
					<FormItem
						v-model="dataAuditWorkflowSchemeCode"
						:items="[
							{
								prop: 'dataAuditWorkflowSchemeCode',
								label: '',
								type: 'selectRemote',
								remoteUrl: '/api/workflow/workflowSchemeInfo',
								remoteParams: {enabledMark: true, category: FlowType.Fill},
								remoteFilterKey: 'keyword',
								remoteInit: true,
								remoteValue: 'code',
							},
						]"
						style="width: 100%"
					>
						<template #form-dataAuditWorkflowSchemeCode-right>
							<el-button type="primary" ml-10px @click="onViewFlow(dataAuditWorkflowSchemeCode)">
								查看流程
							</el-button>
							<el-button type="primary" @click="onJumpCreateFlow">新建流程</el-button>
						</template>
					</FormItem>
				</template> -->
			</FormComp>
		</Dialog>

		<ViewFlow v-model="showViewFlow" :code="viewFlowCode"></ViewFlow>

		<!-- 附件 -->
		<Dialog
			v-model="uploadModelVisible"
			title="上传附件"
			width="800"
			@close="uploadModelVisible = false"
			@click-close="uploadModelCancel"
			@click-confirm="uploadModelConfirm"
		>
			<div class="uploadArea">
				<el-upload
					class="upload-demo"
					drag
					ref="upload"
					action=""
					multiple
					:http-request="customUpload"
					accept=".jpg,.jpeg,.png,.gif,.rar,.zip,.doc,.docx,.pdf,.xlsx,.xls"
					:limit="5"
					:on-remove="onRemoveFile"
					:before-upload="beforeUpload"
					:file-list="imgFile"
				>
					<el-icon class="el-icon--upload"><upload-filled /></el-icon>
					<div class="el-upload__text">点击或将文件拖动到这里进行上传</div>
					<div class="el-upload__text pd-top-15">
						支持格式:.jpg.jpeg.png.gif.rar.zip.doc.docx.pdf.xlsx.xls，
						最多支持上传5个附件,文件总大小不超过20MB
					</div>
				</el-upload>
			</div>
		</Dialog>

		<Dialog
			v-model="departmentShow"
			title="重新转发"
			width="800"
			height="400"
			:visible-footer-button="true"
			@click-confirm="submitToReForward"
			@close="departmentShow = false"
		>
			<div w-full flex="~" items-center style="padding: 0 20px">
				<div w-80px>填报部门:</div>
				<div flex="1">
					<departmentFavoriteComp
						v-show="departmentShow"
						placeholder="请选择转发部门"
						:data="[]"
						:type="'modal'"
						:defaultCheckedData="[]"
						@change="handleCheckReForward"
					></departmentFavoriteComp>
				</div>
			</div>
		</Dialog>

		<!-- 转发 -->
		<!-- <Dialog
			v-model="forwardModalIsVisible"
			title="设置转发"
			width="550"
			:enableButton="false"
			@close="forwardModalIsVisible = false"
		>
			<FormComp
				:form="forwardformArray"
				:showResetButton="false"
				:rules="forwardFormRules"
				submitText="确认"
				@onChange="forwardFormChange"
				@onClean="forwardFormClean"
				@onSubmit="forwardSubmit"
			>
				<template #staffs>
					<el-tree-select
						class="no-style"
						node-key="id"
						:props="props"
						ref="treeRefs"
						w-full
						v-model="staffId"
						multiple
						:data="selectDepartmentGroup"
						:load="loadNode"
						@current-change="handleCurrentChange"
						lazy
					></el-tree-select>
				</template>
			</FormComp>
		</Dialog> -->

		<Dialog
			v-model="forwardModalIsVisible"
			title="任务转发"
			:destroy-on-close="true"
			width="600"
			@click-confirm="forwardSubmit"
		>
			<Form
				ref="turnFormRef"
				v-model:="turnForm"
				:enable-button="false"
				:props="
					turnForm.model === 2
						? [...turnFormProps, ...turnFormPropsOther]
						: currentInfo?.hasDataAuditWorkflow
						? [...turnFormProps, ...turnFormPropsOther]
						: turnFormProps
				"
				:notClearColumns="['model']"
				:rules="{
					fillUsers: [
						{
							required: true,
							message: `请选择填报${
								turnForm.model === 1 ? Tips.Personnel : Tips.Department
							}`,
							trigger: 'change',
						},
					],
				}"
				label-width="100"
				@change="onTurnFormChange"
			>
				<template #form-fillUsers="scoped">
					<el-tree-select
						v-if="scoped.form.model === 1"
						ref="treeSelectRef"
						class="no-style"
						v-model="treeSelectValue"
						:data="treeSelectData"
						:load="loadNode"
						:props="{
							label: 'label',
							value: 'value',
							isLeaf: 'isLeaf',
						}"
						:remote-method="filterMethod"
						:loading="loading"
						filterable
						multiple
						remote
						lazy
						@change="onTreeSelectChange"
					>
					</el-tree-select>
					<departmentFavoriteComp
						v-if="scoped.form.model === 2"
						:type="'modal'"
						placeholder="请选择"
						@change="departmentListChange"
					></departmentFavoriteComp>
				</template>

				<!-- 下发填报 -->
				<template #form-turnFlow-right>
					<el-button type="primary" class="mg-left-10" @click="onViewFlow"
						>查看流程</el-button
					>
					<!-- <el-button type="primary" @click="onJumpCreateFlow">新建流程</el-button> -->
					<div class="tip" style="padding: 0">
						<i class="icon i-ic-baseline-tips-and-updates"></i>
						<p>说明: 转发不限制必须设置转发审核流程</p>
					</div>
				</template>

				<template #form-auditFlow-right>
					<el-button type="primary" class="mg-left-10" @click="onViewFlow"
						>查看流程</el-button
					>
					<!-- <el-button type="primary" @click="onJumpCreateFlow">新建流程</el-button> -->
				</template>
			</Form>
		</Dialog>
	</div>
</template>
<style lang="scss" scoped>
.report-task-detail {
	display: flex;

	.left {
		flex: 1;
	}

	.right {
		margin-right: 20px;
		width: 300px;
	}

	.open-history {
		align-items: center;
		border-bottom: 3px solid rgba(var(--z-line-rgb), 0.5);
		background-color: #fff;
		cursor: pointer;
		display: flex;
		line-height: 1.5;
		margin-right: 20px;
		margin-bottom: 20px;
		justify-content: center;
		span {
			color: var(--z-main);
			font-size: 16px;
			width: 20px;
		}
		width: 50px;
	}
}

:deep(.el-tabs__header) {
	margin-top: 10px;
	margin-bottom: 0;
}

.excel-box {
	border-radius: 0 5px 5px 5px;
	box-shadow: 0 -6px 6px -6px rgba(0, 0, 0, 0.12);
	border: 1px solid #e4e7ed;
	border-top: none;
	background-color: #fff;
	height: calc(100% - 192px);
	padding: 0 10px 10px 10px;

	.xlsx-plus {
		padding: 0;
	}

	&.full {
		height: 100% !important;
		left: 0;
		position: fixed;
		top: 0;
		width: 100%;
		z-index: 3;
	}
}

.excel_tips {
	align-items: center;
	display: flex;
	justify-content: flex-end;
	flex: 1;
	white-space: nowrap;

	i {
		color: var(--z-warning);
		height: 24px;

		svg {
			position: relative;
			top: -1px;
		}
	}

	span {
		color: var(--z-warning);
		height: 24px;
		line-height: 24px;
		padding: 0 0 0 5px;
	}
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
	border-radius: 5px;
	background: var(--z-theme);
}

.toggle-import-type {
	align-items: center;
	display: flex;
	position: relative;
	padding-right: 10px;

	span {
		background-color: #f1f1f1;
		height: 15px;
		position: absolute;
		right: 0;
		top: calc(50% - 7.5px);
		width: 2px;
	}
}

.linked-tips {
	align-items: center;
	display: flex;
	flex-wrap: nowrap;
	margin-left: 10px;
	white-space: nowrap;

	span {
		color: #666;
		padding: 0 10px;
	}

	.span1 {
		border-bottom: 5px solid #f78989;
		width: 30px;
	}

	.span3 {
		border-bottom: 5px solid #66b1ff;
		width: 30px;
	}
}
:deep(.fill-btn) {
	span {
		line-height: 15px;
	}
}

.info-over-view {
	display: flex;
}
.tip {
	align-items: center;
	color: var(--z-font-color);
	display: flex;
	padding: 0 10px;
	width: 100%;
	i {
		color: #f00;
	}
	p {
		color: var(--z-main);
		font-size: 12px;
	}
}
</style>
<style>
.xlsx-plus-component-full {
	position: fixed !important;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 0 10px 10px 10px !important;
	height: 100% !important;
	z-index: 9;
	background-color: #fff;
}
</style>
