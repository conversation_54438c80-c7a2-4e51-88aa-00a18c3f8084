<script setup lang="ts" name="record">
import {reactive, ref, onMounted, watch, onActivated} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {ReportsFlowStatus, ReportsFlowStatusType} from '@/define/statement.define'
import {FlowAuditTypes} from '@/define/Workflow'

enum HistoryType {
	Record = '审核记录',
	Todo = '填报记录',
}
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const activeName = ref<any>(HistoryType.Record)
const url: any = {
	[HistoryType.Record]: '/api/workflow/workflowTask/my-completed',
	[HistoryType.Todo]: '/api/platform/workToDoRecord/pages',
}
const searchForm: any = ref({})
const formItems = [
	{prop: 'title', type: 'text', placeholder: '请输入任务名称'},
	{
		prop: 'date',
		type: 'datetimerange',
		placeholder: '请选择创建时间',
	},
	// {
	// 	prop: 'IsUrge',
	// 	type: 'select',
	// 	placeholder: '请选择提状态',
	// 	options: [
	// 		{label: '正常', value: '0'},
	// 		{label: '催办', value: '1'},
	// 	],
	// },
]

const tableRef = ref()
const tableHeight = ref(0)
const tableColumns: any = {
	[HistoryType.Record]: [
		{label: '任务名称', prop: 'title', type: 'text'},
		// {label: '提交人', prop: 'createUserName', type: 'text'},
		{label: '提交人部门', prop: 'createUserLargeDepartmentName', type: 'text'},
		{label: '提交人科室', prop: 'createUserDepartmentName', type: 'text'},
		{label: '提交时间', prop: 'creationTime', type: 'datetime'},
		{
			label: '办理时间',
			prop: 'processTime',
			type: 'datetime',
			sortable: 'custom',
		},
		{label: '审核结果', prop: 'outcome', type: 'switch'},
	],
	[HistoryType.Todo]: [
		// {label: '任务名称', prop: 'reportTaskName'},
		// {label: '创建人', prop: 'reportTaskCreatorName'},
		// {label: '创建人部门', prop: 'createdBigDepartment'},
		// {label: '创建人科室', prop: 'createdDepartment'},
		// {label: '创建时间', prop: 'creationTime'},
		// {label: '填报时间', prop: 'fillerSubmitTime', sortable: 'custom'},
		// {label: '状态', prop: 'status'},

		{label: '任务名称', prop: 'name'},
		{label: '任务类型', prop: 'category'},
		{label: '创建人', prop: 'createrName'},
		{label: '创建人部门', prop: 'createrLargeDepartmentName'},
		{label: '创建人科室', prop: 'createrDepartmentName'},
		{label: '创建时间', prop: 'creationTime', sortable: 'custom'},
		{label: '状态', prop: 'status', width: 90},
	],
}
const defaultSort: any = ref({
	[HistoryType.Record]: {prop: 'processTime', order: 'descending'},
	[HistoryType.Todo]: {prop: 'creationTime', order: 'descending'},
})

const statusArr = [
	{name: '-', color: '#303030', state: 0},
	{name: '已通过', color: '#4caf50', state: 1},
	{name: '已驳回', color: '#FD6B69', state: 2},
]
const tableButtons = [{label: '查看', code: 'toexamine', type: 'primary', icon: ''}]

const beforeComplete = ({items, next}: any) => {
	const temp: any = []
	items.forEach((x: any) => {
		if (activeName.value === HistoryType.Record) {
			x.title = x.process.title
			// x.ledgerName = x.process.businessExtend ? x.process.businessExtend.LedgerName : ''
			x.createUserName = x.process.createUserName
			x.createUserDepartmentName = x.process.createUserDepartmentName
			x.createUserLargeDepartmentName = x.process.createUserLargeDepartmentName
			x.creationTime = x.process.creationTime
			;(x.finishTime = x.process.finishTime ?? '-'), (x.outcome = x.state == 3 ? 1 : 2)
			temp.push(x)
		} else {
			const row = {
				name: x?.name,
				category: x?.category,
				createrLargeDepartmentName: x?.createrLargeDepartmentName,
				createrDepartmentName: x?.createrDepartmentName,
				createrName: x?.createrName,
				creationTime: x?.creationTime,
				endTime: x?.endTime,
				currentServiceTime: x?.currentServiceTime,
				status: x?.status,
				_raw: JSON.parse(JSON.stringify(x)),
			}
			temp.push(row)
		}
	})
	next(temp)
}
const reqParams: any = reactive({
	skipCount: 0,
	maxResultCount: 10,
	Sorting: `${defaultSort.value[activeName.value].prop} desc`,
})

const pagination = reactive({
	total: 0,
	page: 1,
	size: 10,
})

const IsShowStateStop = ref(true)
const getInfo = async () => {
	// const res = await getTodoNotice(
	// 	{...pageParams.value, ...searchParams.value},
	// 	1,
	// 	IsShowStateStop.value
	// )
	// tableData.value = res.data.items
	// total.value = res.data.totalCount
	// userStore.$patch({workCount: total.value})
}
// 办理记录
const routeTo = () => {
	// router.push({
	// 	path: '/work/record',
	// })
}

const onBlockHeightChanged = (val: number) => {
	tableHeight.value = val - 75
}

const onTableCompleted = () => {
	pagination.total = tableRef.value.getTotal()
}

const onTableButtonClick = ({row, btn, index}: any) => {
	console.log(row, index)

	if (btn.code === 'toexamine') {
		if (activeName.value === HistoryType.Record) {
			// router.push({
			// 	path: '/taskPending/task-review-details',
			// 	query: {
			// 		id: row.id,
			// 		taskId: row.process.id,
			// 		keyword: row.process.keyword2,
			// 		businessId: row.process.businessId,
			// 		Remark: row.process.businessExtend.Remark,
			// 		showButton: 'false',
			// 	},
			// })

			if (row.process.businessType === FlowAuditTypes.ReportTaskIssude) {
				// 下发
				router.push({
					path: '/taskPending/detail',
					query: {
						id: row.process.businessId,
						taskId: row.id,
						type: 'detail',
						currentIndex: 1,
					},
				})
			} else if (
				row.process.businessType === FlowAuditTypes.ReportTaskDataAudit ||
				row.process.businessType === FlowAuditTypes.ReportTaskTranspondAudit
				// 数据审核转发
			) {
				router.push({
					path: '/taskPending/report-task-detail',
					query: {
						taskId: row.id,
						reportTaskId: row.process.businessExtend.ReportTaskId,
						areaOrganizationUnitId: row.process.businessExtend.AreaOrganizationUnitId,
						id: row.process.businessExtend.Id,
						type: 'detail',
						currentIndex: 4,
						businessType: row.process.businessType,
					},
				})
			} else if (row.process.businessType === FlowAuditTypes.LedgerDataExport) {
				router.push({
					path: '/taskPending/export-audit',
					query: {
						id: row.process.businessId,
						rid: row.processId,
						tid: row.id,
					},
				})
			} else {
				router.push({
					path: '/taskPending/task-review-details',
					query: {
						id: row.id,
						taskId: row.process.id,
						keyword: row.process.keyword2,
						businessId: row.process.businessId,
						Remark: row.process.businessExtend.Remark,
						showButton: 'false',
					},
				})
			}
		} else if (activeName.value === HistoryType.Todo) {
			if (row.category === 1) {
				router.push({
					path: '/taskPending/report-task-detail',
					query: {
						reportTaskId: row._raw.businessExtend.reportTaskId,
						areaOrganizationUnitId: row._raw.businessExtend.areaOrganizationUnitId,
						id: row._raw.businessExtend.id ?? undefined,
						type: 'detail',
						from: 'taskPending',
					},
				})
			} else if (row.category === 2) {
				router.push({
					path: '/taskPending/batch-record',
					query: {
						id: row._raw.businessId,
						ledgerId: row._raw.businessExtend.LedgerId,
					},
				})
			}
		}
	}
}

const onSortChange = (e: any) => {
	delete reqParams.Sorting
	reqParams.Sorting = `${e.prop} ${e.order === 'ascending' ? 'asc' : 'desc'}`
}

// const onCreate = () => {
// router.push({path: '/taskPending/report-task-detail', query: {}})
// }

const clearReqParams = (needResetPage: boolean = false) => {
	if (needResetPage) {
		pagination.page = 1
		pagination.size = 10
		reqParams.skipCount = 0
		reqParams.maxResultCount = 10
	}

	Object.keys(reqParams).forEach((key) => {
		if (key !== 'skipCount' && key !== 'maxResultCount') delete reqParams[key]
	})

	// 处理固定参数
	if (activeName.value === HistoryType.Record) {
		reqParams.Sorting = 'processTime desc'
	} else if (activeName.value === HistoryType.Todo) {
		reqParams.Sorting = 'creationTime desc'
		reqParams.status = [3, 4]
	}
}

const onSearch = (bool: boolean = false) => {
	clearReqParams(bool)

	let date = searchForm.value.date
	if (activeName.value == HistoryType.Record) {
		reqParams.title = searchForm.value.title
		if (date) {
			reqParams.startDate = date[0]
			reqParams.endDate = date[1]
		}
	} else {
		reqParams.name = searchForm.value.title
		if (searchForm.value.date) {
			reqParams.startTime = date[0]
			reqParams.endTime = date[1]
		}
	}
}

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.page = 1
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const handleTabsClick = () => {
	clearReqParams(true)
}

const goToMyTdoto = () => {
	router.push({path: '/statementDone'})
}

onActivated(() => {
	if (route.query.menu || route.query.isData) {
		activeName.value = HistoryType.Todo
		onSearch(true)
	}
})
</script>
<template>
	<div class="flow">
		<Block
			:enable-fixed-height="true"
			:enable-expand-content="true"
			title="办理记录"
			@height-changed="onBlockHeightChanged"
		>
			<template #title>
				<el-tabs
					v-model="activeName"
					class="tabs-ui"
					:before-leave="() => !loading"
					@tab-change="handleTabsClick"
					style="width: 100%"
				>
					<el-tab-pane
						:label="HistoryType.Record"
						:name="HistoryType.Record"
					></el-tab-pane>
					<el-tab-pane :label="HistoryType.Todo" :name="HistoryType.Todo"></el-tab-pane>
				</el-tabs>
			</template>

			<template #topRight>
				<el-link size="small" type="primary" @click="goToMyTdoto" style="font-size: 12px"
					>历史已办</el-link
				>
			</template>

			<template #expand>
				<div class="search">
					<Form
						v-model="searchForm"
						:props="formItems"
						:column-count="4"
						:enable-reset="false"
						:loading="loading"
						buttonVertical="flowing"
						label-width="0"
						confirm-text="查询"
						@submit="onSearch"
					></Form>
				</div>
			</template>
			<TableV2
				:key="activeName"
				ref="tableRef"
				:url="url[activeName]"
				:req-params="reqParams"
				:columns="tableColumns[activeName]"
				:buttons="tableButtons"
				:enable-edit="false"
				:enable-delete="false"
				:enable-selection="false"
				:enable-latest-data="false"
				:height="tableHeight"
				:enable-toolbar="false"
				:default-sort="defaultSort[activeName]"
				:form-label-width="100"
				@completed="onTableCompleted"
				@beforeComplete="beforeComplete"
				@click-button="onTableButtonClick"
				@sort-change="onSortChange"
				@loading="loading = $event"
			>
				<template #title="{row}">{{ row.process?.title }}</template>
				<template #outcome="{row}">
					<span
						:style="{
							color: statusArr.filter(
								(x) => x.state === (!row.isAgree ? 2 : row.outcome)
							)[0].color,
						}"
					>
						{{
							statusArr.filter((x) => x.state === (!row.isAgree ? 2 : row.outcome))[0]
								.name
						}}
					</span>
				</template>

				<!-- FlowType.Fill -->
				<template v-if="activeName === HistoryType.Todo" #category="scoped">
					{{ scoped.row.category === 1 ? '临时报表填报' : '业务表填报' }}
				</template>
				<template v-if="activeName === HistoryType.Todo" #status="scoped">
					<el-tag :type="ReportsFlowStatusType(scoped.row.status, 'fillReport')">
						{{
							ReportsFlowStatus.find(
								(f) => f.value === scoped.row.status && f.type === 'fillReport'
							)?.label || '-'
						}}
					</el-tag>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>
	</div>
</template>
<route>
	{
		meta: {
			title: '办理记录-任务待办'
		}
	}
</route>
