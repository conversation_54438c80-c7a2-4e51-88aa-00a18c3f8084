<route>
	{
		meta: {
			childTitle: '',
		},
	}
</route>
<script setup lang="ts" name="batchrecord">
import {onMounted, reactive, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {GetReportTaskDetail} from '@/api/ReportApi'
import {getDetailByLedgerId} from '@/api/LedgerApi'
import {useFormatTableHeader} from '@/hooks/useFormatTableHeader'
import AuditRecord from '@/views/index/ledger/components/auditRecord.vue'
import {ElMessage} from 'element-plus'
import {updateLabelTitle} from '@/hooks/useLabels'
const router = useRouter()
const loading = ref(false)
const route = useRoute()
const ledgerDetail: any = ref(null)
const taskDetail: any = ref(null)

const tableRef = ref()
const tableUrl = ref('')
const tableReqParams = reactive({
	skipCount: 0,
	maxResultCount: 10,
})
const pagination = reactive({
	size: 10,
	total: 0,
})

const form = ref({})
const formProps: any = [
	{prop: 'name', label: '业务表名称'},
	{prop: 'creatorDepartmentName', label: '创建部门'},
	{prop: 'creatorUserName', label: '创建人'},
	{prop: 'description', label: '填报说明', full: true},
]

const tableColumns: any = [
	{prop: 'creationTime', label: '提交时间'},
	{prop: 'totalCount', label: '提交数量(条)'},
	{prop: 'passedCount', label: '通过数量(条)'},
	{prop: 'notPassedCount', label: '未通过数量(条)'},
	{prop: 'requestComment', label: '说明'},
]

const tableButtons: any = [{label: '详情', type: 'primary', code: 'view'}]
const statusMap = ['待提交', '审核中', '审核通过', '审核不通过']
const operationTypeMap = ['用户新增', '用户修改', '用户删除']

const currentRow: any = ref(null)
const showAuditRecord = ref(false)
const auditTableColumns: any = ref(null)
const auditReqParams = reactive({
	ledgerId: '',
	auditedBatchId: '',
	maxResultCount: 10,
	skipCount: 0,
})
const auditPagination = reactive({
	size: 10,
	total: 0,
})

const onTableCompleted = (type: any) => {
	if (type === 'audit') {
		auditPagination.total = tableRef.value.getTotal()
	} else {
		pagination.total = tableRef.value.getTotal()
	}
}

const onTableButtonClick = ({row, btn, index}: any) => {
	currentRow.value = row
	switch (btn.code) {
		case 'view':
			showAuditRecord.value = true
			auditReqParams.ledgerId = route.query.ledgerId as string
			auditReqParams.auditedBatchId = row.id
			return
		default:
			return
	}
}

const onPageinationChange = (val: number, type: string, tableType?: any) => {
	if (tableType === 'audit') {
		if (type == 'page') {
			auditReqParams.skipCount = (val - 1) * auditReqParams.maxResultCount
		} else {
			auditPagination.size = val
			auditReqParams.maxResultCount = auditPagination.size
		}
	} else {
		if (type == 'page') {
			tableReqParams.skipCount = (val - 1) * tableReqParams.maxResultCount
		} else {
			pagination.size = val
			tableReqParams.maxResultCount = pagination.size
		}
	}
}

const onAuditBeforeTableCompleted = ({items, next}: any) => {
	const reslut: any = []
	items.forEach((item: any) => {
		reslut.push({
			...item,
			...item.data,
			operationType: item.operationType,
			status: item.status,
			creationTime: item.creationTime,
		})
	})
	next(reslut)
}

const init = () => {
	const {id, ledgerId} = route.query
	if (id && ledgerId) {
		getDetailByLedgerId(ledgerId as string)
			.then((res: any) => {
				ledgerDetail.value = res.data
				const {tableFieldGroups, fields} = res.data.tableInfo

				const auditFields = useFormatTableHeader(tableFieldGroups, fields)
				auditTableColumns.value = [
					...auditFields,
					...[
						{
							prop: 'operationType',
							label: '操作类型',
							attrs: {fixed: 'right', width: 100},
						},
						{
							prop: 'creationTime',
							label: '提交时间',
							attrs: {fixed: 'right', width: 180},
						},
						{
							prop: 'status',
							label: '审核状态',
							attrs: {fixed: 'right', width: 80},
						},
					],
				]
			})
			.catch((err: any) => {
				window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
			})

		GetReportTaskDetail(id as string)
			.then((res: any) => {
				taskDetail.value = res.data
				if (taskDetail.value) {
					const {planTask} = taskDetail.value
					form.value = {
						name: taskDetail.value.name,
						creatorDepartmentName: planTask.creatorDepartmentName,
						creatorUserName: planTask.creatorUserName,
						description: planTask.description,
					}
					updateLabelTitle({
						path: router.currentRoute.value.fullPath,
						title: `查看-任务待办-${taskDetail.value.name}`,
					})
					tableUrl.value = `/api/ledger/ledger-audit-batch/my-submit-audit-batchs?taskItemId=${id}&ledgerId=${ledgerId}`
				}
			})
			.catch((err: any) => {
				window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
			})
	}
}

onMounted(() => {
	init()
})
</script>
<template>
	<div class="batch-record">
		<Block title="填报记录" :enable-expand-content="false">
			<Form
				v-model="form"
				:props="formProps"
				:grid="true"
				:column-count="4"
				:label-width="100"
			></Form>
		</Block>

		<Block
			title="提交记录"
			:enable-expand-content="false"
			:enable-back-button="false"
			:enable-close-button="false"
		>
			<TableV2
				ref="tableRef"
				:url="tableUrl"
				:req-params="tableReqParams"
				:columns="tableColumns"
				:buttons="tableButtons"
				:auto-height="true"
				:enable-toolbar="false"
				:enable-own-button="false"
				@loading="loading = $event"
				@clickButton="onTableButtonClick"
				@completed="onTableCompleted"
			>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:currentPage="1"
				justify-content="flex-end"
				@size-change="onPageinationChange($event, 'size')"
				@current-change="onPageinationChange($event, 'current')"
			>
			</Pagination>
		</Block>

		<AuditRecord
			v-model="showAuditRecord"
			title="审核记录"
			width="70%"
			:LedgerId="(route.query.ledgerId as string)"
			:AuditedBatchId="currentRow?.id"
			:disable-table="true"
		>
			<template #table="scoped">
				<TableV2
					url="/api/ledger-service/ledgerAuditedData/my-audited"
					:auto-height="true"
					:req-params="auditReqParams"
					:columns="auditTableColumns"
					:disabled="true"
					@before-complete="onAuditBeforeTableCompleted"
					@completed="onTableCompleted('audit')"
				>
					<template #status="scoped">
						{{ statusMap[scoped.row.status] }}
					</template>
					<template #operationType="scoped">
						{{ operationTypeMap[scoped.row.operationType] }}
					</template>
				</TableV2>
				<Pagination
					:total="auditPagination.total"
					:page-size="auditPagination.size"
					:currentPage="1"
					justify-content="flex-end"
					@size-change="onPageinationChange($event, 'size', 'audit')"
					@current-change="onPageinationChange($event, 'current', 'audit')"
				>
				</Pagination>
			</template>
		</AuditRecord>
	</div>
</template>
