<script setup lang="ts" name="taskreviewdetails">
import {
	computed,
	h,
	inject,
	onBeforeUnmount,
	onDeactivated,
	onMounted,
	onUnmounted,
	reactive,
	ref,
	toRaw,
	watch,
	nextTick,
} from 'vue'
import {Request} from '#/interface'
import {useRoute, useRouter} from 'vue-router'
import {ElMention, ElMessage, ElMessageBox, ElNotification, FormInstance} from 'element-plus'
import {useUserStore} from '@/stores/useUserStore'
import {STAFFROLEARRAY} from '@/define/organization.define'
import {ledgerAuditedData, workflowTaskLog, ledgerTable} from '@/api/WorkflowApi'
import {
	getDownLoadData,
	CanNoDataAudit,
	GetProcessList,
	getTableFiledValidateRule,
	getAuditLedgerDataInfoById,
	getLedgerDataInfoById,
	ledgerAuditedDataGet,
	ledgerAuditedDataPUTOther,
} from '@/api/LedgerApi'
import {dayjs} from 'element-plus'
import {useArrayToTree} from '@/hooks/useConvertHook'
import BusinessProcess from '@/components/BusinessProcess.vue'
import {deleteLabel, updateLabelTitle} from '@/hooks/useLabels'
import collapseForm from '../ledger/components/collapseForm.vue'
import {useViewStore} from '@/stores/useViewStore'

import {auditEditApi} from '@/api/ReportApi'
import {useEvalHook} from '@/hooks/useEvalHook'

import {
	isEqual,
	isNotEqual,
	isGreaterThan,
	isGreaterThanOrEqual,
	isLessThan,
	isLessThanOrEqual,
	isBetweenExclusive,
	isNotBetween,
} from '@/define/numberRules'

const viewStore = useViewStore()

const router = useRouter()
const axios = inject('#axios') as Request
const route = useRoute()
const userStore = useUserStore()

enum oprationCodeEnum {
	disagree = 'danger',
	agree = 'success',
}

enum BatchType {
	default = 0,
	noDataUpdate = 1,
}
const cascadeFormData = ref<any>({})

const departmentInfo = ref<any>(null)

departmentInfo.value = userStore.getCurrentDepartment

const id = route.query.id as string
const taskId = route.query.taskId as string
const keyword = route.query.keyword as string
const businessId = route.query.businessId as string
const organizationId = route.query.areaOrganizationUnitId as string
const Remark = route.query.Remark as string
const blockRef = ref()
const blockHistoryRef = ref()
const _excel_canview = computed(() =>
	_detailInfo.value ? _detailInfo.value.status !== 4 && _detailInfo.value.status !== 7 : false
)
const showButton = ref(route.query.showButton ? false : true)
console.log(route.query.showButton ? false : true, '11111111')
const isPushing = ref(false)
// 详情组件title参数 可变化
const titleList = ref([
	{
		width: '25%',
		title: '任务名称',
		key: 'creatorDepartmentName',
	},
	{
		width: '25%',
		title: '创建人',
		key: 'name',
	},
	{
		width: '25%',
		title: '创建人科室',
		key: 'creatorUserName',
	},
	// {
	// 	width: '25%',
	// 	title: '所属批次',
	// 	key: 'taskItemName',
	// },
	{
		width: '25%',
		title: '填报截止时间',
		key: 'deadline',
	},
	{
		width: '25%',
		title: '填报说明',
		key: 'description',
	},
	{
		width: '25%',
		title: '填报人',
		key: 'dataFillerName',
	},
	{
		width: '25%',
		title: '填报批次',
		key: 'taskItemName',
	},
])
// 领导审核弹窗
const auditModalIsVisible = ref(false)
// 是否通过
const isPass = ref('')
// 详情信息
const _detailInfo = ref<any>({})
const isCanNoDataAudit = ref(false)

const setLabelTitle = () => {
	updateLabelTitle({
		path: router.currentRoute.value.fullPath,
		title: `审核-任务待办-${_detailInfo.value.creatorDepartmentName}`,
	})
}

// 获取报表任务详情
const getReportInfo = () => {
	axios
		.get(`/api/platform/wFDataProcess/${taskId}/by-wf-processId`)
		.then((res) => {
			const {data} = res
			console.log(444, data)
			if (data) {
				_detailInfo.value = {
					name: data.taskItem.planTask.creatorUserName,
					creatorUserName: data.taskItem.planTask.creatorDepartmentName,
					creatorDepartmentName: data.taskItem.planTask.name,
					taskItemName: data.taskItem.name,
					deadline: data.taskItem.deadline,
					description: Remark,
					dataFillerName: data.dataFillerName,
				}
			}
			setLabelTitle()
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}
const process = ref([
	{
		type: 'textarea',
		title: '请确认已选择数据需要驳回填报人。并填写驳回原因',
		filterable: true,
	},
])
// 审核表单数据
const auditformArray = ref([
	{
		type: 'textarea',
		title: isPass.value == 'agree' ? '审核意见' : '驳回原因',
		field: 'auditReason',
		filterable: true,
	},
])
const auditformRules = ref({
	auditReason: [
		{
			required: true,
			message: `请输入${isPass.value == 'agree' ? '审核意见' : '驳回原因'}`,
			trigger: 'blur',
		},
	],
})
watch(
	() => auditModalIsVisible.value,
	(val) => {
		if (val === true) {
			auditformArray.value = [
				{
					type: 'textarea',
					title: isPass.value == 'agree' ? '审核意见' : '驳回原因',
					field: 'auditReason',
					filterable: true,
				},
			]
			auditformRules.value = {
				auditReason: [
					{
						required: true,
						message: `请输入${isPass.value == 'agree' ? '审核意见' : '驳回原因'}`,
						trigger: 'blur',
					},
				],
			}
		}
	}
)
const selectData = ref([])
const selectVisible = ref(false)
// 数据审核提交
const processSure = (form: any) => {
	let dataIds = selectData.value.map((v: any) => v.Id)
	axios
		?.post(`/api/workflow/workflowProcess/audit-data-list`, {
			name: '驳回',
			des: form.auditReason,
			code: 'disagree',
			AuditedBatchId: businessId,
			dataIds,
		})
		.then(async (res: any) => {
			ElMessage.success({
				message: `审核成功`,
				duration: 2000,
			})
			selectVisible.value = false
			// selectable.value=!dataIds.includes(row.id)
			getList()
		})
		.catch((err) => {
			window.errMsg(err, '提交审核')
		})
}
const selectable = (row: any) => !['3a1a02ab-abc1-526b-529a-6a62153de376'].includes(row.id)
console.log(selectable, 'cesss')
// 取消
const closeClick = () => {
	selectVisible.value = false
}
// 多选
const handleSelectionChange = (row: any) => {
	selectData.value = row
	console.log(selectData.value)
}
const selectClick = () => {
	if (selectData.value.length < 1)
		return ElMessage.warning({
			message: `请选择需要审核的内容`,
			duration: 2000,
		})
	selectVisible.value = true
}
// 提交审核
const auditformSubmit = (val: any, formRef: FormInstance | undefined) => {
	// 表单验证
	formRef?.validate().then((validate: boolean) => {
		isPushing.value = true
		axios
			?.post(`/api/workflow/workflowProcess/audit/${id}`, {
				name: isPass.value == 'agree' ? '通过' : '驳回',
				des: val.auditReason,
				code: isPass.value,
			})
			.then(async (res: any) => {
				ElMessage.success({
					message: `${isPass.value == 'agree' ? '已通过' : '已驳回'}`,
					duration: 2000,
				})
				auditModalIsVisible.value = false
				isPushing.value = true
				deleteLabel({
					path: router.currentRoute.value.fullPath,
				})
			})
			.catch((err) => {
				window.errMsg(err, '提交审核')
			})
	})
}
// 审核流程
const audit = (val: any) => {
	isPass.value = val
	auditModalIsVisible.value = true
}
const isFull = ref(false)
const infoOverViewHeight = ref(0)
const showProgress = ref(true)
const onToggleInfoOverViewVisible = (hide: boolean, height: number) => {
	if (hide) {
		infoOverViewHeight.value = height
	} else {
		infoOverViewHeight.value = 0
	}
	setTimeout(() => {
		showProgress.value = !hide
	}, 100)
}
// 表中的内容
const tableData = ref([])

// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 200
}
const active = ref(0)
const onTableBeforeComplete = ({items, next}: any) => {
	const temp: any = []
	if (active.value === 1) {
		items.forEach((item: any) => {
			item.details.forEach((detail: any) => {
				temp.push({
					district: item.district,
					departmentName: detail.departmentName,
					ledgerCount: detail.ledgerCount,
					ledgerDataCount: detail.ledgerDataCount,
				})
			})
		})
		next(temp)
	} else if (active.value == 2) {
		items.forEach((item: any) => {
			item.details.forEach((detail: any) => {
				temp.push({
					district: item.district,
					street: detail.street,
					cityLedgerCount: detail.cityLedgerCount,
					cityLedgerDataCount: detail.cityLedgerDataCount,
					districtLedgerCount: detail.districtLedgerCount,
					districtLedgerDataCount: detail.districtLedgerDataCount,
					historyReportCount: detail.historyReportCount,
					ledgerCount: detail.ledgerCount,
					ledgerDataCount: detail.ledgerDataCount,
					userCount: detail.userCount,
				})
			})
		})
		next(temp)
	} else {
		next(items)
	}
}
//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 查询条件
const runwayForm: any = ref({
	name: '',
	chartType: null,
})
// 查询
const getList = () => {
	ledgerAuditedData(businessId, {
		skipCount: (pagination.value.page - 1) * pagination.value.size,
		MaxResultCount: pagination.value.size,
		LedgerId: keyword,
		AuditedBatchId: businessId,
		OperationTypes: checkList.value,
		// name: runwayForm.value.name,
		// chartType: runwayForm.value.chartType,
	})
		.then((res: any) => {
			const {data} = res
			console.log('data233', data)

			pagination.value.total = data.totalCount
			tableData.value = data.items.map((v: any) => ({
				...v.oldData,
				...v.data,
				Id: v.id,
				ledgerId: v.ledgerId,

				operationType:
					v.operationType == 0
						? '新增'
						: v.operationType == 1
						? '更新'
						: v.operationType == 2
						? '删除'
						: '',
				status: v.status,
				creationTime: v.creationTime,
			}))
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.size = val
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}

const tableOffsetHeight = ref(0)
const flowPathData = ref<any>([])
const canAudit = ref(true)
// 流程
const getWorkflowTaskLog = () => {
	// workflowTaskLog(taskId).then((res: any) => {
	// 	if (res.status == 200) {
	// 		const {data} = res
	// 		data.forEach((item: any) => {
	// 			item.label = item.unitName
	// 			item.text = item.des
	// 			item.user = item.userName
	// 			item.time = item.creationTime
	// 			item.type = (oprationCodeEnum as any)[item.operationCode] ?? 'default'
	// 			// item.user = item.unitName
	// 		})
	// 		flowPathData.value = data
	// 	}
	// 	console.log(res)
	// })

	GetProcessList(taskId)
		.then((res: any) => {
			console.log(2323, res)
			// const start: any = getStart()
			// const auditList = getAuditList(res.data)
			// const end = getEnd(res.data)
			flowPathData.value = res.data
			const currentUserId = JSON.parse(localStorage.getItem('currentUserInfo') || '{}').id
			// 如果流程已经结束或者已被审核，则不能审核
			if (flowPathData.value.isApproved || flowPathData.value.isEnd) {
				canAudit.value = false
			} else {
				// 获取当前节点
				const currentUnit = flowPathData.value.units.filter(
					(units: any) => units.isCurrentUnit
				)
				if (currentUnit.length === 0) {
					canAudit.value = false
				} else {
					const isCountersign = currentUnit[0].isCountersign
					// 判断会签状态
					if (isCountersign) {
						// 如果是所有人都需要审核
						currentUnit[0].tasks
							?.filter((user: any) => !user.isAgree)
							.some((dd: any) => {
								if (dd?.auditor?.id === currentUserId) {
									return (canAudit.value = true)
								} else {
									return (canAudit.value = false)
								}
							})
					} else {
						// 如果是只需要节点里某一个人进行了审核
						const Audit = currentUnit[0].tasks?.filter((x) => !x.isAgree)
						if (Audit.length !== currentUnit[0].tasks.length) {
							// 已经审核过了
							canAudit.value = false
						} else {
							// 所有都没有审核过
							canAudit.value =
								Audit.filter((v) => v.auditor.id === currentUserId).length !== 0
						}
					}
				}
			}
			console.log(3333, canAudit.value)
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}
const checkList = ref<any>([0, 1, 2]) //默认勾选
const checked = ref<any>([0, 1, 2])
const changeCheckList = (val: string | number | boolean) => {
	getList()
}
// 表头
const tableColumns: any = ref([])
const TilingColData: any = ref([])
// 筛选表头
const columnsList: any = ref([])

const allFiled = ref<any>([])
const tableFiled = ref<any>([])
const exportTableFiled = ref<any>([])
const tableGroup = ref<any>([])
const filterField = ref<any>([])

// 递归
const recursionFun = (obj: any) => {
	let arr: any = []
	obj.modifyChildren = []
	obj?.children?.forEach((item: any, index: any) => {
		if (!item.displayName) {
			if (arr.length > 0) {
				obj.modifyChildren.push(arr)
			}
			obj.modifyChildren.push(item)
			// activeNames.value.push(item.id)
			recursionFun(obj.modifyChildren[obj.modifyChildren.length - 1])
			// 把这一段放进去递归
			arr = []
		} else {
			arr.push(item)
		}
		if (obj.children.length - 1 == index) {
			if (arr.length > 0) {
				obj.modifyChildren.push(arr)
			}
			arr = []
		}
	})
}
const auxiliaryFillingType = ref(3)
const formSearch: any = ref([])

const evnFormSearch = (expression: string) => {
	console.log('搜索到11', expression)
	const fields = formSearch.value.filter((f: any) => expression?.includes(f.raw?.id))
	console.log('搜索到22', fields)
	if (fields) {
		const Indexs = formSearch.value.findIndex((f: any) => f.raw?.id === fields[0].raw?.id)

		formSearch.value[Indexs].multiple = false
		console.log('关闭多选了', formSearch)
	}
}

const getledgerTable = async () => {
	const {id: ledgerId}: any = route.query

	const cache: any = viewStore.LDF_RULE || {}
	let ruleRes: any
	if (cache[ledgerId] === undefined) {
		const res = await getTableFiledValidateRule()
		cache[ledgerId] = res
		// localStorage.setItem('LDF_RULE', JSON.stringify(cache))
		viewStore.setRule(cache)
		ruleRes = res
	} else {
		ruleRes = cache[ledgerId]
	}
	const ruleResData = ruleRes.data
	ruleData.value = ruleRes.data

	ledgerTable(keyword)
		.then((res: any) => {
			if (!res.data) return
			columnsList.value = res.data.tableInfo?.fields

			res.data.tableInfo.fields.unshift({
				name: '_cascade',
				displayName: '所属区域',
				type: 'cascade',
				show: false,
				showAreaList: res.data.tableInfo.fields
					.filter(
						(f: any) =>
							f.name === 'City' ||
							f.name === 'District' ||
							f.name === 'Street' ||
							f.name === 'Community'
					)
					.filter((x: any) => x.isListField)
					.map((v: any) => v.name),
			})
			getHandData(res.data)

			const data = res.data
			const {tableFieldGroups} = data.tableInfo
			tableColumns.value = []
			TilingColData.value = []
			console.log(999, data)

			data.tableInfo.fields.forEach((item: any) => {
				item.tzName = data.tableInfo.displayName
				item.bm = item.name
			})
			allFiled.value = res.data.tableInfo.fields
			// 过滤有权限字段
			tableFiled.value = data.tableInfo.fields.filter(
				(f: any) => f.isDepartmentField && f.name !== 'UpdateTime' && f.isListField //&& f.isListField  && f.name !== 'UpdateTime'
			)
			exportTableFiled.value = data.tableInfo.fields
				.filter((f: any) => f.isDepartmentField && f.name !== 'UpdateTime' && f.isListField)
				.concat(data.tableInfo.fields.filter((f: any) => f.name === 'UpdateTime')[0])
			tableGroup.value = data.tableInfo.tableFieldGroups
			console.log('data.tableInfo', data.tableInfo)
			let tableFieldField: any = []
			tableFieldGroups.forEach((item: any) => {
				tableFieldField = [
					...tableFieldField,
					...item.tableFields.filter((f: any) => f.isDepartmentField && f.isListField), //&& f.isListField
				]
			})
			filterField.value = data.tableInfo.fields.filter((f: any) => {
				return !tableFieldField.some((f2: any) => f2.id === f.id && f.isDepartmentField)
			})
			const group: any = []
			tableFieldGroups.forEach((item: any, index: any) => {
				if (item.tableFields) {
					group.push(
						Object.assign(item, {
							id: item.id,
							label: item.name,
							parentId: item.parentId,
							tableFields: toRaw(
								item.tableFields.filter(
									(f: any) => f.isDepartmentField && f.isListField
								) //&& f.isListField
							),
						})
					)
					item.tableFields.forEach((tf: any) => {
						if (tf.isListField && tf.isDepartmentField) {
							group.push({
								label: tf.displayName,
								field: tf.name,
								prop: tf.name,
								parentId: item.id,
								id: tf.id,
								...tf,
							})
						}
					})
				} else {
					group.push({label: item.name, id: item.id, parentId: item.parentId})
				}
			})
			const LedgerGroup = useArrayToTree(
				group,
				'id',
				'parentId',
				'label',
				false,
				'children',
				true
			)

			filterField.value = [...filterField.value, ...LedgerGroup]

			filterField.value.sort((a: any, b: any) => a.sort - b.sort)

			let tempArr: any = []
			let arr: any = []
			filterField.value.forEach((item: any, index: any) => {
				if (!item.children) {
					arr.push(item)
				} else {
					if (arr.length > 0) {
						tempArr.push(arr)
					}
					tempArr.push(item)
					// activeNames.value.push(item.id)
					recursionFun(tempArr[tempArr.length - 1])
					// 使用递归去添加item数据
					arr = []
				}
				if (filterField.value.length - 1 == index) {
					if (arr.length > 0) {
						tempArr.push(arr)
					}
					arr = []
				}
			})
			filterField.value = tableFiled.value.filter((f: any) => {
				return !tableFieldField.some((f2: any) => f2.id === f.id)
			})
			// 暂时列表隐藏所属部门字段
			const __fields = tableFiled.value.filter(
				(f: any) => f.isListField && f.isDepartmentField && f.name !== 'Department'
			)
			const groups: any = []
			const columns: any = tableFieldGroups.length === 0 ? __fields : tableFieldGroups

			columns.forEach((f: any) => {
				if (f.tableFields) {
					groups.push(
						Object.assign(f, {
							id: f.id,
							title: f.name,
							align: 'left',
							parentId: f.parentId,
							__isGroup: true,
							sort: f.sort,
							sortable:
								f.type === 'datetime' ||
								f.type === 'date' ||
								f.type === 'decimal' ||
								f.type === 'int',
						})
					)
					f.tableFields
						.filter((ff: any) => ff.isListField)
						.forEach((tf: any) => {
							groups.push({
								title: tf.displayName ?? tf.name,
								label: tf.displayName ?? tf.name,
								field: tf.name,
								prop: tf.name,
								raw: tf,
								parentId: f.id,
								sort: tf.sort,
								sortable:
									tf.type === 'datetime' ||
									tf.type === 'date' ||
									tf.type === 'decimal' ||
									tf.type === 'int',
							})
						})
				} else {
					groups.push({
						id: f.id,
						title: f.displayName ?? f.name,
						label: f.displayName ?? f.name,
						prop: tableFieldGroups.length === 0 ? f.name : null,
						align: 'left',
						field: tableFieldGroups.length === 0 ? f.name : null,
						parentId: f.parentId,
						raw: f,
						canClick: f.type === 'images' || f.type === 'attachments',
						sort: f.sort,
						__isGroup: tableFieldGroups.length !== 0,
						sortable:
							f.type === 'datetime' ||
							f.type === 'date' ||
							f.type === 'decimal' ||
							f.type === 'int',
					})
				}
			})

			// 处理没分组字段
			__fields.forEach((f: any) => {
				if (!groups.some((g: any) => !g.__isGroup && g.field === f.name)) {
					groups.push({
						title: f.displayName ?? f.name,
						label: f.displayName ?? f.name,
						field: f.name,
						prop: f.name,
						raw: f,
						parentId: null,
						sort: f.sort,
						sortable:
							f.type === 'datetime' ||
							f.type === 'date' ||
							f.type === 'decimal' ||
							f.type === 'int',
					})
				}
			})
			console.log(__fields)

			groups.forEach((v: any) => {
				if (v.children) v.children = []
			})
			tableColumns.value = useArrayToTree(
				groups,
				'id',
				'parentId',
				'title',
				false,
				'children',
				true
			)
			res.data.tableInfo.fields
				.filter((v) => (v.isListField && v.type !== 'UpdateTime') || v.type !== 'Informant')
				.forEach((item, index) => {
					// item.bm = item.name
					// tableColumns.value.push({
					// 	prop: item.name,
					// 	label: item.displayName,
					// 	type: item.type,
					// })
					if (
						item.type == 'datetime' ||
						item.type === 'images' ||
						item.type === 'attachments'
					) {
						TilingColData.value.push(item)
					}
				})

			tableColumns.value.sort((a: any, b: any) => a.sort - b.sort)

			tableColumns.value.push({
				prop: 'operationType',
				label: '操作类型',
				type: 'operationType',
			})

			getList()

			fields.value = data.tableInfo.fields.sort((a: any, b: any) => a.sort - b.sort)

			// 表格
			fields.value.unshift({
				name: 'Id',
				displayName: 'Id',
				type: 'string',
			})

			// 过滤表单字段
			const filterFiledName = [
				'Id',
				'Department',
				'DataStatus',
				'LockUserId',
				'DeleteUserId',
				'City',
				'District',
				'Street',
				'Community',
			]
			const departmentInfo = JSON.parse(localStorage.getItem('currentUserInfo') as any)

			const advancedSearchFormHistory = localStorage.getItem('LDF_ASF')
			auxiliaryFillingType.value = data.auxiliaryFillingType
			let historySearch = advancedSearchFormHistory
				? JSON.parse(advancedSearchFormHistory)
				: null

			console.log(444, fields.value)
			// 处理formArray.value
			const __fieldsFilter = fields.value
				.filter(
					(f: any) =>
						![
							'Id',
							'DataStatus',
							'LockUserId',
							'DeleteUserId',
							'operationType',
							'creationTime',
						].includes(f.name) &&
						(f.isListField || !f.isNullable)
				)
				.map((m: any) => {
					const item: any = {
						type: m.type,
						title: m.displayName,
						field: m.name,
						disabled: m.editDisabled,
						__disabled: m.editDisabled,
						raw: m,
					}

					if (m.type === 'date') {
						item.datetype = 'day'
					}
					if (m.type === 'datetime') {
						item.datetype = 'times'
					}

					// 单、复选框
					if (m.type === 'string' && typeof m.multiple === 'boolean') {
						item.__type = m.multiple ? 'checkbox' : 'radio'
						item.multiple = m.multiple
						item.type = 'select'
						item.data = m.options.map((o: any) => ({label: o, value: o}))
						item.full = true
					}

					if (m.type === 'cascade') {
						console.log(2222, m)

						const options: any[] = []
						item.disabled = false
						item.__disabled = false

						if (departmentInfo.City === null && m.showAreaList.includes('City')) {
							options.push({
								prop: 'City',
								label: '所属城市',
								type: 'select',
								filterable: true,
								cascadeUrl: '/api/platform/department/my-region-names',
								cascadeKeys: ['name', 'name'],
								beforeInitOptions: (val: any, next: any, item: any) => {
									const id = item.options.find((o: any) => o.value === val)?.raw
										.id
									if (id) {
										next.cascadeParams = {guid: id}
									}
								},
								placeholder: '请选择城市',
								options: [],
								isDepartmentEditField: !fields.value.some(
									(f: any) => f.name === 'City' && f.isDepartmentEditField
								),
							})
						}

						if (
							departmentInfo.district === null &&
							m.showAreaList.includes('District')
						) {
							options.push({
								prop: 'District',
								label: '所属区县',
								type: 'select',
								filterable: true,
								cascadeUrl: '/api/platform/department/my-region-names',
								beforeInitOptions: (val: any, next: any, item: any) => {
									const id = item.options.find((o: any) => o.value === val)?.raw
										.id
									if (id) {
										next.cascadeParams = {guid: id}
									}
								},
								placeholder: '请选择区县',
								options: [],
								isDepartmentEditField: !fields.value.some(
									(f: any) => f.name === 'District' && f.isDepartmentEditField
								),
							})
						}

						if (departmentInfo.street === null && m.showAreaList.includes('Street')) {
							options.push({
								prop: 'Street',
								label: '所属街道',
								type: 'select',
								filterable: true,
								cascadeUrl: '/api/platform/department/my-region-names',
								beforeInitOptions: (val: any, next: any, item: any) => {
									const id = item.options.find((o: any) => o.value === val)?.raw
										.id
									if (id) {
										next.cascadeParams = {guid: id}
									}
								},
								placeholder: '请选择街道',
								options: [],
								isDepartmentEditField: !fields.value.some(
									(f: any) => f.name === 'Street' && f.isDepartmentEditField
								),
							})
						}

						if (
							departmentInfo.community === null &&
							m.showAreaList.includes('Community')
						) {
							options.push({
								prop: 'Community',
								label: '所属社区',
								type: 'select',
								filterable: true,
								cascadeUrl: '/api/platform/department/my-region-names',
								beforeInitOptions: (val: any, next: any, item: any) => {
									const id = item.options.find((o: any) => o.value === val)?.raw
										.id
									if (id) {
										next.cascadeParams = {guid: id}
									}
								},
								placeholder: '请选择社区',
								isDepartmentEditField: !fields.value.some(
									(f: any) => f.name === 'Community' && f.isDepartmentEditField
								),
								options: [],
							})
						}

						item.cascadeOptions = options
						item.cascadeKeys = ['name', 'name']
						cascadeOptions.value = options
					}

					return item
				})

			__fieldsFilter.forEach((f: any) => {
				if (
					departmentInfo.district === null &&
					(f.field === 'Street' || f.field === 'Community')
				) {
					f.show = false
				}
				if (departmentInfo.street === null && f.field === 'Community') {
					f.show = false
				}
			})

			console.log('333__fieldsFilter', __fieldsFilter)

			// 新增/编辑
			formArray.value = __fieldsFilter.filter(
				(f: any) =>
					(!['Informant', 'DataSource', 'UpdateTime', 'Editor'].includes(f.raw.name) &&
						(f.raw.hasOwnProperty('isListField') ? f.raw.isListField : true) &&
						(f.raw.hasOwnProperty('isDepartmentField')
							? f.raw.isDepartmentField
							: true)) ||
					f.raw.isUnique ||
					!f.raw.isNullable

				// (f: any) => f.raw.name
			)

			// 必填字段
			formArray.value
				.filter((f: any) => f.raw.isNullable == false)
				.forEach((f: any) => {
					formRules.value[f.raw.name] = [
						{
							required: true,
							message: `请输入${f.raw.displayName}`,
							trigger: 'blur',
						},
					]
				})

			// 整数和小数配置了限制范围

			formArray.value
				.filter(
					(item: any) =>
						(item.type === 'int' || item.type === 'decimal') &&
						item.raw.customValueLimit
				)
				.forEach((f: any) => {
					console.log('整数和小数配置了限制范围', f)

					formRules.value[f.raw.name] = [
						{
							required: !f.raw.isNullable,
							trigger: 'blur',
							validator: (rule: any, value: any, callback: any) => {
								// if (value === 0) {
								// 	callback()
								// } else {
								const numberRules: any = {
									0: '等于',
									1: '不等于',
									2: '大于',
									3: '大于等于',
									4: '小于',
									5: '小于等于',
									6: '包含在',
									7: '不包含在',
								}
								if (!f.raw.isNullable && (value === null || value === undefined)) {
									callback(new Error(`请输入数据`))
								}
								if (f.raw.isNullable && (value === null || value === undefined)) {
									callback()
								}
								if (f.raw.customValueLimit.type === 0) {
									if (!isEqual(value, f.raw.customValueLimit.rangeOne)) {
										callback(
											new Error(
												`允许输入数据${
													numberRules[f.raw.customValueLimit.type]
												}${f.raw.customValueLimit.rangeOne}`
											)
										)
									} else {
										callback()
									}
								} else if (f.raw.customValueLimit.type === 1) {
									if (!isNotEqual(value, f.raw.customValueLimit.rangeOne)) {
										callback(
											new Error(
												`允许输入数据${
													numberRules[f.raw.customValueLimit.type]
												}${f.raw.customValueLimit.rangeOne}`
											)
										)
									} else {
										callback()
									}
								} else if (f.raw.customValueLimit.type === 2) {
									if (!isGreaterThan(value, f.raw.customValueLimit.rangeOne)) {
										callback(
											new Error(
												`允许输入数据${
													numberRules[f.raw.customValueLimit.type]
												}${f.raw.customValueLimit.rangeOne}`
											)
										)
									} else {
										callback()
									}
								} else if (f.raw.customValueLimit.type === 3) {
									if (
										!isGreaterThanOrEqual(
											value,
											f.raw.customValueLimit.rangeOne
										)
									) {
										callback(
											new Error(
												`允许输入数据${
													numberRules[f.raw.customValueLimit.type]
												}${f.raw.customValueLimit.rangeOne}`
											)
										)
									} else {
										callback()
									}
								} else if (f.raw.customValueLimit.type === 4) {
									if (!isLessThan(value, f.raw.customValueLimit.rangeOne)) {
										callback(
											new Error(
												`允许输入数据${
													numberRules[f.raw.customValueLimit.type]
												}${f.raw.customValueLimit.rangeOne}`
											)
										)
									} else {
										callback()
									}
								} else if (f.raw.customValueLimit.type === 5) {
									if (
										!isLessThanOrEqual(value, f.raw.customValueLimit.rangeOne)
									) {
										callback(
											new Error(
												`允许输入数据${
													numberRules[f.raw.customValueLimit.type]
												}${f.raw.customValueLimit.rangeOne}`
											)
										)
									} else {
										callback()
									}
								} else if (f.raw.customValueLimit.type === 6) {
									if (
										!isBetweenExclusive(
											value,
											f.raw.customValueLimit.rangeOne,
											f.raw.customValueLimit.rangeTwo
										)
									) {
										callback(
											new Error(
												`允许输入数据${
													numberRules[f.raw.customValueLimit.type]
												}${f.raw.customValueLimit.rangeOne}与${
													f.raw.customValueLimit.rangeTwo
												}之间`
											)
										)
									} else {
										callback()
									}
								} else if (f.raw.customValueLimit.type === 7) {
									if (
										!isNotBetween(
											value,
											f.raw.customValueLimit.rangeOne,
											f.raw.customValueLimit.rangeTwo
										)
									) {
										callback(
											new Error(
												`允许输入数据${
													numberRules[f.raw.customValueLimit.type]
												}${f.raw.customValueLimit.rangeOne}与${
													f.raw.customValueLimit.rangeTwo
												}之间`
											)
										)
									} else {
										callback()
									}
								}
								// }
							},
						},
					]
				})
			formArray.value.forEach((item) => {
				if (item.raw.calculateRule) {
					item.disabled = true
				}
			})

			console.log('formRules233', formRules)

			console.log(formArray.value)
			// 规则字段
			formArray.value
				.filter((obj1) => ruleResData.some((obj2) => obj1.type === obj2.name))
				.map((item1: any) => {
					const matchedItem = ruleResData.find((item2) => item1.type === item2.name)
					return {
						...item1,
						__rules: matchedItem.expression, // 添加新属性 names
					}
				})
				.forEach((f: any) => {
					console.log(f)
					// if (f.raw.type !== 'birthday') {
					formRules.value[f.raw.name] = [
						{
							type: f.raw.type === 'birthday' ? 'date' : null,
							required: f.raw.isNullable == false ? true : false,
							// trigger: f.raw.type === 'birthday' ? 'change' : 'blur',
							trigger: 'blur',
							validator: (rule: any, value: any, callback: any) => {
								if (value === 0) {
									callback()
								}
								if (!value || value === 'Invalid Date') {
									if (f.raw.isNullable == false) {
										callback(new Error(`请输入${f.raw.displayName}`))
									} else {
										callback()
									}
								} else {
									const Reg = f.__rules ? new RegExp(f.__rules) : /.*/
									const res = Reg.test(value)
									console.log(2222, res)
									console.log(value)

									if (!res && f.raw.type === 'phone') {
										callback(
											new Error(
												`请输入正确的${f.raw.displayName},13888888888,023-88888888`
											)
										)
									} else if (!res && f.raw.type != 'birthday') {
										callback(new Error(`请输入正确的${f.raw.displayName}`))
									} else {
										callback()
									}
									// callback(new Error('Please input the password'))
								}
							},
						},
					]
					// }
				})
			console.log('formArray233', formArray.value)
			console.log('formRules233', formRules.value)

			// 提示文案
			const rules = formArray.value.map((m: any) => m.raw.calculateRule).filter(Boolean)
			console.log(rules)
			if (rules.length > 0) {
				rules.forEach((f: any) => {
					getEvalRPNText(f.rpnExpression)
				})
			}
			console.log(formArray.value)

			// 身份证相关联
			const rulesId = formArray.value
				.map((m: any) => m.raw.relevanceCalculateRule)
				.filter(Boolean)
			if (rulesId.length > 0) {
				rulesId.forEach((f: any) => {
					evnCardId(f)
				})
			}
			// 搜索
			formSearch.value = JSON.parse(
				JSON.stringify(__fieldsFilter.filter((f: any) => f.raw.isQueryField))
			)
			// 请勿删除下行代码 大屏测试用
			// localStorage.setItem('formSearch222', JSON.stringify(formSearch.value))

			let valuesFound: any = ['City', 'District', 'Street', 'Community']
			console.log(
				12333,
				fields.value
					.filter((v: any) => v.isQueryField)
					.every((item: any) => valuesFound.includes(item.name))
			)
			if (
				fields.value
					.filter((v: any) => v.isQueryField)
					.every((f: any) => valuesFound.includes(f.name))
			) {
				formSearch.value.unshift({
					type: 'cascade',
					field: '_cascade',
					title: '所属区域',
					cascadeOptions: [],
				})
			}
			formSearch.value.forEach((f: any) => {
				f.disabled = f.__disabled = false
				if (f.__type === 'checkbox' || f.__type === 'radio') {
					f.full = false
				}
			})

			// 级联选择第一级必须是单选
			const fieldMultiple = formSearch.value
				.map((m: any) => m.raw?.fieldMultipleDto)
				.filter(Boolean)
			console.log(12333, formSearch)
			console.log(12444, fieldMultiple)

			if (fieldMultiple.length > 0) {
				fieldMultiple.forEach((item) => {
					evnFormSearch(item.multipleArrId)
				})
			}
			const fieldMultiple2 = formArray.value
				.map((m: any) => m.raw?.fieldMultipleDto)
				.filter(Boolean)
			console.log('fieldMultiple2', fieldMultiple2)

			if (fieldMultiple2.length > 0) {
				fieldMultiple2.forEach((item) => {
					evnFormSearch2(item.multipleArrId)
				})
			}
			let arrEdit = formArray.value

			let isDepartmentEditFields = arrEdit.filter(
				(item: any) => item.raw?.isDepartmentEditField || item.type === 'cascade'
			)
			// formArray.value = isDepartmentEditFields
			formArrayCop.value = JSON.parse(JSON.stringify(formArray.value))
			console.log(formArray.value)
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}

const evnCardId = (raws: any) => {
	formArray.value.forEach((f: any) => {
		if (raws.id === f.raw.id) {
			console.log(f)
			f.disabled = true
			f.__disabled = true
		}
	})
	console.log(formArray.value)
}
const getQuarterString = (dateStr) => {
	const date = dayjs(dateStr)
	const year = date.year()
	const month = date.month() + 1 // Day.js的month()方法返回的月份是从0开始的

	let quarter
	if (month >= 1 && month <= 3) {
		quarter = '第一季度'
	} else if (month >= 4 && month <= 6) {
		quarter = '第二季度'
	} else if (month >= 7 && month <= 9) {
		quarter = '第三季度'
	} else {
		quarter = '第四季度'
	}

	return `${year}${quarter}`
}

const imageUrl = ref()
const download = async (data: any, field: any) => {
	if (data[field.bm] === '' || data[field.bm] === '[]' || !field) {
		return
	}
	let type = ''
	switch (JSON.parse(data[field.bm])[0]?.extension) {
		case 'xlsx':
			type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			break
		case 'xls':
			type = 'application/vnd.ms-excel'
	}

	const file = JSON.parse(data[field.bm])[0]
	const getDownData = {
		ledgerId: data.ledgerId,
		dataId: data.Id,
		fieldName: field.bm,
		fileName: file.name,
	}
	// 审核逐不让下载
	// const res = await getDownLoadData(getDownData)
	// if (res) {
	// 	const blob = new Blob([res.data], {type})
	// 	const downloadElement = document.createElement('a')
	// 	const href = window.URL.createObjectURL(blob)
	// 	// console.log(href)
	// 	if (field.type === 'images') {
	// 		// imagePreviewModalIsVisible.value = true
	// 		imageUrl.value = href
	// 		return
	// 	}
	// 	downloadElement.href = href
	// 	downloadElement.download = JSON.parse(data[field.bm])[0].name
	// 	document.body.appendChild(downloadElement)
	// 	downloadElement.click()
	// 	document.body.removeChild(downloadElement)
	// 	window.URL.revokeObjectURL(href)
	// }
}
onMounted(async () => {
	tableOffsetHeight.value = Math.ceil(titleList.value.length / 4) * 40 + 15

	batchTypePage.value = route.query.batchType as string
	ledgerIdPage.value = route.query.keyword as string

	const batchType = route.query.batchType as string

	if (batchType === BatchType.noDataUpdate.toString()) {
		isCanNoDataAudit.value = true
		ElMessageBox.alert('该任务为已核实无更新数据任务', '提示')
	}
	getReportInfo()
	getledgerTable()
	getWorkflowTaskLog()
	// CanNoDataAudit(keyword).then((res: any) => {
	// 	isCanNoDataAudit.value = res.data
	// 	if (isCanNoDataAudit.value) {
	// 		ElMessageBox.confirm('该任务为已核实无更新数据任务', '提示')
	// 			.then(() => {})
	// 			.catch(() => {
	// 				// catch error
	// 			})
	// 	}
	// })
})

const batchTypePage: any = ref(null)

const fields: any = ref([])

const addOpen = ref(false)
const showAddLedgerDataModelisVisible = ref(false)
const handleData = ref<any>([])
// const tableFiled = ref<any>([])
const formisDepartmentEditField = ref()
const formArray: any = ref([])
const formArrayCop: any = ref([])
const formRules = ref<any>({
	// name: [{required: true, message: '请输入姓名', trigger: 'blur'}],
})
const addLoading = ref(false)

const ledgerIdPage: any = ref('')

const fixedFields = [
	'UpdateTime',
	'Community',
	'Street',
	'District',
	'City',
	'Informant',
	'Editor',
	'DataSource',
]

const formTitle: any = ref('编辑')
const editData: any = ref({})

function findIndicesInA(A, B) {
	// 创建一个对象来存储每个id在A中的下标列表
	let indicesMap = {}
	// 遍历A来构建这个映射
	A.forEach((item, index) => {
		if (!indicesMap[item.raw?.fieldMultipleDto?.multipleArrId]) {
			indicesMap[item.raw?.fieldMultipleDto?.multipleArrId] = []
		}
		indicesMap[item.raw?.fieldMultipleDto?.multipleArrId].push(index)
	})

	let idToFind = B[0].multipleArrId

	return indicesMap[idToFind] || []
}
// 单条编辑和撤回
const currentEditData: any = ref(null)
const evnGetSelect = (selectId: any, data: any) => {
	const fieldMultiple = formArray.value.map((m: any) => m.raw?.fieldMultipleDto).filter(Boolean)
	const fields = formArray.value.filter((f: any) => selectId?.includes(f.raw.id))[0]
	const fieldsIndex = formArray.value.findIndex((f: any) => selectId?.includes(f.raw.id))
	console.log(fields)
	console.log(fieldsIndex)
	console.log(data)
	if (fields) {
		const targetArr = fieldMultiple.filter((f: any) => fields.raw.id === f.multipleArrId)
		console.log(addFormRef.value)
		const originVal: any = data.data[fields.field]
		const IndexArr = findIndicesInA(formArray.value, targetArr)
		IndexArr.forEach((f: any) => {
			let findChild = formArray.value[f].raw?.fieldMultipleDto?.multipleArr.find(
				(f: any) => f.value === originVal
			)
			console.log(findChild)
			formArray.value[f].data = findChild && findChild.children ? findChild.children : []
			findChild = {}
			console.log(formArray.value[fieldsIndex])
		})

		formArray.value[fieldsIndex].default = data.data[fields.field]
			? String(data.data[fields.field])
			: null

		console.log(formArray.value)
	}
}

const cardTextObj: any = {
	identification_number: '居民身份证号',
	passport: '护照号',
	hk_macao_permit: '港澳居民来往内地通行证',
	taiwan_permit: '台湾居民来往内地通行证',
	居民身份证号: 'identification_number',
	护照号: 'passport',
	港澳居民来往内地通行证: 'hk_macao_permit',
	台湾居民来往内地通行证: 'taiwan_permit',
}
const evnGetRules = (row: any) => {
	console.log(row)
	const target = formArray.value.find(
		(item: any) => item.raw.id === row.raw.validationRule?.relatedFieldId
	)
	const findTargetRule = ruleData.value.find(
		(item: any) => item.name === cardTextObj[target.default]
	)
	console.log(ruleData.value)
	console.log(target)

	if (target) {
		formRules.value[row.raw.name] = [
			{
				required: row.raw.isNullable == false ? true : false,
				trigger: 'blur',
				validator: (rule: any, value: any, callback: any) => {
					let Reg: any
					if (findTargetRule) {
						Reg = new RegExp(findTargetRule.expression)
						const res = Reg.test(value)
						if (
							row.raw.isNullable &&
							(value == '' || value == undefined || value == null)
						) {
							return callback()
						}
						if (!res) {
							callback(new Error(`请输入正确的${row.raw.displayName}`))
						} else {
							callback()
						}
					} else {
						callback()
					}
				},
			},
		]
		console.log(formRules.value)
	}
}

// 编辑了666
const onTableClickButton = async (scope: any) => {
	console.log('onTableClickButton,scope', scope, formArray)

	ledgerIdPage.value = scope.ledgerId

	const {ledgerId} = scope

	if (true) {
		// 获取编辑数据
		const rowData = ref<any>(null)
		const {data} = await ledgerAuditedDataGet(ledgerId, scope.Id)
		console.log('datadatadatadatadatadatadatadata', data)

		rowData.value = data.data
		editData.value = data.data
		const {community, street, district, city} = userStore.getUserInfo
		formArray.value = formArrayCop.value.filter(
			(v: any) => v.raw?.isDepartmentEditField || v.field === '_cascade'
		)
		formisDepartmentEditField.value = formArrayCop.value.filter(
			(v: any) => !v.raw.isDepartmentEditField && v.field !== '_cascade'
		)
		formArray.value.forEach((f: any) => {
			f.disabled = f.__disabled
			if (f.type === 'int' || f.type === 'decimal') {
				f.disabled = f.raw?.calculateRule !== null || f.raw?.editDisabled
			}
			if (f.type === 'age' || f.type === 'sex' || f.type === 'birthday') {
				f.disabled = f.raw?.relevanceCalculateRule !== null
			}
			if (f.type === 'sex') {
				f.__type = f.multiple ? 'checkbox' : 'radio'
				f.multiple = false
				f.type = 'select'
				f.data = [
					{value: '男', lable: '男'},
					{value: '女', lable: '女'},
				]
				f.full = true
			}
			if (f.default == 'null') {
				f.default = ''
			}
			if (f.type === 'cascade') {
				f.cascadeOptions.forEach((option: any) => {
					option.value = scope[option.prop]
					console.log(567, option)
				})
			}
		})
		if (formisDepartmentEditField.value) {
			formisDepartmentEditField.value.forEach((f: any) => {
				f.disabled = f.__disabled
				if (f.type === 'int' || f.type === 'decimal') {
					f.disabled = f.raw?.calculateRule !== null || f.raw?.editDisabled
				}
				if (f.type === 'age' || f.type === 'sex' || f.type === 'birthday') {
					f.disabled = f.raw?.relevanceCalculateRule !== null
				}
				if (f.type === 'sex') {
					f.__type = f.multiple ? 'checkbox' : 'radio'
					f.multiple = false
					f.type = 'select'
					f.data = [
						{value: '男', lable: '男'},
						{value: '女', lable: '女'},
					]
					f.full = true
				}
				if (f.default == 'null') {
					f.default = ''
				}
				if (f.type === 'cascade') {
					f.cascadeOptions.forEach((option: any) => {
						option.value = scope[option.prop]
						console.log(567, option)
					})
				}
			})
		}

		for (let [key, value] of Object.entries(rowData.value)) {
			if (key !== 'Id') {
				formArray.value.forEach((f: any) => {
					if (f.field === key) {
						if (f.type === 'checkbox' || f.__type === 'checkbox') {
							let val: any = value
							if (val) {
								if (val.indexOf('[') != -1) {
									val = JSON.parse(val.replace(/，/g, ','))
									if (val.length == 1) {
										val = val[0]
											.trim()
											.split(',')
											.map((item: any) => item.trim())
									}
									f.default = val
								} else {
									f.default = val.split(',')
								}
							} else {
								f.default = []
							}
						} else {
							f.default = value
						}
					}
					if (f.type === 'date' || f.type === 'datetime') {
						f.datetype = 'day'
						// console.log(value)
					}
					if (f.type === 'datetime' && f.raw.displayForm && f.raw.displayForm === 4) {
						// f.default = value
						// console.log(value)
					}
					if (fixedFields.includes(f.raw.name)) {
						if (f.raw.name === 'Community') {
							f.disabled = community ? true : false
						}
						if (f.raw.name === 'Street') {
							f.disabled = street ? true : false
						}
						if (f.raw.name === 'District') {
							f.disabled = district ? true : false
						}
						if (f.raw.name === 'UpdateTime') {
							f.disabled = true
						}
						if (f.raw.name === 'City') {
							f.disabled = true
						}
					}
				})

				if (formisDepartmentEditField.value) {
					formisDepartmentEditField.value.forEach((f: any) => {
						if (f.field === key) {
							if (f.type === 'checkbox' || f.__type === 'checkbox') {
								let val: any = value
								if (val) {
									if (val.indexOf('[') != -1) {
										val = JSON.parse(val.replace(/，/g, ','))
										if (val.length == 1) {
											val = val[0]
												.trim()
												.split(',')
												.map((item: any) => item.trim())
										}
										f.default = val
									} else {
										f.default = val.split(',')
									}
								} else {
									f.default = []
								}
							} else {
								f.default = value
							}
						}
						if (f.type === 'date' || f.type === 'datetime') {
							f.datetype = 'day'
							// console.log(value)
						}
						if (f.type === 'datetime' && f.raw.displayForm && f.raw.displayForm === 4) {
							// f.default = value
							// console.log(value)
						}
						if (fixedFields.includes(f.raw.name)) {
							if (f.raw.name === 'Community') {
								f.disabled = community ? true : false
							}
							if (f.raw.name === 'Street') {
								f.disabled = street ? true : false
							}
							if (f.raw.name === 'District') {
								f.disabled = district ? true : false
							}
							if (f.raw.name === 'UpdateTime') {
								f.disabled = true
							}
							if (f.raw.name === 'City') {
								f.disabled = true
							}
						}
					})
				}
			}
		}
		formArray.value.forEach((it: any) => {
			if (it.type === 'int' || it.type === 'decimal') {
				it.disabled = it.raw?.calculateRule !== null || it.raw?.editDisabled
			}
			if (it.type === 'datetime' && it.raw.displayForm && it.raw.displayForm === 4) {
				if (data.data.hasOwnProperty(it.field)) {
					if (data.data[it.field]) {
						it.default = parseQuarterToDate(data.data[it.field])
						console.log(parseQuarterToDate(data.data[it.field]))
					}
				}
			}
		})

		if (formisDepartmentEditField.value) {
			formisDepartmentEditField.value.forEach((it: any) => {
				if (it.type === 'int' || it.type === 'decimal') {
					it.disabled = it.raw?.calculateRule !== null || it.raw?.editDisabled
				}
				if (it.type === 'datetime' && it.raw.displayForm && it.raw.displayForm === 4) {
					if (data.data.hasOwnProperty(it.field)) {
						if (data.data[it.field]) {
							it.default = parseQuarterToDate(data.data[it.field])
							console.log(parseQuarterToDate(data.data[it.field]))
						}
					}
				}
			})
		}

		//证件号第一次进来插入正则
		const fildRules = formArray.value
			.map((m: any) => {
				if (m.raw.validationRule) {
					return m
				}
			})
			.filter(Boolean)
		console.log(fildRules)
		if (fildRules.length > 0) {
			fildRules.forEach((f: any) => {
				evnGetRules(f)
			})
		}
		// 级联选择
		const fieldMultiple = formArray.value
			.map((m: any) => m.raw.fieldMultipleDto)
			.filter(Boolean)
		console.log('级联选择', data)
		console.log('级联选择', fieldMultiple)
		console.log('级联选择', formArray.value)

		if (fieldMultiple.length > 0) {
			fieldMultiple.forEach((f) => {
				evnGetSelect(f.multipleArrId, data)
			})
		}
		// 级联选择第一级必须是单选

		formArray.value.forEach((f: any) => {
			if (f.field === '_cascade') {
				f.cascadeOptions.forEach((ff: any) => {
					ff.value = scope[ff.prop]
					ff.default = scope[ff.prop]
				})
			}
		})
		let arrEdit = formArray.value

		let isDepartmentEditFields = arrEdit.filter(
			(item: any) => item.raw?.isDepartmentEditField || item.type === 'cascade'
		)
		// formArray.value = isDepartmentEditFields
		// debugger
		if (scope.operationType == '更新') {
			formArray.value.forEach((item: any) => {
				if (item.raw.editDisabled) {
					item.disabled = true
				}
			})
			if (formisDepartmentEditField.value) {
				formisDepartmentEditField.value.forEach((item: any) => {
					item.disabled = true
				})
			}

			let options = cascadeOptions.value
			options.forEach((item: any) => {
				item.disabled = item.isDepartmentEditField
			})
			cascadeOptions.value = options
		} else {
			formArray.value.forEach((item: any) => {
				item.disabled = false
			})
			if (formisDepartmentEditField.value) {
				formisDepartmentEditField.value.forEach((item: any) => {
					item.disabled = true
				})
			}

			let options = cascadeOptions.value
			options.forEach((item: any) => {
				item.disabled = false
			})
			cascadeOptions.value = options
		}
		console.log(formArray.value)
		console.log(formisDepartmentEditField.value)
		currentEditData.value = scope
		showAddLedgerDataModelisVisible.value = true
		addOpen.value = true
	}
}

watch(
	() => cascadeFormData,
	(val: any, oldVal: any) => {
		console.log('watchCascadeFormData', val, oldVal)
	},
	{immediate: true, deep: true}
)

const dealWithFrom = (formVal: any) => {
	const keys: any = Object.keys(formVal)
	keys.forEach((key: any) => {
		const field = fields.value.find((field: any) => field.name === key)
		if (field && !formVal[key]) {
			if (field.type === 'date' || field.type === 'datetime') {
				formVal[key] = null
			}

			if (field.type === 'string' && field.multiple) {
				formVal[key] = []
			}
		}
	})
	return formVal
}
// 提交了666
const formSubmitAdd = (val: any, formRef: any) => {
	// addLoading.value = true
	// 表单验证
	formRef
		?.validate()
		.then((validate: boolean) => {
			console.log('cascadeFormData提交11', cascadeFormData)

			// return
			let timesObj: any = {}
			formArray.value.forEach((m: any) => {
				if (m.raw.displayForm && m.raw.displayForm === 4) {
					const times = addFormRef.value.getFieldValue(m.field)
					timesObj[m.field] = times ? formatDateWithQuarter(times) : null
				}
			})
			let form: any = dealWithFrom(toRaw(val))
			console.log('验证通过:', form, timesObj, currentEditData)

			// return

			console.log('cascadeFormData提交11', cascadeFormData)
			let newFormData: any = {
				City: cascadeFormData.value.City || null,
				District: cascadeFormData.value.District || null,
				Street: cascadeFormData.value.Street || null,
				Community: cascadeFormData.value.Community || null,
			}

			if (Object.keys(cascadeFormData.value).length > 0) {
				form = Object.assign(form, newFormData)
			}

			delete form._cascade
			for (let key in form) {
				if (
					form[key] === '' ||
					form[key] === null ||
					form[key] === undefined ||
					form[key] === 'undefined' ||
					form[key] === 'null' ||
					(typeof form[key] === 'object' && form[key] == '')
				) {
					form[key] = null
				} else {
					form[key] = form[key].toString()
				}
			}
			console.log('form22', form)
			// return

			if (form) {
				const keys = Object.keys(form)
				for (let i = 0; i < keys.length; i++) {
					if (Array.isArray(form[keys[i]])) {
						console.log(form[keys[i]])
						if (form[keys[i]].length == 0) {
							form[keys[i]] = null
						} else {
							form[keys[i]] = form[keys[i]].join(',')
						}
					}
				}
				console.log('编辑233', currentEditData.value, form)
				// return
				ledgerAuditedDataPUTOther(ledgerIdPage.value, currentEditData.value.Id, {
					...form,
					...timesObj,
				}).then((res) => {
					ElMessage.success('修改成功！')
					addOpen.value = false
					showAddLedgerDataModelisVisible.value = false
					addLoading.value = false
					// cascadeData.value = {}
					// getLedgerAuditTableList()
					getList()
					cascadeFormData.value = {}
					// emits('uploadLedgenTbaleListData', true)
					closed()
				})
			}
		})
		.catch((err: any) => {
			console.log(err)
			console.log('验证失败')
			addLoading.value = false
		})
}
const closed = () => {
	// submitAllModalIsVisible.value = false
	// submitAllLoading.value = false
	// submitDelVisible.value = false
	// submitAllRemark.value = null
}
const tableRef: any = ref(null)

const checkIdCard = (idCard: any) => {
	const year = idCard.substring(6, 10)
	const month = idCard.substring(10, 12)
	const day = idCard.substring(12, 14)
	const birthDateString = `${year}-${month}-${day}`
	const birthDate = new Date(birthDateString)
	const today = new Date()

	// 校验出生日期是否早于当前日期
	if (birthDate > today) {
		return {isValid: false, message: '出生日期晚于当前日期'}
	}
	// 计算年龄
	let age = today.getFullYear() - birthDate.getFullYear()
	const m = today.getMonth() - birthDate.getMonth()
	// if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
	// 	age--
	// }

	return {
		isValid: true,
		birthDate: birthDateString,
		age: age,
		message: '身份证号有效',
	}
}

const parseIdCard = (idCard: any) => {
	const year = idCard.substring(6, 10)
	const month = idCard.substring(10, 12)
	const day = idCard.substring(12, 14)
	const birthDate = `${year}-${month}-${day}`

	const genderCode = idCard.substring(16, 17)
	const gender = genderCode % 2 === 0 ? '女' : '男'

	const today = new Date()
	const birthDateObj = new Date(birthDate)
	const age = today.getFullYear() - birthDateObj.getFullYear()
	console.log(age)
	console.log(gender)

	const m = today.getMonth() - birthDateObj.getMonth()
	console.log(today.getDate())
	console.log(birthDateObj.getDate())

	// if (m < 0 || (m === 0 && today.getDate() < birthDateObj.getDate())) {
	// 	age--
	// }
	return {
		age,
		birthDate,
		gender,
	}
}

const remoteMethod = (val: any) => {
	if (val) {
		let obj: any = {}
		ledgerAuxiliaryFillingList.value[0].configs.forEach((el: any) => {
			obj[el.sourceFiled.name] = el.field.name
		})
		// if (ledgerAuxiliaryFillingList.value[0].field) {
		// 	ledgerAuxiliaryFillingList.value[0].selectField = ledgerAuxiliaryFillingList.value[0].field
		// }
		selectFields.value = obj
		let searchPms
		if (auxiliaryFillingType.value === 0) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].configs.filter(
						(e) => e.fieldId === ledgerAuxiliaryFillingList.value[0].fieldId
					)[0].sourceFiled.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}
		if (auxiliaryFillingType.value === 1) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].selectField.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}
		if (auxiliaryFillingType.value === 2) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].field.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}

		getSingleLedgerData(searchPms)
			.then((res: any) => {
				if (res.status === 200) {
					// if (res.data.data.length === 0) return ElMessage.warning('未查询到数据。')
					// if (res.data.data === null || Object.keys(res.data.data).length === 0)
					// 	return ElMessage.warning('未查询到数据。')

					formArray.value.forEach((v: any) => {
						if (v.field === 'District' || v.field === 'City') return
						if (Object.values(obj).includes(v.field)) {
							// v.default = res.data[v.field]
							v.data = res.data.data.map((item: any) => ({
								label: res.data.preview
									.map((x) => item[x])
									.toString()
									.split(',')
									.join(' '),
								value: item,
							}))
						}
					})
					console.log(formArray.value)

					// addFormRef.value?.reset()
				} else {
					formArray.value.forEach((v: any) => {
						if (v.field === 'District' || v.field === 'City') return
						if (Object.values(obj).includes(v.field)) {
							// v.default = res.data[v.field]
							v.data = []
						}
					})
				}
			})
			.finally(() => {})
	}
}

// 失去焦点---辅助填报功能
const ledgerAuxiliaryFillingList = ref<any[]>([])
const selectFields = ref()
const onBlur = (val: any, f: any, v: string, row: any) => {
	if (v === '') return
	formArray.value.forEach((v: any) => {
		if (v.type === 'int' || v.type === 'decimal') {
			let val = truncateDecimal(addFormRef.value.getFieldValue(v.field), v.raw.precision)
			if (isNaN(val)) {
				val = null
			}
			addFormRef.value.setFieldValue(v.field, val)
		}
	})
}

const showAuxiliaryFillingDataModal = ref(false)

const auxiliaryFillingDataSelected = ref([])
const getSingleData = ref<any>()
const onAuxiliaryFillingSearch = (field, val) => {
	if (val) {
		showAuxiliaryFillingDataModal.value = true
		let obj: any = {}
		ledgerAuxiliaryFillingList.value[0].configs.forEach((el: any) => {
			obj[el.sourceFiled.name] = el.field.name
		})
		// if (ledgerAuxiliaryFillingList.value[0].field) {
		// 	ledgerAuxiliaryFillingList.value[0].selectField = ledgerAuxiliaryFillingList.value[0].field
		// }
		selectFields.value = obj
		let searchPms
		if (auxiliaryFillingType.value === 0) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].configs.filter(
						(e) => e.fieldId === ledgerAuxiliaryFillingList.value[0].fieldId
					)[0].sourceFiled.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}
		if (auxiliaryFillingType.value === 1) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].selectField.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}
		if (auxiliaryFillingType.value === 2) {
			searchPms = {
				ledgerId: route.query.ledgerId,
				maxResultCount: 9999,
				skipCount: 0,
				SourceId:
					ledgerAuxiliaryFillingList.value[0].sourceLedgerId ??
					ledgerAuxiliaryFillingList.value[0].sourceTableDataSetId,
				whereFields: {
					[ledgerAuxiliaryFillingList.value[0].field.name]: {
						0: val,
					},
				},
				selectFields: obj,
				keyword: val,
			}
		}

		getSingleLedgerData(searchPms)
			.then((res: any) => {
				if (res.status === 200) {
					getSingleData.value = res.data
				} else {
				}
			})
			.finally(() => {})
	} else {
		ElMessage.warning('请输入内容')
	}
}

const onAuxiliaryFillingCOnfirm = (val) => {
	if (auxiliaryFillingDataSelected.value.length === 0) {
		return ElMessage.warning('请选择要填充的数据')
	}
	showAuxiliaryFillingDataModal.value = false
	// 开始填充
	// auxiliaryFillingDataSelected
	const data = auxiliaryFillingDataSelected.value[0]
	formArray.value.forEach((v: any) => {
		if (v.field === 'District' || v.field === 'City') return

		Object.keys(selectFields.value).map((keys) => {
			if (v.field === selectFields.value[keys]) {
				v.default = data[keys]
			}
		})

		// if (Object.keys(data).includes(v.field)) {
		// 	v.default = data[v.field]
		// }
	})

	addFormRef.value?.reset()

	// 全部重置
	auxiliaryFillingDataSelected.value = []
	getSingleData.value = null
}
const onFormReset = () => {
	formArray.value.forEach((f: any) => {
		if (f.field === '_cascade') {
			f.cascadeOptions.forEach((ff: any) => {
				cascadeRef.value?.setValue(ff.prop, ff.default)
			})
		}
	})
}

watch(
	() => [userStore.getCurrentDepartmentId],
	(val: Array<any>) => {
		const [visible, departmentId] = val
		console.log('visible, departmentId', val)
		if (visible) {
			if (departmentId) {
				initAudit(() => onOpened())
			}
		} else {
			window.clearTimeout(timer.value)
		}
	}
)

const onTemporarySave = (val: any) => {
	let form: any = dealWithFrom(toRaw(val))
	if (Object.keys(cascadeData.value).length > 0) {
		form = Object.assign(form, cascadeData.value)
	}

	if (
		Object.keys(form).every(
			(keys) => form[keys] === null || form[keys] === '' || form[keys] === undefined
		)
	) {
		return ElMessage.warning('无暂存数据')
	}
	const historyData = JSON.parse(localStorage.getItem('temporarySaveData') || '[]')
	let data = null
	if (historyData && historyData.length > 0) {
		const d = historyData.filter((item: any) => item.ledgerId !== route.query.ledgerId)
		data = [
			...d,
			{
				ledgerId: route.query.ledgerId,
				data: form,
			},
		]
	} else {
		data = [
			{
				ledgerId: route.query.ledgerId,
				data: form,
			},
		]
	}
	localStorage.setItem('temporarySaveData', JSON.stringify(data))
	ElMessage.success('暂存成功')
}

// 新的函数
const cascadeOptions: any = ref([])
const isClear = ref(false)

const onClean = (val) => {
	console.log(cascadeFormData.value)
	console.log(cascadeOptions.value)
	isClear.value = true
	nextTick(() => (isClear.value = false))
}

const addFormRef = ref()

function truncateDecimal(num: any, maxDecimalPlaces = 3) {
	console.log(num)
	// 转换为字符串
	const strNum = String(num)

	// 找到小数点的位置
	const decimalIndex = strNum.indexOf('.')

	// 如果没有小数点，直接返回原始字符串
	if (decimalIndex === -1) {
		return parseFloat(strNum)
	}

	// 计算小数部分的长度
	const decimalLength = strNum.length - decimalIndex - 1

	// 如果小数部分长度已经超过最大小数位数，则截断
	if (decimalLength > maxDecimalPlaces) {
		return parseFloat(strNum.slice(0, decimalIndex + 1 + maxDecimalPlaces))
	}

	// 否则返回原始字符串
	return parseFloat(strNum)
}
const evalRPN = (expression: string) => {
	let str = expression
	const split = expression.match(/[0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12}/g)
	const fields = formArray.value.filter((f: any) => split?.includes(f.raw.id))
	const target = formArray.value.find(
		(f: any) => f.raw.calculateRule?.rpnExpression === expression
	)

	fields.forEach((f: any) => {
		// str = eval(
		// 	`str.replace(/${f.raw.id}/, ${Number(addFormRef.value.getFieldValue(f.field) || 0)})`
		// )
		str = useEvalHook(
			`argument[0].replace(/${f.raw.id}/, ${Number(
				addFormRef.value.getFieldValue(f.field) || 0
			)})`,
			str
		)
	})

	let val: any = 0
	const splitArray: any = str
		.split(' ')
		.map((m: any) => Number(m))
		.filter((item: any) => !Object.is(item, NaN))
	if (str.includes('max')) {
		val = Math.max(...splitArray)
	} else if (str.includes('min')) {
		val = Math.min(...splitArray)
	} else {
		val = rpn.rpnCalculate(str)
	}

	console.log(str, fields, target, target.field)
	if (target.type === 'int') {
		val = val.toFixed(0)
	} else {
		val = truncateDecimal(val, target.raw.precision)
	}
	console.log('truncateDecimal(val)', val)

	addFormRef.value.setFieldValue(target.field, val)
}

const evalRPNForCunShe = (expression: string) => {
	let str = expression
	const split = expression.match(/[0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12}/g)
	const fields = formisDepartmentEditField.value.filter((f: any) => split?.includes(f.raw.id))
	const target = formisDepartmentEditField.value.find(
		(f: any) => f.raw.calculateRule?.rpnExpression === expression
	)

	fields.forEach((f: any) => {
		// str = eval(
		// 	`str.replace(/${f.raw.id}/, ${Number(addFormRef.value.getFieldValue(f.field) || 0)})`
		// )
		str = useEvalHook(
			`argument[0].replace(/${f.raw.id}/, ${Number(
				addFormRef.value.getFieldValue(f.field) || 0
			)})`,
			str
		)
	})

	let val: any = 0
	const splitArray: any = str
		.split(' ')
		.map((m: any) => Number(m))
		.filter((item: any) => !Object.is(item, NaN))
	if (str.includes('max')) {
		val = Math.max(...splitArray)
	} else if (str.includes('min')) {
		val = Math.min(...splitArray)
	} else {
		val = rpn.rpnCalculate(str)
	}

	console.log(str, fields, target, target.field)
	if (target.type === 'int') {
		val = val.toFixed(0)
	} else {
		val = truncateDecimal(val, target.raw.precision)
	}
	console.log('truncateDecimal(val)', val)

	addFormRef.value.setFieldValue(target.field, val)
}
const getEvalRPNText = (expression: string) => {
	console.log(expression)
	let str = expression
	const split: any = expression.match(/[0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12}/g)
	let fields = formArray.value.filter((f: any) => split?.includes(f.raw.id))
	const target = formArray.value.find(
		(f: any) => f.raw.calculateRule?.rpnExpression === expression
	)

	let newUuidParts = split
		.map((uuid, index) => {
			if (index < fields.length) {
				return fields[index].title
			} else {
				return uuid
			}
		})
		.join(' ')
	console.log(newUuidParts)

	const replacements = {
		'+': '求和',
		'/': '求平均',
		max: '求最大值',
		min: '求最小值',
	}
	if (str.includes('max')) {
		newUuidParts = target.title + '=' + newUuidParts + ' 求最大值'
	} else if (str.includes('min')) {
		newUuidParts = target.title + '=' + newUuidParts + ' 求最小值'
	} else if (str.includes('+') && !str.includes('/')) {
		newUuidParts = target.title + '=' + newUuidParts + ' 求和'
	} else if (str.includes('/') && str.includes('/')) {
		newUuidParts = target.title + '=' + newUuidParts + ' 求平均值'
	}

	// if (newUuidParts.includes('max')) {
	// 	newUuidParts= target.title + "=" + newUuidParts.replace(/max/g, replacements['max'])
	// } else if (newUuidParts.includes('min')) {
	// 	newUuidParts= target.title + "=" + newUuidParts.replace(/min/g, replacements['min'])
	// }  else if (newUuidParts.includes('+')) {
	// 	newUuidParts =	target.title + "=" + newUuidParts.replace(/\+/g, replacements['+'])
	// }  else if (newUuidParts.includes('/')) {
	// 	newUuidParts= target.title + "=" + newUuidParts.replace(/\//g, replacements['/'])
	// }
	console.log(newUuidParts)
	console.log(target)
	console.log(formArray.value)

	formArray.value.forEach((item: any) => {
		if (item.raw.id === target.raw.id) {
			if (target.type === 'int') {
				item.tip = newUuidParts + ',结果四舍五入'
			} else {
				item.tip = newUuidParts
			}
		}
	})
}

const ruleData = ref()

const evalCardSelect = (row: any, val: any) => {
	const fieldMultiple = formArray.value.filter((item: any) => item.raw.validationRule)
	const target = fieldMultiple.find(
		(f: any) => f.raw.validationRule?.relatedFieldId === row.raw.id
	)
	const findTargetRule = ruleData.value.find((item: any) => item.name === val)
	if (target && findTargetRule) {
		addFormRef.value.setFieldValue(target.field, '')
		console.log(findTargetRule)
		formRules.value[target.raw.name] = [
			{
				required: target.raw.isNullable == false ? true : false,
				trigger: 'blur',
				validator: (rule: any, value: any, callback: any) => {
					const Reg = new RegExp(findTargetRule.expression)
					const res = Reg.test(value)
					if (
						target.raw.isNullable &&
						(value == '' || value == undefined || value == null)
					) {
						return callback()
					}
					if (!res) {
						callback(new Error(`请输入正确的${target.raw.displayName}`))
					} else {
						callback()
					}
				},
			},
		]
	}
}
const evalSelect = (raws: any) => {
	const fieldMultiple = formArray.value.map((m: any) => m.raw?.fieldMultipleDto).filter(Boolean)
	console.log(raws)
	console.log(fieldMultiple)

	const target = fieldMultiple.find((f: any) => f.multipleArrId === raws.raw.id)
	console.log(target)

	if (target && raws.raw.id === target.multipleArrId) {
		const targetArr = fieldMultiple.filter((f: any) => target.multipleArrId === f.multipleArrId)
		console.log(targetArr)
		const originVal: any = addFormRef.value.getFieldValue(raws.field)
		console.log(originVal)
		console.log(targetArr)

		const IndexArr = findIndicesInA(formArray.value, targetArr)
		IndexArr.forEach((f: any) => {
			let findChild = formArray.value[f].raw?.fieldMultipleDto?.multipleArr.find(
				(f: any) => f.value === originVal
			)
			formArray.value[f].data = findChild && findChild.children ? findChild.children : []
			addFormRef.value.setFieldValue(formArray.value[f].field, '')
			findChild = {}
		})

		console.log(formArray.value)
	}
}
const evnFormSearch2 = (expression: string) => {
	const fields = formArray.value.filter((f: any) => expression?.includes(f.raw?.id))

	if (fields && fields.length !== 0) {
		const Indexs = formArray.value.findIndex((f: any) => f.raw?.id === fields[0].raw?.id)

		formArray.value[Indexs].multiple = false
	}
}
function formatDateWithQuarter(dateTimeStr: any) {
	// 1. 解析日期时间字符串
	const [year, month, day, ...rest] = dateTimeStr.split('-').map(Number)
	const date = new Date(year, month - 1, day, ...rest.slice(0, 3)) // 注意月份是从0开始的

	// 2. 计算季度
	let quarter
	if (month >= 1 && month <= 3) {
		quarter = '第一季度'
	} else if (month >= 4 && month <= 6) {
		quarter = '第二季度'
	} else if (month >= 7 && month <= 9) {
		quarter = '第三季度'
	} else {
		quarter = '第四季度'
	}

	// 3. 格式化日期和季度
	return `${year}年${quarter}`
}
function parseQuarterToDate(quarterStr: any) {
	// 假设输入格式为 "YYYY年一/二/三/四季度"，其中YYYY是年份
	const match = quarterStr.match(/^(\d{4})年第(一|二|三|四)季度$/)
	if (!match) {
		throw new Error('无效的季度字符串格式')
	}

	const year = parseInt(match[1], 10)
	const quarterChinese = match[2] // 获取汉字季度

	// 根据汉字季度确定月份
	let month
	switch (quarterChinese) {
		case '一':
			month = 1
			break
		case '二':
			month = 4
			break
		case '三':
			month = 7
			break
		case '四':
			month = 10
			break
		default:
			// 理论上不会执行到这里，因为正则已经确保了quarterChinese是有效的汉字季度
			throw new Error('内部错误：无效的季度值')
	}

	// 创建一个新的Date对象，设置为该季度的第一个月的第一天（即1号）
	const date = new Date(year, month - 1, 1, 0, 0, 0) // 月份在Date对象中是从0开始的

	// 格式化日期时间字符串
	const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
		2,
		'0'
	)}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(
		2,
		'0'
	)}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`

	return formattedDate
}

// 处理编辑
const formChange = (val: any, field: string, type: any, _form: any, raws: any) => {
	formArray.value.forEach((v: any) => {
		if (v.type === 'int' || v.type === 'decimal') {
			let val = truncateDecimal(addFormRef.value.getFieldValue(v.field), v.raw.precision)
			if (isNaN(val)) {
				val = null
			}
			addFormRef.value.setFieldValue(v.field, val)
		}
	})
	console.log('当前修改表单:', val, field, type, raws)
	if (type) {
		const _field = formArray.value.find((f: any) => f.field === field)
		const rules = formArray.value.map((m: any) => m.raw.calculateRule).filter(Boolean)

		if (rules.length > 0) {
			rules.forEach((f: any) => {
				evalRPN(f.rpnExpression)
			})
		}

		const rulesForCunshe = formisDepartmentEditField.value
			.map((m: any) => m.calculateRule || m.raw?.calculateRule)
			.filter(Boolean)
		if (rulesForCunshe.length > 0) {
			rulesForCunshe.forEach((f: any) => {
				evalRPNForCunShe(f.rpnExpression)
			})
		}
	}

	if (type === 'select' && raws.raw.options.length >= 1 && !raws.raw.fieldMultipleDto) {
		const fieldMultiple = formArray.value
			.map((m: any) => m.raw.fieldMultipleDto)
			.filter(Boolean)
		if (fieldMultiple.length > 0) {
			evalSelect(raws)
		}
	}

	if (type === 'certificate') {
		const fieldMultiple = formArray.value
			.map((item: any) => item.raw.validationRule)
			.filter(Boolean)
		if (fieldMultiple.length > 0) {
			evalCardSelect(raws, val)
		}
	}
}

const onCascadeChange = (val: any) => {
	console.log('onCascadeChange', val)

	cascadeFormData.value = val
}

const getHandData = (data: any) => {
	// 过滤有权限字段
	const {tableFieldGroups} = data.tableInfo
	tableFiled.value = data.tableInfo.fields.filter(
		(f: any) => f.isListField && f.name !== 'UpdateTime'
	)
	tableGroup.value = data.tableInfo.tableFieldGroups

	let tableFieldField: any = []
	tableFieldGroups.forEach((item: any) => {
		tableFieldField = [
			...tableFieldField,
			...item.tableFields.filter((f: any) => f.isListField),
		]
	})

	filterField.value = tableFiled.value.filter((f: any) => {
		return !tableFieldField.some((f2: any) => f2.id === f.id)
	})
	const group: any = []
	tableFieldGroups.forEach((item: any, index: any) => {
		if (item.tableFields) {
			group.push(
				Object.assign(item, {
					id: item.id,
					label: item.name,
					parentId: item.parentId,
					tableFields: toRaw(item.tableFields.filter((f: any) => f.isListField)),
				})
			)
			item.tableFields.forEach((tf: any) => {
				if (tf.isListField)
					group.push({
						label: tf.displayName,
						field: tf.name,
						parentId: item.id,
						id: tf.id,
						...tf,
					})
			})
		} else {
			group.push({label: item.name, id: item.id, parentId: item.parentId})
		}
	})
	const LedgerGroup = useArrayToTree(group, 'id', 'parentId', 'label', false)

	filterField.value = [...filterField.value, ...LedgerGroup]

	filterField.value.sort((a: any, b: any) => a.sort - b.sort)
	let arr: any = []
	filterField.value.forEach((item: any, index: any) => {
		if (!item.child) {
			arr.push(item)
		} else {
			if (arr.length > 0) {
				handleData.value.push(arr)
			}
			handleData.value.push(item)
			// activeNames.value.push(item.id)
			recursionFun(handleData.value[handleData.value.length - 1])
			// 使用递归去添加item数据
			arr = []
		}
		if (filterField.value.length - 1 == index) {
			if (arr.length > 0) {
				handleData.value.push(arr)
			}
			arr = []
		}
	})
}

const onIdentificationClick = (f: any, v: string, row: any) => {
	console.log(23, f)
	console.log(23, v)
	console.log(23, row)
	if (v === '') return
	formArray.value.forEach((v: any) => {
		if (v.type === 'int' || v.type === 'decimal') {
			let val: any = truncateDecimal(addFormRef.value.getFieldValue(v.field), v.raw.precision)
			if (isNaN(val)) {
				val = null
			}
			addFormRef.value.setFieldValue(v.field, val)
		}
	})

	if (row.type === 'identification_number') {
		const displayNameArr = formArray.value
			.filter((item: any) => {
				return item.type === 'birthday' || item.type === 'age' || item.raw.type === 'sex'
			})
			.filter((item: any) => {
				return row.id === item.raw?.relevanceCalculateRule?.relevanceFieldId
			})
		addFormRef.value.validateValue(f).then((res) => {
			if (!checkIdCard(v).isValid) {
				return ElMessage.warning('出生日期晚于当前日期,请重新输入')
			}
			console.log(displayNameArr)
			displayNameArr.forEach((item: any) => {
				if (item.type === 'age') {
					addFormRef.value.setFieldValue(item.field, parseIdCard(v).age)
				} else if (item.type === 'birthday') {
					addFormRef.value.setFieldValue(item.field, parseIdCard(v).birthDate)
				} else if (item.raw.type === 'sex') {
					console.log(parseIdCard(v).gender)
					addFormRef.value.setFieldValue(item.field, parseIdCard(v).gender)
				}
			})
		})
		// return
	}
}
</script>
<template>
	<div class="report-task-detail">
		<div v-show="!showProgress" class="open-history" @click="showProgress = true">
			<span
				>展开查看任务流程
				<i
					class="icon i-ic-baseline-keyboard-double-arrow-right"
					style="position: relative; left: -1px"
				></i>
			</span>
		</div>
		<div v-show="showProgress" class="right">
			<Block
				ref="blockHistoryRef"
				:title="'流程说明'"
				:enable-fixed-height="true"
				:enable-expand-content="false"
				:enable-close-button="false"
			>
				<template #topRight>
					<span
						style="
							cursor: pointer;
							display: flex;
							align-items: center;
							color: var(--z-main);
						"
						@click="showProgress = false"
					>
						<i class="icon i-ic-baseline-keyboard-double-arrow-left"></i>
						收起
					</span>
				</template>
				<!-- <Record :title="''" :sort="'asc'" :data="flowPathData">
				</Record> -->
				<BusinessProcess :data="flowPathData" :is-ledger-recode="true"></BusinessProcess>
			</Block>
		</div>

		<div class="left" style="width: calc(100% - 300px)">
			<Block
				ref="blockRef"
				title="任务信息"
				:enable-fixed-height="true"
				:enable-expand-content="false"
				@heightChanged="onBlockHeightChanged"
			>
				<template #topRight>
					<div v-if="showButton && canAudit">
						<el-button
							ml-10px
							size="small"
							type="warning"
							@click="audit('disagree')"
							:loading="isPushing"
							>驳回</el-button
						>
						<el-button
							size="small"
							type="primary"
							@click="audit('agree')"
							:loading="isPushing"
							>通过</el-button
						>
					</div>
				</template>
				<div class="info-over-view">
					<InfoOverViewComp
						:data="_detailInfo"
						:titles="titleList"
						@onToggleVisible="onToggleInfoOverViewVisible"
					/>
				</div>
				<div class="info df aic">
					<div class="label">操作类型：</div>
					<el-checkbox-group v-model="checkList" size="small" @change="changeCheckList">
						<el-checkbox
							:label="item"
							v-for="(item, index) in checked"
							:key="checked"
							border
							style="margin-right: 10px"
						>
							{{ item == 0 ? '新增' : item == 1 ? '更新' : '删除' }}</el-checkbox
						>
					</el-checkbox-group>
					<div>
						<el-button
							v-if="tableData.length > 1"
							size="small"
							type="primary"
							@click="selectClick('agree')"
							:loading="isPushing"
							>单条审核</el-button
						>
					</div>
				</div>
				<TableV2
					ref="tableRef"
					:height="tableHeight"
					:columns="tableColumns"
					:defaultTableData="tableData"
					:headers="{Urlkey: 'ledger'}"
					:enableToolbar="false"
					:enable-create="false"
					row-key="id"
					:enable-edit="false"
					:enable-delete="false"
					:enableSelection="true"
					:selectable="selectable"
					:enableIndex="false"
					@before-complete="onTableBeforeComplete"
					@selection-change="handleSelectionChange"
					:req-params="reqParams"
				>
					<!-- <template #operationType="{row}">
							{{ row.operationType }}
						</template> -->
					<template #buttons="{row}" v-if="batchTypePage === '0'">
						<el-button
							type="primary"
							:disabled="row.operationType === '删除'"
							size="small"
							@click="onTableClickButton(row)"
						>
							编辑
						</el-button>
					</template>

					<template #[filed.bm]="{row}" v-for="filed in TilingColData">
						<!-- <template v-if="filed.type === 'datetime'">
							<p v-if="filed.displayForm === 1">
								{{ dayjs(row[filed.bm]).format('YYYY年MM月DD日') }}
							</p>
							<p v-if="filed.displayForm === 2">
								{{ dayjs(row[filed.bm]).format('YYYY年MM月') }}
							</p>
							<p v-if="filed.displayForm === 3">
								{{ dayjs(row[filed.bm]).format('YYYY年') }}
							</p>
							<p v-if="filed.displayForm === 4">
								{{ getQuarterString(row[filed.bm]) }}
							</p>
						</template> -->
						<template v-if="filed.type === 'images' || filed.type === 'attachments'">
							<el-link @click="download(row, filed)">
								{{
									row[filed.bm] === '[]' ||
									row[filed.bm] === '' ||
									row[filed.bm] === undefined ||
									row[filed.bm] === null
										? '-'
										: JSON.parse(row[filed.bm])[0].name
								}}
							</el-link>
						</template>
					</template>
				</TableV2>
				<Pagination
					:total="pagination.total"
					:current-page="pagination.page"
					:page-size="pagination.size"
					@current-change="onPaginationChange($event, 'page')"
					@size-change="onPaginationChange($event, 'size')"
				>
				</Pagination>
			</Block>
		</div>

		<!-- 编辑弹窗 666 -->
		<Dialog
			v-model="addOpen"
			title="编辑"
			width="600"
			class="auth-edit-dialog"
			:enable-button="false"
			@close=";(addOpen = false), (showAddLedgerDataModelisVisible = false)"
			@click-close=";(addOpen = false), (showAddLedgerDataModelisVisible = false)"
		>
			<collapseForm
				v-if="showAddLedgerDataModelisVisible"
				ref="addFormRef"
				label-position="right"
				label-width="100"
				submitIcon="i-ic-twotone-published-with-changes"
				:formGroupArray="handleData"
				:ledgerAuxiliaryFillingList="ledgerAuxiliaryFillingList"
				:fields="tableFiled"
				:formisDepartmentEditField="formisDepartmentEditField"
				:form="formArray.map((v:any) => {
				return {
					...v,
					// disabled: v.field === 'District' || v.field === 'City' || v.field === 'Street' || v.field === 'Community',
				}
			})"
				:rules="formRules"
				:showButton="true"
				:loading="addLoading"
				@onClean="onClean"
				@onChange="formChange"
				@onSubmit="formSubmitAdd"
				@onBlur="onBlur"
				@onReset="onFormReset"
				@onSearch="onAuxiliaryFillingSearch"
				@onIdentificationClick="onIdentificationClick"
			>
				<template v-if="departmentInfo.city === null" #City="scope">
					<Cascade
						ref="cascadeRef"
						v-model="editData"
						:options="cascadeOptions"
						:isClear="isClear"
						:keys="['name', 'name']"
						:vertical="true"
						@change="onCascadeChange"
					></Cascade>
				</template>

				<template v-if="departmentInfo.district === null" #District="scope">
					<Cascade
						ref="cascadeRef"
						v-if="departmentInfo.city !== null"
						v-model="editData"
						:options="cascadeOptions"
						:isClear="isClear"
						:keys="['name', 'name']"
						:form-data="cascadeFormData"
						:vertical="true"
						@change="onCascadeChange"
					></Cascade>
				</template>

				<template v-if="departmentInfo.street === null" #Street="scope">
					<Cascade
						v-if="departmentInfo.district !== null"
						ref="cascadeRef"
						v-model="editData"
						:options="cascadeOptions"
						:isClear="isClear"
						:keys="['name', 'name']"
						:form-data="cascadeFormData"
						:vertical="true"
						@change="onCascadeChange"
					></Cascade>
				</template>

				<template v-if="departmentInfo.community === null" #Community="scope">
					<Cascade
						v-if="departmentInfo.street !== null"
						ref="cascadeRef"
						v-model="editData"
						:options="cascadeOptions"
						:isClear="isClear"
						:keys="['name', 'name']"
						:form-data="cascadeFormData"
						:vertical="true"
						@change="onCascadeChange"
					></Cascade>
				</template>
			</collapseForm>
		</Dialog>

		<Dialog
			v-model="auditModalIsVisible"
			title="提交审核"
			width="800"
			:destroy-on-close="true"
			:enableButton="false"
			@close="auditModalIsVisible = false"
		>
			<FormComp
				:form="auditformArray"
				:rules="auditformRules"
				:showResetButton="false"
				:loading="isPushing"
				submitText="提交"
				@onSubmit="auditformSubmit"
			>
			</FormComp>
		</Dialog>
		<Dialog
			v-model="selectVisible"
			title="数据审核"
			width="800"
			:destroy-on-close="true"
			:enableButton="false"
			@close="selectVisible = false"
		>
			<FormComp
				:form="process"
				:rules="auditformRules"
				:showResetButton="false"
				:clearClick="false"
				:loading="isPushing"
				submitText="提交"
				@closeClick="closeClick"
				@onSubmit="processSure"
			>
			</FormComp>
		</Dialog>
	</div>
</template>
<style lang="scss" scoped>
.auth-edit-dialog {
	.cascade-component {
		.el-form-item__label {
			color: rgb(96, 98, 102);
			font-weight: 500;
			font-size: 14px;
			justify-content: flex-start;
		}

		&.vertical > div {
			&:not(:last-child) {
				margin-bottom: 15px;
			}
		}
	}

	.el-form-item:first-child {
		/* margin-bottom: 0; */
	}
}
.report-task-detail {
	display: flex;

	.left {
		flex: 1;
	}

	.right {
		margin-right: 20px;
		width: 300px;
	}

	.open-history {
		align-items: center;
		border-bottom: 3px solid rgba(var(--z-line-rgb), 0.5);
		background-color: #fff;
		cursor: pointer;
		display: flex;
		margin-right: 20px;
		margin-bottom: 20px;
		line-height: 1.5;
		justify-content: center;
		span {
			color: var(--z-main);
			font-size: 16px;
			width: 20px;
		}
		width: 50px;
	}
}

:deep(.el-tabs__header) {
	margin-top: 10px;
	margin-bottom: 0;
}

.excel-box {
	border-radius: 0 5px 5px 5px;
	box-shadow: 0 -6px 6px -6px rgba(0, 0, 0, 0.12);
	border: 1px solid #e4e7ed;
	border-top: none;
	background-color: #fff;
	height: calc(100% - 192px);
	padding: 0 10px 10px 10px;

	.xlsx-plus {
		padding: 0;
	}

	&.full {
		height: 100% !important;
		left: 0;
		position: fixed;
		top: 0;
		width: 100%;
		z-index: 3;
	}
}

.excel_tips {
	align-items: center;
	display: flex;
	justify-content: flex-end;
	flex: 1;
	white-space: nowrap;

	i {
		color: var(--z-warning);
		height: 24px;

		svg {
			position: relative;
			top: -1px;
		}
	}

	span {
		color: var(--z-warning);
		height: 24px;
		line-height: 24px;
		padding: 0 0 0 5px;
	}
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
	border-radius: 5px;
	background: var(--z-theme);
}

.toggle-import-type {
	align-items: center;
	display: flex;
	position: relative;
	padding-right: 10px;

	span {
		background-color: #f1f1f1;
		height: 15px;
		position: absolute;
		right: 0;
		top: calc(50% - 7.5px);
		width: 2px;
	}
}

.linked-tips {
	align-items: center;
	display: flex;
	flex-wrap: nowrap;
	margin-left: 10px;
	white-space: nowrap;

	span {
		color: #666;
		padding: 0 10px;
	}

	.span1 {
		border-bottom: 5px solid #f78989;
		width: 30px;
	}

	.span3 {
		border-bottom: 5px solid #66b1ff;
		width: 30px;
	}
}
:deep(.fill-btn) {
	span {
		line-height: 15px;
	}
}

.info-over-view {
	display: flex;
}
.info {
	display: flex;
	margin: 10px 0px;
}
</style>
<style>
.xlsx-plus-component-full {
	position: fixed !important;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 0 10px 10px 10px !important;
	height: 100% !important;
	z-index: 9;
	background-color: #fff;
}
</style>
