<script setup lang="ts" name="detail">
import ScheduleProgressComp from '@/components/common/schedule-progress-comp.vue'
import {STAFFROLEARRAY} from '@/define/organization.define'
import {GetBatch, periodList, reportTastStatus, surplusDate} from '@/define/statement.define'
import util from '@/plugin/util'
import {useTaskManageStore} from '@/stores/taskManageStore'
import {useUserStore} from '@/stores/useUserStore'
import {useViewStore} from '@/stores/useViewStore'
import {ElMessage, ElNotification, FormInstance, TabsPaneContext, dayjs} from 'element-plus'
import {inject, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {FlowAuditCode} from '@/define/Workflow'
import {PushWorkflowProcessAudit} from '@/api/WorkflowApi'
import {GetWorkflowProcess} from '@/api/workflowTaskApi'
import {useFlowRecord} from '@/hooks/useFlowRecord'
import {GetPlanTaskProcess} from '@/api/ReportApi'
import {deleteLabel, updateLabelTitle} from '@/hooks/useLabels'

const route = useRoute()
const router = useRouter()
const axios = inject('#axios') as any
const isPushing = ref(false)
const userStore = useUserStore()
const infoOverViewHeight = ref(0)
const taskManageStore = useTaskManageStore()
const currentIndex = route.query.currentIndex
const titleList = ref([
	{
		width: '25%',
		title: '任务名称',
		key: 'taskName',
	},
	{
		width: '25%',
		title: '创建部门',
		key: 'creationOrganizationUnitName',
	},
	{
		width: '25%',
		title: '创建人',
		key: 'creatorStaff',
	},
	// {
	// 	width: '25%',
	// 	title: '填报周期',
	// 	key: 'period',
	// },
	{
		width: '25%',
		title: '填报期限',
		key: 'timeLimit',
	},
	{
		width: '25%',
		title: '填报范围',
		key: 'range',
	},
	{
		width: '25%',
		title: '审核提交时间',
		key: 'creationTime',
	},
	{
		width: '25%',
		title: '填报截止时间',
		key: 'endTime',
	},
	{
		width: '100%',
		title: '填报说明',
		key: 'description',
	},
	// {
	// 	width: '50%',
	// 	title: '来件部门',
	// 	key: 'parentAreaOrganizationUnitName',
	// },
	// {
	// 	width: '100%',
	// 	title: '审核人',
	// 	key: 'auditor',
	// },
	// {
	// 	width: '100%',
	// 	title: '查看流程',
	// 	key: '_viewFlow',
	// },
])
const data = ref()
const isPass = ref(false)
const auditModalIsVisible = ref(false)

const blockRef = ref()
const blockHistoryRef = ref()
const showOpinion = ref(false)
const opinionForm = ref({des: ''})
const currentFlowAuditCode = ref('')
const currentTaskInfo: any = ref(null)

const auditformArray = ref<any>([
	// {
	// 	type: 'textarea',
	// 	title: isPass ? '审核意见' : '驳回原因',
	// 	field: 'auditReason',
	// 	filterable: true,
	// 	data: [],
	// },
])
const auditformRules = ref<any>({
	// auditReason: [
	// 	{required: true, message: `请输入${isPass ? '审核意见' : '驳回原因'}`, trigger: 'blur'},
	// ],
})
// 获取计划任务的子报表任务列表
const colData = [
	// {
	// 	title: '所属批次',
	// 	field: 'planTaskId',
	// },
	{
		title: '数据量',
		field: 'totalDataRows',
	},
	{
		title: '状态',
		field: 'reportTaskStatus',
	},
	{
		title: '完成情况',
		field: 'departmentReportingStatus',
	},
	// {
	// 	title: '下发/分发时间',
	// 	field: 'creationTime',
	// },
	{
		title: '填报截止时间',
		field: 'endDate',
	},
]
const tableData = ref<any>()
const totalCount = ref(10)
const buttons = [
	{
		type: 'default',
		code: 'detail',
		title: '查看',
		icon: '<i i-majesticons-eye-line></i>',
		verify: 'true',
	},
]
const clickButton = (btn: {
	btn: {
		code: string
		title: string
		verify: string
	}
	scope: any
}) => {
	router.push({
		path: '/statementTodo/report-task-detail',
		query: {
			reportTaskId: btn.scope.id,
			areaOrganizationUnitId: btn.scope.creatingAreaOrganizationUnitId,
			type: 'detail',
			status: btn.scope.reportTaskStatus,
		},
	})
}
const auditformSubmit = (val: any, formRef: FormInstance | undefined) => {
	// 表单验证
	formRef
		?.validate()
		.then((validate: boolean) => {
			let params
			if (route.query.currentIndex === '1') {
				if (isPass.value) {
					params = {
						pass: isPass.value,
						auditReason: val.auditReason,
						dataLeaderId: val.dataLeaderId.split('/')[1],
						ActualDataLeaderAreaOrganizationUnitId: val.dataLeaderId.split('/')[0],
						isComplete: false,
					}
				} else {
					params = {
						pass: isPass.value,
						auditReason: val.auditReason,
						isComplete: false,
					}
				}

				axios
					?.post(
						`/api/filling/plan-task/in-charge-leader-audit?id=${route.query.id}`,
						params
					)
					.then((res: any) => {
						auditModalIsVisible.value = false
						// router.push('/statementTodo')
						router.go(-1)
					})
			} else {
				// 数据领导审核
				if (userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[4])) {
					params = {
						pass: isPass.value,
						auditReason: val.auditReason,
						isComplete: isComplete.value,
					}
					axios
						?.post(
							`/api/filling/plan-task/data-leader-audit?id=${route.query.id}`,
							params
						)
						.then((res: any) => {
							auditModalIsVisible.value = false
							router.go(-1)
						})
				}

				// 分管领导审核
				else {
					params = {
						pass: isPass.value,
						auditReason: val.auditReason,
						isComplete: false,
					}
					axios
						?.post(
							`/api/filling/plan-task/in-charge-leader-audit?id=${route.query.id}`,
							params
						)
						.then((res: any) => {
							auditModalIsVisible.value = false
							// router.push('/statementTodo')
							router.go(-1)
						})
				}
			}

			///api/filling/plan-task/audit 旧审核接口

			// console.log('验证通过:', validate, val)
		})
		.catch((error) => {
			console.log(error)

			console.log('验证失败')
		})
}
const getChildReportTasks = async (id: string) => {
	await axios?.get(`/api/filling/plan-task/${id}/child-report-tasks`).then((res: any) => {
		if (res) {
			tableData.value = res.data.items
		}
	})
}
const setLabelTitle = () => {
	const type = route.query.type
	let head = ''
	if (type === 'detail') {
		head = '查看'
	} else if (type === 'audit') {
		head = '审核'
	}
	updateLabelTitle({
		path: router.currentRoute.value.fullPath,
		title: `${head}-任务待办-${currentTaskInfo.value.name}`,
	})
}
const getTaskInfo = async () => {
	await axios
		?.get(`/api/filling/plan-task/${route.query.id}`)
		.then((res: any) => {
			flowCode.value = res.data.issuedAuditWorkflowSchemeCode
			currentTaskInfo.value = res.data
			data.value = {
				// ...res.data,
				creationOrganizationUnitName: res.data.createdDepartment ?? '-',
				creatorStaff: res.data.creatorName ?? '-',
				creationTime: res.data.creationTime,
				taskName: res.data.name,
				status: res.data.status,
				id: res.data.id,
				description: res.data.description,
				period: periodList[res.data.fillingPeriodType - 1].label,
				timeLimit: `${
					res.data.status === 5
						? '已完结'
						: surplusDate(
								res.data.fillingPeriodType,
								res.data.newToDays,
								res.data.endDate
						  ) === false
						? '已截止'
						: '剩余' +
						  surplusDate(
								res.data.fillingPeriodType,
								res.data.newToDays,
								res.data.endDate
						  ) +
						  '天'
				}`,
				range:res.data.fillingRange,
				endTime: dayjs(res.data.endDate).format('YYYY-MM-DD HH:mm:ss'),
				// 预留来件部门
				// 预留审核提交时间
				auditor: '-',
				auditReason: res.data.auditReason,
				totalDataRows: res.data.totalDataRows,
				reportTaskStatus: res.data.reportTaskStatus,
				departmentReportingStatus: res.data.departmentReportingStatus,
				fileInfos: res.data.fileInfos,
				// 预留审核提交人
			}
			// getChildReportTasks(res.data.id)
			if (res.data.status === 3 || res.data.status === 4) {
				// titleList.value.push({
				// 	width: '100%',
				// 	title: res.data.status === 3 ? '审核意见' : '驳回原因',
				// 	key: 'auditReason',
				// })
			}
			if (res.data.fileInfos && res.data.fileInfos.length !== 0) {
				titleList.value.push({
					width: '100%',
					title: '相关附件',
					key: 'fileInfos',
				})
			}
			setLabelTitle()
			getBusinessProcess()
		})
		.catch((err: any) => {
			console.log(err)

			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}
const __reportHeadList = ref<any[]>([])
const __excel_data = ref({})
const __currentHead = ref('')
const currentReportId = ref('')
const __excel_sheetId = ref('')
const sheets = ref([])
let reportList: any = []
const __getReportList = () => {
	axios
		?.get(`/api/filling/plan-task/${route.query.id}/table-templates`)
		.then(async (res: any) => {
			const {data} = res
			if (data && data[0]) {
				// sheet
				const {data} = res
				if (data && data[0]) {
					const [sheet] = data // 默认显示第一个

					// 选项卡
					__reportHeadList.value = data
					__currentHead.value = sheet.id
					// Xlsx - plus sheets
					sheets.value = [
						{
							name: sheet.name,
							head: JSON.parse(sheet.header || '[]'),
							data: JSON.parse(sheet.statisticCells || '[]'),
							config: JSON.parse(sheet.globalStyle || '{}'),
						},
					] as any
					currentReportId.value = sheet.id
					// 明细表获取列表数据
					await getReportListById(sheet.id)
				}
			}
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('当前进行任务填报人员较多，请5分钟后再试')
			}
		})
}

// const exportRef = ref()
const getReportListById = async (id: string, loading: boolean = true) => {
	const result = await axios?.batch(`/api/filling/report-table/${id}/data`)
	reportList = result
	// exportRef.value?.refreshList(result)
}

const __tabClick = (pane: TabsPaneContext, ev: Event) => {
	const headData = __reportHeadList.value.filter((f: any) => f.id == (pane.paneName as string))[0]
	// 表头, 权限配置
	// const { head, config } = JSON.parse(headData.globalStyle)
	__excel_sheetId.value = headData.id
	sheets.value = [
		{
			name: headData.displayName,
			head: JSON.parse(headData.header || '[]'),
			config: JSON.parse(headData.globalStyle || '{}'),
			data: [],
		},
	] as any
}
const dataLedgerList = ref<any[]>([])
const getSameOrganizationStaff = () => {
	axios
		.request({
			method: 'get',
			url: `/api/platform/departmentInternal/department-extend-bind-users`,
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((users: any) => {
			console.log(users)
			const {data} = users
			dataLedgerList.value = data
				.filter((v: any) => v.staffRole.includes(STAFFROLEARRAY[4]))
				.map((res: any) => ({
					label:
						res?.department?.region.name + '-' + res?.department?.name + '-' + res.name,
					value: res?.department?.id + '/' + res.id,
				}))
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('当前进行任务填报人员较多，请5分钟后再试')
			}
		})
}
const isComplete = ref(false)
// 领导审核计划任务
const audit = (code: FlowAuditCode) => {
	// isPass.value = val
	// if (complete) isComplete.value = complete
	// if (val && route.query.currentIndex === '1') {
	// 	auditformArray.value = [
	// 		{
	// 			full: true,
	// 			type: 'textarea',
	// 			title: '审核意见',
	// 			field: 'auditReason',
	// 			filterable: true,
	// 		},
	// 		{
	// 			full: true,
	// 			type: 'select',
	// 			title: '数据领导',
	// 			data: dataLedgerList.value,
	// 			field: 'dataLeaderId',
	// 			filterable: true,
	// 		},
	// 	]
	// 	auditformRules.value = {
	// 		auditReason: [
	// 			{
	// 				required: true,
	// 				message: `请输入${isPass.value ? '审核意见' : '驳回原因'}`,
	// 				trigger: 'blur',
	// 			},
	// 		],
	// 		dataLeaderId: [{required: true, message: `请选择数据领导`, trigger: 'change'}],
	// 	}
	// } else {
	// 	auditformArray.value = [
	// 		{
	// 			type: 'textarea',
	// 			title: `${isPass.value ? '审核意见' : '驳回原因'}`,
	// 			field: 'auditReason',
	// 			filterable: true,
	// 		},
	// 	]
	// 	auditformRules.value = {
	// 		auditReason: [
	// 			{
	// 				required: true,
	// 				message: `请输入${isPass.value ? '审核意见' : '驳回原因'}`,
	// 				trigger: 'blur',
	// 			},
	// 		],
	// 	}
	// }
	// auditModalIsVisible.value = true
	showOpinion.value = true
	currentFlowAuditCode.value = code
}

const onClickOpinion = () => {
	isPushing.value = true
	PushWorkflowProcessAudit(route.query.taskId as string, {
		code: currentFlowAuditCode.value,
		des: opinionForm.value.des,
	})
		.then(() => {
			ElMessage.success(
				`${
					currentFlowAuditCode.value === FlowAuditCode.Agree
						? '已同意审核'
						: currentFlowAuditCode.value === FlowAuditCode.Disagree
						? '已驳回审核'
						: ''
				} `
			)
			router.push({path: '/taskPending'})
			deleteLabel({path: router.currentRoute.value.fullPath})
		})
		.catch((err) => {
			window.errMsg(err, '审核')
		})
		.finally(() => {
			isPushing.value = false
		})
}

const xlsxPlusRef = ref()

const viewStore = useViewStore()
const onAutoSave = async (
	data: any,
	sheetConfig: any,
	isInsert: boolean = false,
	isDelete: boolean = false
) => {
	// debugger
	if (data.length === 0) return

	let result: any = []
	const len = data.length

	for (let i = 0; i < len; i++) {
		if (isInsert) {
			// 新增数据自动保存
			result.push({
				rowId: util._guid(),
				actionType: 1,
				rowNum: data[i][0].r + 1,
				rawData: JSON.stringify(data[i]),
			})
		} else if (isDelete) {
			result = data
		} else {
			// 修改数据自动保存
			const dl = data[i].length
			const rd = reportList.find((f: any) => f.rowNum === data[i][0].r + 1)

			result.push({
				rowId: rd ? rd.rowId || rd.id : util._guid(),
				actionType: rd ? 2 : 1,
				rowNum: rd ? rd.rowNum : dl > 0 ? data[i][0].r + 1 : i + 2,
				rawData: JSON.stringify(data[i]),
			})
		}
	}

	const promise = []

	// 更新当前报表模版的gloableStyle
	if (sheetConfig) {
		promise.push(
			axios.put(`/api/filling/report-table/${currentReportId.value}/global-style`, {
				globalStyle: JSON.stringify(sheetConfig),
			})
		)
	}

	promise.push(axios.put(`/api/filling/report-table/${currentReportId.value}/data`, result))

	await Promise.all(promise)

	getReportListById(currentReportId.value, false)
	xlsxPlusRef.value.$saveDone()
}
const onSaveTableHeader = (sheet: any) => {
	console.log(__currentHead.value)
	let head: any = []
	let data: any = []
	sheet.head = sheet.head.filter((v: any) => v.v.v !== '')
	if (!sheet.head.every((f: any) => f.v.bg)) return ElNotification.warning('请先指定表头')
	// 根据模版类型处理不同数据结构
	for (let i = 0; i < sheet.data.length; i++) {
		if (sheet.data[i].v.bg) {
			data.push(sheet.data[i])
		} else {
			head.push(sheet.data[i])
		}
	}
	axios
		?.put(
			`/api/filling/report-table-template/${__currentHead.value}`,
			Object.assign({
				name: sheet.name,
				displayName: sheet.name,
				globalStyle: JSON.stringify(sheet.config),
				header: JSON.stringify(sheet.head),
				tableTemplateColumn: head.map((m: any) => ({
					name: m.v.v + '',
					displayName: m.v.v + '',
					rowIndex: m.r, // 后端从1开始
					columnIndex: m.c,
				})),
			})
		)
		.then((res: any) => {
			//TODO: 重复保存会保存多个相同的模版
			if (res.status === 200) {
				ElNotification.success('保存成功!')
			}
		})
}
const onXlsxPlusSave = (sheets: Array<any>) => {
	const sheet = sheets[0]
	if (!xlsxPlusRef.value.$isLock()) {
		// ElNotification.warning(`保护已启用, 锁定保护${curReportType.value === 0 ? '区域' : '工作表'}!`)
		// cze新需求
		ElNotification.warning(`请指定表头!`)

		return
	}
	// 分管领导---编辑表头数据
	if (userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[1])) {
		onSaveTableHeader(sheet)
		xlsxPlusRef.value.$resize()
	}
	// 数据领导----保存编辑表格数据
	if (userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[4])) {
		onAutoSave(sheet.rawData, sheet.config)
	}
}
const showProgress = ref(true)
const onToggleInfoOverViewVisible = (hide: boolean, height: number) => {
	if (hide) {
		infoOverViewHeight.value = height
	} else {
		infoOverViewHeight.value = 0
	}
	xlsxPlusRef.value.$resize()
	setTimeout(() => {
		showProgress.value = !hide
	}, 100)
}
const autoHeight = ref(0)
const getAutoHeight = () => {
	const height = document.documentElement.clientHeight - 350
	autoHeight.value = height
}

let xlsxPlusParent: any = null
const isFull = ref(false)
const onFull = () => {
	isFull.value = !isFull.value

	const xlsxPlus = document.querySelector('.xlsx-plus-component') as HTMLElement
	if (isFull.value) {
		xlsxPlusParent = xlsxPlus.parentElement
		xlsxPlus.classList.add('xlsx-plus-component-full')
		document.body.appendChild(xlsxPlus)
	} else {
		xlsxPlus.classList.remove('xlsx-plus-component-full')
		xlsxPlusParent.appendChild(xlsxPlus)
		xlsxPlusParent = null
	}

	xlsxPlusRef.value.$resize()
}

const showFlow = ref(false)
const flowCode = ref('')
const onClickViewFlow = () => {
	showFlow.value = true
}

const logList = ref([])
const getLogList = async (processId: string) => {
	const res = await GetWorkflowProcess(processId)

	const logs = res.data.logs
	const tasks = res.data.tasks.filter(
		(v: any) =>
			(v.type === 1 || v.type === 5 || v.type === 9) && (v.state === 1 || v.state === 5)
	)

	logList.value = useFlowRecord().toList(
		[
			...logs,
			{
				unitName: tasks[0].unitName,
				des: '正在审核',
				userName: Array.from(new Set(tasks.map((v: any) => v.userName))).join(','),
				creationTime: tasks[0].creationTime,
			},
		],
		['unitName', 'des', 'userName', 'creationTime']
	) as any
	console.log('流程记录', res, logList.value)
}

const businessProcessData: any = ref({})
const getBusinessProcess = () => {
	GetPlanTaskProcess(currentTaskInfo.value.id)
		.then((res: any) => {
			businessProcessData.value = res.data
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('当前进行任务填报人员较多，请5分钟后再试')
			}
		})
}
const close = () =>{
	isPushing.value=false;
	opinionForm.value.des = ''
}
onMounted(async () => {
	getAutoHeight()
	await getSameOrganizationStaff()
	await getTaskInfo()
	__getReportList()

	if (data.value) await getChildReportTasks(data.value.id)
})
</script>
<template>
	<div class="report-task-detail">
		<div
			v-show="!showProgress"
			class="open-history"
			@click=";(showProgress = true), xlsxPlusRef.$resize()"
		>
			<span
				>展开流程记录
				<i
					class="icon i-ic-baseline-keyboard-double-arrow-right"
					style="position: relative; left: -1px"
				></i
			></span>
		</div>

		<div v-show="showProgress" class="right">
			<Block
				ref="blockHistoryRef"
				title="流程记录"
				:enable-back-button="false"
				:enable-expand-content="false"
				:enable-fixed-height="true"
				:enable-close-button="false"
			>
				<template #topRight>
					<span
						style="
							cursor: pointer;
							display: flex;
							align-items: center;
							color: var(--z-main);
						"
						@click=";(showProgress = false), xlsxPlusRef.$resize()"
					>
						<i class="icon i-ic-baseline-keyboard-double-arrow-left"></i>
						收起
					</span>
				</template>
				<BusinessProcess :data="businessProcessData"></BusinessProcess>
			</Block>
		</div>

		<div class="left" style="width: calc(100% - 300px)">
			<Block ref="blockRef" title="任务详情" :delay="256" :enable-expand-content="false">
				<template #topRight v-if="route.query.type === 'audit'">
					<el-button
						ml-10px
						size="small"
						type="warning"
						@click="audit(FlowAuditCode.Disagree)"
						:loading="isPushing"
						>驳回</el-button
					>
					<el-button
						size="small"
						type="primary"
						@click="audit(FlowAuditCode.Agree)"
						:loading="isPushing"
						>通过</el-button
					>
					<!-- <el-button type="danger" v-if="userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[4])" @click="audit(true, true)">完结</el-button> -->
				</template>

				<div class="df flx mg-bottom-15">
					<InfoOverViewComp
						:data="data"
						:titles="titleList"
						@onToggleVisible="onToggleInfoOverViewVisible"
					>
						<template #_viewFlow>
							<div style="display: flex; align-items: center; padding: 0 10px">
								<el-button type="primary" size="small" @click="onClickViewFlow"
									>查看流程</el-button
								>
							</div>
						</template>
					</InfoOverViewComp>
				</div>

				<el-tabs
					v-model="__currentHead"
					type="card"
					@tab-click="__tabClick"
					v-if="
						data?.status === 2 ||
						data?.status === 1 ||
						data?.status === 6 ||
						data?.status === 7 ||
						data?.status === 3 ||
						data?.status === 13
					"
				>
					<el-tab-pane
						v-for="item of __reportHeadList"
						:label="item?.displayName"
						:name="item?.id"
					></el-tab-pane>
				</el-tabs>

				<div
					class="excel-box"
					v-if="
						data?.status === 2 ||
						data?.status === 1 ||
						data?.status === 4 ||
						data?.status === 6 ||
						data?.status === 7 ||
						data?.status === 3 ||
						data?.status === 13
					"
				>
					<XlsxPlusComp
						class="xlsx-plus-component"
						:style="{height: `calc(${autoHeight}px + ${infoOverViewHeight}px)`}"
						ref="xlsxPlusRef"
						:enableProtect="
							currentIndex === '1' &&
							userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[1])
						"
						:enableAutoSave="false"
						:enableImport="
							currentIndex === '5' &&
							userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[4])
						"
						:sheets="sheets"
						:enableReload="true"
						@onSave="onXlsxPlusSave"
						:viewonly="
							route.query.type === 'detail' ||
							!userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[1])||route.query.type === 'audit'
						"
					>
						<template
							#headerRight
							v-if="
								route.query.type === 'detail' ||
								!userStore.userInfo?.staffRole.includes(STAFFROLEARRAY[1])
							"
						>
							<div class="excel_tips"></div>
							<el-button
								type="primary"
								size="small"
								ml-10px
								style="display: flex; align-items: center"
								class="mg-left-10"
								@click="onFull"
							>
								<template v-if="isFull">
									<i
										i-ic-sharp-fullscreen-exit
										mr-5px
										style="font-size: 18px"
									></i>
									<span>收起</span>
								</template>
								<template v-else>
									<i i-ic-baseline-fullscreen mr-5px style="font-size: 18px"></i>
									<span>全屏</span>
								</template>
							</el-button>
						</template>
					</XlsxPlusComp>
				</div>

				<BaseTableComp
					v-if="
						data?.status !== 2 &&
						data?.status !== 1 &&
						data?.status !== 3 &&
						data?.status !== 4 &&
						data?.status !== 6 &&
						data?.status !== 7 &&
						data?.status !== 13
					"
					:colData="colData"
					:data="tableData"
					:offsetHeight="100"
					:checkbox="true"
					:buttons="buttons"
					:total="totalCount"
					:visible-setting="false"
					:visible-search="false"
					:visible-header="true"
					:visible-page="true"
					@click-button="clickButton"
				>
					<template #planTaskId="scope">
						{{ GetBatch(scope.rowData.fillingPeriodType, scope.rowData.creationTime) }}
					</template>
					<template #reportTaskStatus="scope">
						<span
							:style="{
								color: reportTastStatus[scope.rowData.reportTaskStatus - 1].color,
							}"
						>
							{{ reportTastStatus[scope.rowData.reportTaskStatus - 1].name }}
						</span>
					</template>
				</BaseTableComp>
			</Block>
		</div>

		<Dialog
			v-model="auditModalIsVisible"
			:key="auditModalIsVisible"
			:showResetButton="false"
			title="提交审核"
			width="600"
			height="200"
			:enableButton="false"
			@close="auditModalIsVisible = false"
		>
			<FormComp
				:form="auditformArray"
				:rules="auditformRules"
				:showResetButton="false"
				submitText="提交"
				@onSubmit="auditformSubmit"
			>
			</FormComp>
		</Dialog>

		<Dialog
			:title="`${
				currentFlowAuditCode === FlowAuditCode.Agree
					? '同意审核'
					: currentFlowAuditCode === FlowAuditCode.Disagree
					? '驳回审核'
					: ''
			}`"
			@close="close"
			v-model="showOpinion"
			:loading="isPushing"
			@click-confirm="onClickOpinion"
			width="600"
		>
			<Form
				v-model="opinionForm"
				:props="[{prop: 'des', label: '审核意见', type: 'textarea'}]"
				:enable-button="false"
			></Form>
		</Dialog>

		<ViewFlow v-model="showFlow" :code="flowCode"></ViewFlow>
	</div>
</template>
<style lang="scss" scoped>
.report-task-detail {
	display: flex;

	.left {
		flex: 1;
	}

	.right {
		margin-right: 20px;
		width: 300px;
	}

	.open-history {
		align-items: center;
		border-bottom: 3px solid rgba(var(--z-line-rgb), 0.5);
		background-color: #fff;
		cursor: pointer;
		display: flex;
		margin-right: 20px;
		margin-bottom: 20px;
		line-height: 1.5;
		justify-content: center;
		span {
			color: var(--z-main);
			font-size: 16px;
			width: 20px;
		}
		width: 50px;
	}
}

:deep(.el-tabs__header) {
	margin-top: 10px;
	margin-bottom: 0;
}

.excel-box {
	border-radius: 0 5px 5px 5px;
	box-shadow: 0 -6px 6px -6px rgba(0, 0, 0, 0.12);
	border: 1px solid #e4e7ed;
	border-top: none;
	height: calc(100% - 224px);
	padding: 0 10px 10px 10px;
}

.excel_tips {
	align-items: center;
	display: flex;

	i {
		color: var(--z-warning);
	}

	span {
		color: var(--z-warning);
		padding: 0 10px;
	}
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
	border-radius: 5px;
	background: var(--z-theme);
}
.xlsx-plus-component-full {
	position: fixed !important;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 0 10px 10px 10px !important;
	height: 100% !important;
	z-index: 9;
	background-color: #fff;
}
</style>
