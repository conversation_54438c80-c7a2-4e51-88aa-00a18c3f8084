<script setup lang="ts" name="export-audit">
import {onActivated, ref, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {FlowAuditCode} from '@/define/Workflow'
import {
	GetExportRecordDetail,
	PushWorkflowProcessAudit,
	GetExportRecordProcess,
} from '@/api/WorkflowApi'
import {ElMessage} from 'element-plus'
import {useUserStore} from '@/stores/useUserStore'
import {updateLabelTitle} from '@/hooks/useLabels'

const loading = ref(false)
const router = useRouter()
const user = useUserStore()
const {id, rid, tid} = useRoute().query as {id: string; rid: string; tid: string}
const collapsed = ref(false)
const form: any = ref({})
const formProps = [
	{prop: 'name', label: '业务表名称'},
	{prop: 'creatorDepartment', label: '创建部门'},

	{prop: 'exportDepartment', label: '数据导出部门'},
	{prop: 'exportUser', label: '导出人'},

	{prop: 'creationTime', label: '提交审核时间'},
	{prop: 'dataCount', label: '导出数据量', full: true},
	{prop: '_remark', label: '导出数据说明', full: true},
]

const detail: any = ref(null)
const tableData: any = ref([])
const process: any = ref([])
const processEnd = ref(true)
const hasOwn = ref(false)
const status = ref(0)
const showAuditDialog = ref(false)
const auditFormRef = ref()

const getDetail = () => {
	GetExportRecordDetail(id).then((res) => {
		const {dataCount, creationTime, ledger, exportUserDepartmentName, exportUser} = res.data

		detail.value = res.data
		tableData.value = Object.values(res.data.exportFields).map((val: any) => ({
			field: val,
		}))

		Object.assign(form.value, {
			name: ledger.name,
			creatorDepartment: ledger.creatorDepartment.name,
			exportDepartment: exportUserDepartmentName,
			exportUser: exportUser.name,
			dataCount,
			creationTime,
		})
		console.log('导出详情:', detail.value)
		updateLabelTitle({
			path: router.currentRoute.value.fullPath,
			title: `查看-任务管理-${ledger.name}`,
		})
	})
}

const getProcess = () => {
	if (rid) {
		GetExportRecordProcess(rid).then((res) => {
			process.value = res.data
		})
	}
}

const onClickAudit = (val: number) => {
	status.value = val
	showAuditDialog.value = true
}

const onPushAudit = () => {
	auditFormRef.value.validate((valid: boolean) => {
		if (!valid) return

		loading.value = true
		PushWorkflowProcessAudit(tid, {
			code: status.value ? FlowAuditCode.Agree : FlowAuditCode.Disagree,
			des: form.value.auditReason,
		})
			.then(() => {
				ElMessage.success(`${status.value ? '审核成功' : '驳回成功'}`)
			})
			.catch((e) => {
				window.errMsg(`${status.value}?'审核':'驳回'`, e)
			})
			.finally(() => {
				form.value = {}
				loading.value = false
				showAuditDialog.value = false
				router.go(-1)
			})
	})
}

const onAgreeUser = (ids: any, currentIds: string[]) => {
	nextTick(() => {
		// 流程是否结束
		if (ids.some((id: string) => id === user.getUserInfo.id)) {
			processEnd.value = true
		}

		// 当前节点是否有自己并且没有通过
		if (currentIds.includes(user.getUserInfo.id) && !ids.includes(user.getUserInfo.id)) {
			hasOwn.value = true
		}
	})
}

onActivated(() => {
	getDetail()
	getProcess()
})
</script>
<template>
	<div class="df export-audit">
		<div class="left mg-right-20" :style="{width: collapsed ? '60px' : '300px'}">
			<Block
				title="流程记录"
				:enableExpand="true"
				:enable-fixed-height="true"
				:enable-expand-content="false"
				:enable-back-button="false"
				:enable-expand-button="true"
				:enable-close-button="false"
				:expand-vertical="false"
				collapsedText="展开查看审核流程"
				@collapsed="($event:any) => (collapsed = $event)"
			>
				<BusinessProcess
					v-if="rid"
					v-model:end="processEnd"
					:data="process"
					:is-ledger-recode="true"
					@agree="onAgreeUser"
				></BusinessProcess>
				<template v-else>
					<el-empty description="暂无流程记录"></el-empty>
				</template>
			</Block>
		</div>
		<div class="right flx" :style="{width: collapsed ? '100%' : 'calc(100% - 300px)'}">
			<Block title="任务信息" :enable-fixed-height="true" :enable-expand-content="false">
				<template #topRight v-if="!processEnd">
					<el-button
						v-if="tid && hasOwn"
						type="danger"
						size="small"
						@click="onClickAudit(0)"
						:loading="loading"
					>
						驳回
					</el-button>
					<el-button
						v-if="tid && hasOwn"
						type="primary"
						size="small"
						@click="onClickAudit(1)"
						:loading="loading"
					>
						通过
					</el-button>
				</template>
				<Form
					v-model="form"
					:props="formProps"
					:grid="true"
					:column-count="3"
					label-width="120"
				>
					<template #form-_remark>
						{{ detail?.remark || '-' }}
					</template>
				</Form>
				<TableV2
					:auto-height="true"
					:default-table-data="tableData"
					:enable-toolbar="false"
					:enable-edit="false"
					:enable-delete="false"
					:columns="[{prop: 'field', label: '列表字段', attrs: {align: 'left'}}]"
					class="mg-top-5"
				>
					<template #field="scoped">
						<div class="w-full" style="text-align: left">
							{{ scoped.row.field }}
						</div>
					</template>
				</TableV2>
			</Block>
		</div>

		<Dialog
			v-model="showAuditDialog"
			:title="status ? '通过' : '驳回'"
			:enable-popconfirm="true"
			confirmText="提交"
			@click-confirm="onPushAudit"
		>
			<Form
				ref="auditFormRef"
				v-model="form"
				:props="[
					{prop: 'auditReason', label: '审核意见', type: 'textarea', labelWidth: 80},
				]"
				:rules="{
					auditReason: [{required: true, message: '请输入审核意见', trigger: 'blur'}],
				}"
				:enable-button="false"
				:loading="loading"
			></Form>
		</Dialog>
	</div>
</template>
<style scoped lang="scss">
.export-audit {
	.left {
		transition: width 0.15s;
	}
}
</style>
