<script setup lang="ts">
import {onMounted, reactive} from 'vue'
import {GetTodoRemind, SetTodoRemind} from '@/api/TodoRemind'
import {TodoRemind} from '@/define/TodoRemind'
import {ElMessage} from 'element-plus'
import {ElNotification} from 'element-plus'

const emits = defineEmits(['completed'])

const form = reactive({
	worktodo: false,
})

const getRemind = () => {
	GetTodoRemind(TodoRemind.WorkTodo)
		.then((res) => {
			form.worktodo = res.data
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}

const onClickConfirm = () => {
	SetTodoRemind(TodoRemind.WorkTodo, form.worktodo)
		.then(() => {
			ElMessage.success('设置成功')
		})
		.catch((err) => {
			window.errMsg('设置', err)
		})
		.finally(() => {
			emits('completed')
		})
}

onMounted(() => {})
</script>
<template>
	<Dialog
		v-bind="$attrs"
		title="待办设置"
		width="600"
		@open="getRemind"
		@click-confirm="onClickConfirm"
	>
		<div class="todo-tip">个性化设置你希望接受到的工作待办</div>
		<ul class="todo-list">
			<li>
				<FormItem
					v-model="form.worktodo"
					:items="[
						{
							prop: 'worktodo',
							label: '任务填报-业务表',
							type: 'switch',
							spaceBetween: true,
							labelWidth: 120,
						},
					]"
					style="margin-bottom: 0px"
				></FormItem>
			</li>
		</ul>
		<div class="todo-tip pd-top-20 df aic red">
			<Icons name="Warning" size="12" color="red" class="mg-right-5" />
			<small>本次设置不更改已收到的工作待办</small>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.todo-tip {
	padding-bottom: 20px;
}
.todo-list {
	border-radius: 5px;
	border: 1px solid var(--z-line);
	padding: 10px 20px;
}
</style>
