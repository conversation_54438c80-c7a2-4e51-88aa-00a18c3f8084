<script setup lang="ts" name="taskpending">
import {reactive, ref, onActivated, computed, watch, watchEffect, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {STAFFROLEARRAY} from '@/define/organization.define'
import {useArrayToTree} from '@/hooks/useConvertHook'
import {request as defHttp} from '@/api/index'
import departmentFavoriteComp from '@/components/common/department-favorite-comp.vue'
import {ReportsFlowStatus, ReportsFlowStatusType} from '@/define/statement.define'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
	SetInternalStaff,
	IssueDataLeader,
	PushReportFlowTask,
	ConfirmCompleted,
	getAuditingApi, // 是否流程审核中
	auditSomeApi, // 批量审核
	auditAllApi, // 全部审核
	newIssueDataLeader,
	// getAuditingApi, // 批量审核
} from '@/api/ReportApi'
import {FlowType as WorkFlowType, FlowAuditTypes, FlowAuditTypeData} from '@/define/Workflow'
import {useUserStore} from '@/stores/useUserStore'
import {ElNotification} from 'element-plus'

import TodoRemindComponent from './component/TodoRemindComponent.vue'
import {useSignalr, useSignalrStop} from '@/hooks/useSignalr'

const route = useRoute()
const router = useRouter()
enum FlowType {
	Initiate = '任务审核',
	Fill = '任务填报',
}

enum Tips {
	Personnel = '人员',
	Department = '部门',
}

enum TableRequest {
	Initiate = '/api/workflow/workflowTask/my-unCompleted',
	// Fill = '/api/filling/report-task-ou',
	Fill = '/api/platform/workToDoRecord/pages',
}

const loading = ref(false)
const isPushing = ref(false)
const showTodoRemind = ref(false)
const activeName = ref(FlowType.Initiate)
const searchForm: any = ref({})
const userStore = useUserStore()
const auditCount = computed(() => userStore.getAuditCount)
const fillCount = computed(() => userStore.getFillCount)

const formItems = {
	[FlowType.Initiate]: [
		{prop: 'title', type: 'text', placeholder: '请输入任务名称'},
		{
			prop: 'date',
			type: 'datetimerange',
			placeholder: '请选择创建时间',
		},
		// {
		// 	prop: 'IsUrge',
		// 	type: 'select',
		// 	placeholder: '请选择提状态',
		// 	options: [
		// 		{label: '正常', value: '0'},
		// 		{label: '催办', value: '1'},
		// 	],
	],
	[FlowType.Fill]: [
		{prop: 'title', type: 'text', placeholder: '请输入任务名称'},
		{
			prop: 'category',
			type: 'select',
			placeholder: '请选择任务类型',
			options: [
				{label: '临时报表填报', value: 1},
				{label: '业务表填报', value: 2},
			],
		},
		{
			prop: 'status',
			type: 'select',
			placeholder: '请选择任务状态',
			options: ReportsFlowStatus.filter((f: any) => f.type === 'fillReport'),
		},
		{
			prop: 'date',
			type: 'datetimerange',
			placeholder: '请选择创建时间',
		},
	],
}

const tableRef = ref()
const tableHeight = ref(0)
const currentRow: any = ref(null)
const tableRequestUrl = ref(TableRequest.Initiate)

const statusArr = [
	{name: '激活', color: '#3D7FFF', state: 1},
	{name: '待激活', color: '#5DC1AA', state: 2},
	{name: '完成', color: '#FD6B69', state: 3},
	{name: '关闭', color: '#EA8B60', state: 4},
	{name: '加签状态', color: '#3D7FFF', state: 1},
	{name: '转移给其他人', color: '#5DC1AA', state: 2},
	{name: '作废', color: '#FD6B69', state: 3},
	{name: '子流程运行中', color: '#EA8B60', state: 4},
]

const tableColumns = {
	[FlowType.Initiate]: [
		{label: '任务名称', prop: 'title', type: 'text'},
		{label: '任务类型', prop: '_category', type: 'text'},
		{label: '提交部门', prop: 'createUserLargeDepartmentName', type: 'text'},
		{label: '提交科室', prop: 'createUserDepartmentName', type: 'text'},
		{label: '提交人', prop: 'createUserName', type: 'text'},
		{label: '提交时间', prop: 'businessRelevanceTime', type: 'datetime', sortable: 'custom'},
		{label: '截止时间', prop: 'deadline', type: 'datetime'},
		// {label: '状态', prop: 'state', type: 'switch'},
	],
	[FlowType.Fill]: [
		{label: '任务名称', prop: 'name'},
		{label: '任务类型', prop: 'category'},
		{label: '创建人', prop: 'createrName'},
		{label: '创建人部门', prop: 'createrLargeDepartmentName'},
		{label: '创建人科室', prop: 'createrDepartmentName'},
		{label: '创建时间', prop: 'creationTime', sortable: 'custom'},
		{label: '截止时间', prop: 'endTime', width: 260},
		{label: '状态', prop: 'status', width: 90},
	],
}
const defaultSort: any = ref({
	[FlowType.Initiate]: {prop: 'businessRelevanceTime', order: 'descending'},
	[FlowType.Fill]: {prop: 'creationTime', order: 'descending'},
})
const tableButtons = {
	[FlowType.Initiate]: [{label: '审核', code: 'toexamine', type: 'primary'}],
	[FlowType.Fill]: [
		{
			label: '录入',
			code: 'fill',
			type: 'primary',
			disabled: `row.category === 1 ? false : row.category === 2 && row.status !== 1`,
		},
		{
			label: '转发',
			code: 'turn',
			type: 'primary',
			disabled: `!row?._raw?.businessExtend?.hasTranspond`,
			show: 'row.category === 1',
		},
		{
			label: '提交',
			code: 'push',
			type: 'primary',
			disabled: `false`,
			show: 'row.category === 1',
		},
		{
			label: '完成',
			code: 'complete',
			type: 'primary',
			disabled: `row.status !== 1`,
			show: 'row.category === 2',
		},
		// {
		// 	label: '删除',
		// 	code: 'delete',
		// 	type: 'danger',
		// 	popconfirm: '是否删除该任务?',
		// 	disabled: '!row?._raw?.stop',
		// },
	],
}

const turnFormProps = ref([
	// {
	// 	prop: 'model',
	// 	label: '填报模式',
	// 	type: 'select',
	// 	options: [
	// 		{label: '内部填报', value: 1},
	// 		{label: '下发填报', value: 2},
	// 	],
	// },
	{prop: 'fillUsers', label: '填报范围'},
])
const turnFormPropsOther = [
	// {
	// 	prop: 'turnFlow',
	// 	label: '转发流程',
	// 	type: 'selectRemote',
	// 	remoteUrl: '/api/workflow/workflowSchemeInfo',
	// 	remoteParams: {enabledMark: true, category: WorkFlowType.Fill},
	// 	remoteFilterKey: 'keyword',
	// 	remoteValue: 'code',
	// 	remoteInit: true,
	// },
	{
		prop: 'auditFlow',
		label: '本部门数据审核流程',
		type: 'selectRemote',
		remoteUrl: '/api/workflow/workflowSchemeInfo',
		remoteParams: {enabledMark: true, category: WorkFlowType.Fill},
		remoteFilterKey: 'keyword',
		remoteValue: 'code',
		remoteInit: true,
		labelWidth: 165,
	},
]

const turnFormRef = ref()
const turnForm: any = ref({model: 1, fillUsers: []})
const treeSelectData = ref([])
const treeSelectValue = ref([])

const pushDesForm = ref({})

const showPushDes = ref(false)
const showTurnFlow = ref(false)

const getTimeLeft = (scoped: any) => {
	if (!scoped.row.endTime || !scoped.row.currentServiceTime) return ''
	const start: any = new Date(scoped.row.currentServiceTime)
	const end: any = new Date(scoped.row.endTime)
	const diffTime = Math.abs(end - start)
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

	let type = ''
	if (diffDays <= 1) {
		type = 'danger'
	} else if (diffDays <= 3) {
		type = 'warning'
	} else {
		type = 'primary'
	}

	return {
		type,
		diffDays,
	}
}

const beforeComplete = ({items, next}: any) => {
	const temp: any = []

	console.log('items222', activeName, items)
	if (activeName.value === FlowType.Initiate) {
		tableData.value = items
		// '1' 全部 '2'本页
		if (chooseType.value === '1') {
			handleChoose()
		}
	}

	items.forEach((x: any) => {
		if (activeName.value === FlowType.Initiate) {
			x.title = x.process.title
			x.createUserName = x.process.createUserName
			x.createUserLargeDepartmentName = x.process.createUserLargeDepartmentName
			x.createUserDepartmentName = x.process.createUserDepartmentName
			x.ledgerName = x.process.businessExtend ? x.process.businessExtend.LedgerName : ''
			x.businessRelevanceTime = x.process.businessRelevanceTime
			x.creationTime = x.process.creationTime
			x.deadline = x.process.deadline ?? '-'
			x.state = x.state
			temp.push(x)
		} else {
			const row = {
				name: x?.name,
				category: x?.category,
				createrLargeDepartmentName: x?.createrLargeDepartmentName,
				createrDepartmentName: x?.createrDepartmentName,
				createrName: x?.createrName,
				creationTime: x?.creationTime,
				endTime: x?.endTime,
				currentServiceTime: x?.currentServiceTime,
				status: x?.status,
				_raw: JSON.parse(JSON.stringify(x)),
			}
			temp.push(row)
		}
	})
	next(temp)
}
const reqParams: any = reactive({
	skipCount: 0,
	maxResultCount: 10,
	Sorting: 'BusinessRelevanceTime desc',
})

const pagination = reactive({
	total: 0,
	page: 1,
	size: 10,
})

const currentFlowCode = ref('')
const showFlow = ref(false)

const IsShowStateStop = ref(true)
const getInfo = async () => {
	// const res = await getTodoNotice(
	// 	{...pageParams.value, ...searchParams.value},
	// 	1,
	// 	IsShowStateStop.value
	// )
	// tableData.value = res.data.items
	// total.value = res.data.totalCount
	// userStore.$patch({workCount: total.value})
}

// 办理记录
const routeTo = () => {
	console.log(activeName.value)
	if (activeName.value == '任务填报') {
		router.push('/taskPending/record?isData=1')
	} else {
		router.push({
			path: '/taskPending/record',
		})
	}
}
const onClickTabs = (val: FlowType) => {
	console.log('onClickTabs', val)
	activeName.value = val
	tableRequestUrl.value = val === FlowType.Initiate ? TableRequest.Initiate : TableRequest.Fill
	searchForm.value = {}
	onSearch(true)
}

const onBlockHeightChanged = (val: number) => {
	tableHeight.value = val - 75
}

const onTableCompleted = () => {
	pagination.total = tableRef.value.getTotal()
}

const onTableButtonClick = ({row, btn, index}: any) => {
	console.log(row, btn, index)
	currentRow.value = row
	if (btn.code === 'toexamine') {
		if (row.process.businessType === FlowAuditTypes.ReportTaskIssude) {
			router.push({
				path: '/taskPending/detail',
				query: {
					id: row.process.businessId,
					taskId: row.id,
					type: 'audit',
					currentIndex: 1,
				},
			})
		} else if (
			row.process.businessType === FlowAuditTypes.ReportTaskDataAudit ||
			row.process.businessType === FlowAuditTypes.ReportTaskTranspondAudit
		) {
			router.push({
				path: '/taskPending/report-task-detail',
				query: {
					taskId: row.id,
					reportTaskId:
						row.process.businessExtend.Id || row.process.businessExtend.ReportTaskId,
					areaOrganizationUnitId:
						row.process.businessExtend.Id ||
						row.process.businessExtend.AreaOrganizationUnitId,
					id: row.process.businessExtend.Id,
					type: 'audit',
					currentIndex: 4,
					businessType: row.process.businessType,
				},
			})
		} else if (row.process.businessType === FlowAuditTypes.LedgerDataExport) {
			router.push({
				path: '/taskPending/export-audit',
				query: {id: row.process.businessId, rid: row.processId, tid: row.id},
			})
		} else {
			router.push({
				path: '/taskPending/task-review-details',
				query: {
					id: row.id,
					taskId: row.process.id,
					keyword: row.process.keyword2,
					businessId: row.process.businessId,
					batchType: row.process.businessExtend.Type,
					Remark: row.process.businessExtend.Remark,
				},
			})
		}
	} else if (btn.code === 'fill') {
		if (row.category === 1) {
			console.log({
				reportTaskId: row._raw.businessExtend?.reportTaskId,
				areaOrganizationUnitId: row._raw.businessExtend?.areaOrganizationUnitId,
				id: row._raw.businessExtend?.id ?? undefined,
				type: 'edit',
				from: 'taskPending',
			})
			// 填报详情
			router.push({
				path: '/statementTodo/report-task-detail',
				query: {
					reportTaskId: row._raw.businessExtend?.reportTaskId,
					areaOrganizationUnitId: row._raw.businessExtend?.areaOrganizationUnitId,
					id: row._raw.businessExtend?.id ?? undefined,
					type: 'edit',
					from: 'taskPending',
				},
			})
		} else if (row.category === 2) {
			// 业务表详情
			router.push({
				path: '/ledger/fill',
				query: {
					ledgerId: row._raw.businessExtend?.LedgerId,
				},
			})
		}
	} else if (btn.code === 'turn') {
		showTurnFlow.value = true
		treeSelectValue.value = []
	} else if (btn.code === 'push') {
		showPushDes.value = true
	} else if (btn.code === 'delete') {
		// DeleteReportFlowTask(row._raw.reportTaskId, row._raw.areaOrganizationUnitId)
		// 	.then(() => {
		// 		ElMessage.success('删除成功')
		// 		onSearch()
		// 	})
		// 	.catch((err) => window.errMsg(err, '删除'))
	} else if (btn.code === 'complete') {
		ElMessageBox.confirm('是否确认完成该任务?', '提示', {
			type: 'warning',
		})
			.then(() => {
				ConfirmCompleted(row._raw.id)
					.then(() => {
						ElMessage.success('操作成功')
						onSearch()
					})
					.catch((err) => window.errMsg(err, '操作'))
			})
			.catch(() => {})
	}
}

// 排序改变---方法
const onSortChange = (e: any) => {
	delete reqParams.Sorting
	reqParams.Sorting = `${e.prop} ${e.order === 'ascending' ? 'asc' : 'desc'}`
}
// const onCreate = () => {
// router.push({path: '/taskPending/report-task-detail', query: {}})
// }

const clearReqParams = (needResetPage: boolean = false) => {
	if (needResetPage) {
		pagination.page = 1
		pagination.size = 10
		reqParams.skipCount = 0
		reqParams.maxResultCount = 10
	}

	Object.keys(reqParams).forEach((key) => {
		if (key !== 'skipCount' && key !== 'maxResultCount') delete reqParams[key]
	})

	// 处理固定参数
	if (activeName.value === FlowType.Initiate) {
		reqParams.Sorting = 'BusinessRelevanceTime desc'
	} else if (activeName.value === FlowType.Fill) {
		// reqParams.sorting = 'asc'
		// reqParams.filterMode = 2
	}
}

const onSearch = (bool: boolean = false) => {
	clearReqParams(bool)

	handleCancel()

	// delete reqParams.IsUrge
	// if (searchForm.value.IsUrge == '0') {
	// 	reqParams.IsUrge = false
	// } else if (searchForm.value.IsUrge == '1') {
	// 	reqParams.IsUrge = true
	// }
	console.log(searchForm.value)

	// let date = searchForm.value.date?.split(',')
	let date = searchForm.value.date

	endDate.value = getCurrentDateTime()

	if (activeName.value == FlowType.Initiate) {
		reqParams.title = searchForm.value.title
		if (date) {
			console.log('data233', date)

			reqParams.startDate = date[0]
			reqParams.endDate = date[1]
		} else {
			reqParams.endDate = endDate.value
		}
	} else {
		reqParams.name = searchForm.value.title
		reqParams.category = searchForm.value.category
		reqParams.status = searchForm.value.status ? [searchForm.value.status] : [1, 2]
		if (searchForm.value.date) {
			reqParams.startTime = date[0]
			reqParams.endTime = date[1]
		}
	}
}

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
		// debugger
		if (activeName.value === FlowType.Initiate) {
			if (chooseType.value === '2') {
				// 本页
				handleCancel()
			} else if (chooseType.value === '1') {
				// 选择全部，先清空id 在设置本页全选
				allSelectPageData.value = []
				handleCancel()
				chooseType.value = '1'
				nextTick(() => {
					handleChoose()
				})
			}
		}
		// if (chooseType.value === '2' && (activeName.value === FlowType.Initiate)) {
		// 	handleCancel()
		// }
	} else {
		pagination.page = 1
		pagination.size = val
		reqParams.skipCount = 0
		reqParams.maxResultCount = pagination.size
		tableRef.value.clearSelection()

		if (chooseType.value === '2' && activeName.value === FlowType.Initiate) {
			handleCancel()
		}
	}
}

const loadNode = (node: any, resolve: any) => {
	console.log(node.data)

	if (node.data.length === 0 || !node.data.id) return
	defHttp
		.request({
			method: 'get',
			url: `/api/platform/departmentInternal/${node.data.id}/bind-users`,
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((users: any) => {
			const userList =
				users.data.items
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
					.map((res: any) => ({
						label: res.name,
						value: res?.department?.id + '/' + res.id,
						departmentId: res?.department?.id,
						isLeaf: true,
						disabled: res?.department?.id ? false : true,
					})) ?? []
			resolve(node.data.children.concat(userList))
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}

const filterMethod = (query: string) => {
	if (query) {
		loading.value = true
		defHttp
			.request({
				method: 'get',
				url: `/api/platform/department/department-bind-users?filter=${query}`,
				headers: {
					Urlkey: 'iframeCode',
				},
			})
			.then((res: any) => {
				const {data} = res
				const arr = data.items
					.map((v: any) => ({
						...v,
						label: v.name,
						name: v.department?.parent?.name + '-' + v.department?.name + '-' + v.name,
						value: v?.department?.id + '/' + v.id,
						departmentId: v?.department?.id,
						isLeaf: true,
						disabled: false,
					}))
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
				treeSelectData.value = useArrayToTree(
					arr,
					'id',
					'parentId',
					'name',
					true,
					'children'
				) as any
				loading.value = false
			})
	} else {
		getDepartmentChildren()
	}
}

const getDepartmentChildren = async () => {
	defHttp
		.request({
			method: 'get',
			url: '/api/platform/departmentInternal/get-department-children',
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((res) => {
			const {data} = res
			const arr = data.map((v: any) => ({
				...v,
				disabled: v?.department?.departmentId ? false : true,
				children: v.children === null ? [] : v.children,
			}))
			treeSelectData.value = useArrayToTree(
				arr,
				'id',
				'parentId',
				'name',
				true,
				'children'
			) as any
		})
		.catch((err: any) => {
			window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
		})
}

const onTurnFormChange = (val: any, formItem: any) => {
	if (formItem.prop === 'model') {
		treeSelectValue.value = []
		turnFormRef.value.setValue('fillUsers', [])
		turnFormProps.value[1].label = `填报${val === 1 ? Tips.Personnel : Tips.Department}`
	}

	if (formItem.prop === 'turnFlow' || formItem.prop === 'auditFlow') {
		const code = formItem.options.find((v: any) => v.value === val)?.raw.code
		if (code) {
			currentFlowCode.value = code
		}
	}
}

const onTreeSelectChange = (val: any) => {
	turnFormRef.value.setValue('fillUsers', val)
}

const departmentListChange = (e: any, userList: any) => {
	turnFormRef.value.setValue(
		'fillUsers',
		e.map((v: any) => v.id).concat(userList.map((x: any) => x.value))
	)
}

const onViewFlow = () => {
	if (!currentFlowCode.value) {
		ElMessage.warning('请选择流程')
		return
	}
	showFlow.value = true
}

const onJumpCreateFlow = () => {
	showTurnFlow.value = false
	router.push({
		path: '/flow/step',
		query: {
			category: '业务表流程',
		},
	})
}

const onTurnConfirm = () => {
	if (turnForm.value.fillUsers.length === 0) {
		// ElMessage.warning(
		// 	`请选择填报${turnForm.value.model === 1 ? Tips.Personnel : Tips.Department}`
		// )
		ElMessage.warning(`请选择填报范围`)
		return
	}

	if (!currentRow.value) return

	const staffs: any = {}
	const {reportTaskId, areaOrganizationUnitId} = currentRow.value._raw.businessExtend
	const success = () => {
		// ${
		// 		turnForm.value.model === 1 ? '内部部门' : '下发部门'
		// 	}
		ElMessageBox.confirm(
			`你已将任务转发,可在"临时报表-报表创建-我的转发"查看任务填报情况`,
			'已转发',
			{
				confirmButtonText: '前往查看',
				cancelButtonText: '确认',
				type: 'success',
			}
		)
			.then(() => {
				router.push({
					path: '/statementTask',
					query: {
						from: 'turn',
					},
				})
			})
			.catch(() => {
				onSearch()
			})
	}

	console.log(currentRow.value)
	console.log(turnForm.value.fillUsers)

	debugger
	let params: any = null
	const Id = currentRow.value._raw?.businessExtend.id
	if (Id) {
		params = {
			id: Id,
		}
	} else {
		params = {
			reportTaskId: currentRow.value._raw?.businessExtend.reportTaskId,
			areaOrganizationUnitId: currentRow.value._raw?.businessExtend.areaOrganizationUnitId,
		}
	}
	const data = {
		dataAuditWorkflowSchemeCode: turnForm.value.auditFlow,
		planTaskStaffAreaOrganizationUnits: turnForm.value.fillUsers.map((v: any) => {
			if (v.split('/').length === 1) {
				console.log('bumen')
				return {
					areaOrganizationUnitId: v,
				}
			} else {
				console.log('renyuan')
				return {
					staffId: v.split('/')[1],
					staffAreaOrganizationUnitId: v.split('/')[0],
				}
			}
		}),
	}
	newIssueDataLeader(data, params)
		.then((res) => {
			ElMessage.success('转发成功')
			showTurnFlow.value = false
			success()
		})
		.catch((err) => window.errMsg(err, '转发'))

	// turnForm.value.fillUsers.forEach((v: any) => {
	// 	const split = v.split('/')
	// 	staffs[split[1]] = split[0]
	// })

	// if (turnForm.value.model === 1) {
	// 	// 内部填报
	// 	let hasWorkFlow: any = {}
	// 	if (currentRow.value._raw.businessExtend?.hasTranspond) {
	// 		hasWorkFlow = {
	// 			transpondAuditWorkflowSchemeCode: turnForm.value.turnFlow,
	// 			dataAuditWorkflowSchemeCode: turnForm.value.auditFlow,
	// 		}
	// 	}

	// 	SetInternalStaff({staffs, ...hasWorkFlow}, reportTaskId, areaOrganizationUnitId)
	// 		.then(() => {
	// 			ElMessage.success('转发成功')
	// 			showTurnFlow.value = false
	// 			success()
	// 		})
	// 		.catch((err) => window.errMsg(err, '转发'))
	// } else if (turnForm.value.model === 2) {
	// 	// 下发填报
	// 	IssueDataLeader({
	// 		data: {
	// 			areaOrganizationUnitIds: Object.values(turnForm.value.fillUsers),
	// 			auditDescription: '',
	// 			transpondAuditWorkflowSchemeCode: turnForm.value.turnFlow,
	// 			dataAuditWorkflowSchemeCode: turnForm.value.auditFlow,
	// 		},
	// 		params: {
	// 			reportTaskId,
	// 			areaOrganizationUnitId,
	// 		},
	// 	})
	// 		.then((res: any) => {
	// 			ElMessage.success('转发成功')
	// 			showTurnFlow.value = false
	// 			success()
	// 		})
	// 		.catch((err) => window.errMsg(err, '转发'))
	// }
}

const onPushDesConfirm = () => {
	const {reportTaskId, areaOrganizationUnitId, id} = currentRow.value._raw.businessExtend
	isPushing.value = true
	PushReportFlowTask({...pushDesForm.value}, id)
		.then(() => {
			ElMessage.success('提交成功')
			showPushDes.value = false

			onSearch()
		})
		.catch((err) => window.errMsg(err, '提交'))
		.finally(() => {
			isPushing.value = false
		})
}
onActivated(() => {
	if (route.query.menu) {
		setTimeout(() => {
			onClickTabs(FlowType.Fill)
		}, 1000)
	}

	startDate.value = ''
	endDate.value = ''

	startDate.value = getCurrentDateTime()
	reqParams.endDate = startDate.value

	setSignalrPage()
})

let signalrWorlflow = useSignalr('taskPending', '/signalr-hubs/workflow')

function setSignalrPage() {
	// debugger
	console.log('signalrWorlflow233对象', signalrWorlflow)

	signalrWorlflow?.on('RefreshWfMyUnCompletedTask', (msg: any) => {
		console.log('signalrWorlflow233返回:', msg)
		tableRef.value.reload()
	})
}

function handleformClear() {
	console.log('清空了')
	onSearch()
}

// '1' 全部 '2'本页
const chooseType = ref('')

const allSelectPageData = ref<any>([])
const tableData = ref<any>([])
const pageNow = ref([])

// activeName.value === FlowType.Initiate

watch(chooseType, (val) => {
	// debugger
	console.log('chooseType', chooseType)
	console.log('tableData', tableData)

	if (activeName.value === FlowType.Initiate) {
		// 本页
		// allSelectPageData.value = []
		if (val === '2') {
			// tableData.value.forEach((row: any) => {
			// 	allSelectPageData.value.push(row.id)
			// })
			handleChoose()
		} else if (val === '1') {
			// 全部
			handleChoose()
		}
	}
})

watchEffect(() => {
	// 选上
	// debugger
	if (activeName.value === FlowType.Initiate) {
		if (chooseType.value === '1' && tableData.value?.length) {
			// pageNow为空，初始值时
			if (!pageNow.value.length) {
				handleChoose()
			}
		}
		console.log('watchEffect333-allSelectPageData-tableData', allSelectPageData, tableData)

		// 取消
		if (
			(chooseType.value === '1' || chooseType.value === '2') &&
			allSelectPageData.value.length !== tableData.value.length
		) {
			console.log('进入取消')
			chooseType.value = ''
		}
	}
})

// 点击多选
const handleSelectionChange = (val: any) => {
	// console.log('handleSelectionChange-val', val)

	let ids = val.map((item: any) => item.id)
	pageNow.value = val

	allSelectPageData.value = ids // 点击的时候保存数据

	// allSelectPageData.value = allSelectPageData.value.reduce((accumulator: any, current: any) => {
	// 	const hasObject = accumulator.some((item: any) => item == current)

	// 	if (!hasObject) {
	// 		accumulator.push(current)
	// 	}
	// 	return accumulator
	// }, [])

	console.log('点击多选-allSelectPageData', val, allSelectPageData)
}
// 选择本页
function handleChoose() {
	console.log('handleChoose', new Date())
	allSelectPageData.value = []

	if (activeName.value === FlowType.Initiate) {
		// 先清空表格选择
		tableRef.value.clearSelection()

		tableData.value.forEach((row: any) => {
			tableRef.value.toggleRowSelection(row, false)
		})

		tableData.value.forEach((row: any) => {
			tableRef.value.toggleRowSelection(row, true)
		})
	}
}

function handleCancel() {
	allSelectPageData.value = []
	chooseType.value = ''
	tableData.value.forEach((row: any) => {
		tableRef.value.toggleRowSelection(row, false)
	})
}

const showCheck: any = ref(false)

async function handleCheck() {
	const res = await getAuditingApi()
	console.log('allSelectPageData33', allSelectPageData)
	if (res.data) {
		ElMessage({
			message: '正在录入中，请稍后再试',
			type: 'warning',
		})
	} else {
		showCheck.value = true
	}
}

// * @param code 'agree
//  * @param des '描述
//  * @param name '通过
// 	* @param taskIds '[]'
const formInline: any = ref({
	code: '',
	// name: '',
	des: '',
})

const ruleFormRef = ref<any>()

const rules = reactive<any>({
	code: [{required: true, message: '请选择', trigger: 'blur'}],
	des: [
		{
			required: true,
			message: '请输入',
			trigger: 'blur',
		},
	],
})
watch(
	() => searchForm,
	(val: any) => {
		console.log('searchForm', val)
	},
	{deep: true}
)
const submitForm = (formEl: any) => {
	if (!formEl) return
	formEl.validate(async (valid: any) => {
		if (valid) {
			console.log('submit!', chooseType)

			// '1' 全部 '2'本页
			if (chooseType.value !== '1') {
				const data = {
					...formInline.value,
					taskIds: allSelectPageData.value,
				}
				console.log('data', data)

				auditSomeApi(data)
			} else {
				let queryParam: any = {}

				if (searchForm.value.title) {
					queryParam.title = searchForm.value.title
				}
				if (searchForm.value?.date?.length) {
					queryParam.startDate = searchForm.value?.date[0]
					queryParam.endDate = searchForm.value?.date[1]
				}

				const data = {
					...formInline.value,
					queryParam,
				}

				if (!Object.keys(queryParam).length) {
					delete data.queryParam
				}

				let getTime = ''
				if (reqParams.endDate) {
					getTime = reqParams.endDate
				} else if (endDate.value) {
					getTime = endDate.value
				} else {
					getTime = startDate.value
				}
				data.queryParam = {
					endDate: getTime,
				}
				console.log('data444', data)

				// return
				auditAllApi(data)
			}

			showCheck.value = false
			formInline.value = {}
			// 重新加载表格数据
			tableRef.value.reload()
			// 清空已选择
			handleCancel()

			setSignalrPage()

			reqParams.endDate = getCurrentDateTime()

			ElMessage({
				message: '提交成功',
				type: 'success',
			})
		} else {
			console.log('error submit!')
		}
	})
}

const resetForm = (formEl: any) => {
	if (!formEl) return
	formEl.resetFields()
}

let startDate: any = ref('')
let endDate: any = ref('')

function getCurrentDateTime() {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0')
	const day = String(now.getDate()).padStart(2, '0')
	const hours = String(now.getHours()).padStart(2, '0')
	const minutes = String(now.getMinutes()).padStart(2, '0')
	const seconds = String(now.getSeconds()).padStart(2, '0')

	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

console.log('getCurrentDateTime233', getCurrentDateTime())
</script>
<template>
	<div class="flow">
		<Block
			:enable-fixed-height="true"
			:enable-back-button="false"
			:enable-expand-content="true"
			@height-changed="onBlockHeightChanged"
		>
			<template #title>
				<el-tabs
					v-model="activeName"
					class="tabs-ui"
					@tab-change="onClickTabs"
					:before-leave="() => !loading"
				>
					<el-tab-pane
						:label="FlowType.Initiate + `(${auditCount})`"
						:name="FlowType.Initiate"
					></el-tab-pane>
					<el-tab-pane
						:label="FlowType.Fill + `(${fillCount})`"
						:name="FlowType.Fill"
					></el-tab-pane>
				</el-tabs>
			</template>
			<template #topRight>
				<div class="df aic">
					<!-- {{ allSelectPageData.length }} -->
					<el-button
						type="primary"
						size="small"
						class="mg-left-10"
						v-if="allSelectPageData.length && activeName === FlowType.Initiate"
						@click="handleCheck"
						>批量审核</el-button
					>

					<!-- <el-checkbox
						ml-10px
						v-model="IsShowStateStop"
						label="隐藏终止任务"
						size="small"
						@change="getInfo"
					></el-checkbox> -->
					<el-link
						class="mg-left-10"
						type="primary"
						@click="routeTo"
						style="font-size: 12px"
						>办理记录</el-link
					>
					<el-button
						type="primary"
						size="small"
						class="mg-left-10"
						@click="showTodoRemind = true"
						>待办设置</el-button
					>
					<TodoRemindComponent
						v-model="showTodoRemind"
						@completed="showTodoRemind = false"
					></TodoRemindComponent>
				</div>
			</template>
			<template #expand>
				<div class="search" v-if="activeName === FlowType.Initiate">
					<Form
						v-model="searchForm"
						:props="formItems[activeName]"
						:column-count="activeName === FlowType.Initiate ? 4 : 6"
						:enable-reset="false"
						buttonVertical="flowing"
						label-width="0"
						confirm-text="查询"
						:loading="loading"
						@submit="onSearch"
						@clear="onSearch"
					></Form>
				</div>
				<div class="search" v-else>
					<Form
						v-model="searchForm"
						:props="formItems[activeName]"
						:column-count="activeName === FlowType.Initiate ? 4 : 6"
						:enable-reset="false"
						buttonVertical="flowing"
						label-width="0"
						confirm-text="查询"
						:loading="loading"
						@submit="onSearch"
						@clear="onSearch"
					></Form>
				</div>
			</template>
			<TableV2
				:key="activeName"
				ref="tableRef"
				:url="tableRequestUrl"
				:req-params="reqParams"
				:columns="tableColumns[activeName]"
				:buttons="tableButtons[activeName]"
				:default-sort="defaultSort[activeName]"
				:enable-edit="false"
				:enable-selection="true"
				:enable-latest-data="false"
				:height="tableHeight"
				:enable-toolbar="false"
				:enableDelete="false"
				:form-label-width="100"
				@completed="onTableCompleted"
				@beforeComplete="beforeComplete"
				@click-button="onTableButtonClick"
				@loading="loading = $event"
				@sort-change="onSortChange"
				@selection-change="handleSelectionChange"
			>
				<!-- FlowType.Initiate -->
				<template v-if="activeName === FlowType.Initiate" #title="{row}">
					{{ row.process?.title }}
				</template>
				<template v-if="activeName === FlowType.Initiate" #state="{row}">
					<span
						:style="{color: statusArr.filter((x:any) => x.state === row.state)[0].color}"
					>
						{{ statusArr.filter((x: any) => x.state === row.state)[0].name }}
					</span>
				</template>
				<template v-if="activeName === FlowType.Initiate" #_category="{row}">
					{{ FlowAuditTypeData[row.process?.businessType].name }}
				</template>

				<!-- FlowType.Fill -->
				<template v-if="activeName === FlowType.Fill" #status="scoped">
					<el-tag :type="ReportsFlowStatusType(scoped.row.status, 'fillReport')">
						{{
							ReportsFlowStatus.find(
								(f) => f.value === scoped.row.status && f.type === 'fillReport'
							)?.label || '-'
						}}
					</el-tag>
				</template>
				<template v-if="activeName === FlowType.Fill" #category="scoped">
					{{ scoped.row.category === 1 ? '临时报表填报' : '业务表填报' }}
				</template>
				<template v-if="activeName === FlowType.Fill" #endTime="scoped">
					<div class="df aic">
						{{ scoped.row.endTime || '-' }}
						<el-tag
							v-if="
								new Date(scoped.row.endTime).getTime() >
								new Date(scoped.row.currentServiceTime).getTime()
							"
							class="mg-left-10"
							:type="getTimeLeft(scoped)?.type"
						>
							剩余{{ getTimeLeft(scoped)?.diffDays }}天
						</el-tag>
						<el-tag v-else-if="scoped.row.endTime" type="danger" class="mg-left-10">
							已截止
						</el-tag>
					</div>
				</template>
			</TableV2>

			<div style="display: flex; justify-content: space-between">
				<div
					style="flex: 1; display: flex; align-items: center; margin-top: 15px"
					v-if="activeName === FlowType.Initiate"
				>
					<el-radio-group v-model="chooseType">
						<el-radio value="1">选择全部</el-radio>
						<el-radio value="2">选择本页</el-radio>
					</el-radio-group>

					<el-button
						type="primary"
						size="small"
						style="margin: 0 20px"
						v-if="allSelectPageData.length"
						@click="handleCancel"
						>取消选择</el-button
					>

					<span v-if="allSelectPageData.length"
						>已选
						<!-- '1' 全部 '2'本页 -->
						{{ chooseType === '1' ? pagination.total : allSelectPageData.length }}
						条</span
					>
				</div>

				<div style="flex: 1">
					<Pagination
						:total="pagination.total"
						:page-size="pagination.size"
						:current-page="pagination.page"
						@current-change="onPaginationChange($event, 'page')"
						@size-change="onPaginationChange($event, 'size')"
					></Pagination>
				</div>
			</div>
		</Block>

		<Dialog
			title="提交审核"
			:enableButton="false"
			:width="'40%'"
			v-model="showCheck"
			popconfirm-text="提交"
			@click-close="showCheck = false"
		>
			<el-form
				label-width="80px"
				:model="formInline"
				class="demo-form-inline"
				ref="ruleFormRef"
				:rules="rules"
			>
				<el-form-item label="状态" prop="code">
					<el-radio-group v-model="formInline.code">
						<el-radio value="agree" size="large">通过</el-radio>
						<el-radio value="disagree" size="large">驳回</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="审核意见" prop="des">
					<el-input
						v-model="formInline.des"
						:autosize="{minRows: 2, maxRows: 4}"
						type="textarea"
						placeholder="请输入"
					/>
				</el-form-item>

				<el-form-item style="">
					<div style="margin-left: auto">
						<el-button @click="resetForm(ruleFormRef)">清空</el-button>
						<el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
					</div>
				</el-form-item>
			</el-form>
		</Dialog>

		<Dialog
			v-model="showPushDes"
			title="提交说明"
			width="600"
			confirmText="提交"
			:enable-popconfirm="true"
			:loading="isPushing"
			@click-confirm="onPushDesConfirm"
		>
			<Form
				v-model="pushDesForm"
				:enable-button="false"
				:props="[
					// {
					// 	prop: 'dataAuditWorkflowSchemeCode',
					// 	label: '数据审核流程',
					// 	type: 'selectRemote',
					// 	remoteUrl: '/api/workflow/workflowSchemeInfo',
					// 	remoteParams: {enabledMark: true},
					// 	remoteFilterKey: 'keyword',
					// 	remoteInit: true,
					// 	remoteValue: 'code',
					// 	formShow: useUserStore().getUserInfo?.staffRole.includes(USER_ROLES_ENUM.DATA_LEADER),
					// },
					{
						prop: 'description',
						label: '提交说明',
						type: 'textarea',
					},
				]"
				label-width="100px"
			></Form>
		</Dialog>

		<Dialog
			v-model="showTurnFlow"
			title="任务转发"
			:destroy-on-close="true"
			width="600"
			@click-confirm="onTurnConfirm"
		>
			<!-- ${
								turnForm.model === 1 ? Tips.Personnel : Tips.Department
							} -->
			<Form
				ref="turnFormRef"
				v-model="turnForm"
				:enable-button="false"
				:props="
					turnForm.model === 2
						? [...turnFormProps, ...turnFormPropsOther]
						: currentRow._raw.businessExtend?.hasTranspond
						? [...turnFormProps, ...turnFormPropsOther]
						: turnFormProps
				"
				:notClearColumns="['model']"
				:rules="{
					fillUsers: [
						{
							required: true,
							message: `请选择填报范围`,
							trigger: 'change',
						},
					],
				}"
				label-width="100"
				@change="onTurnFormChange"
			>
				<template #form-fillUsers="scoped">
					<!-- <el-tree-select
						v-if="scoped.form.model === 1"
						ref="treeSelectRef"
						class="no-style"
						v-model="treeSelectValue"
						:data="treeSelectData"
						:load="loadNode"
						:props="{
							label: 'label',
							value: 'value',
							isLeaf: 'isLeaf',
						}"
						:remote-method="filterMethod"
						:loading="loading"
						filterable
						multiple
						remote
						lazy
						@change="onTreeSelectChange"
					>
					</el-tree-select>
					v-if="scoped.form.model === 2" -->
					<departmentFavoriteComp
						:type="'modal'"
						placeholder="请选择"
						@change="departmentListChange"
					></departmentFavoriteComp>
				</template>

				<!-- 下发填报 -->
				<template #form-turnFlow-right>
					<el-button type="primary" class="mg-left-10" @click="onViewFlow"
						>查看流程</el-button
					>
					<!-- <el-button type="primary" @click="onJumpCreateFlow">新建流程</el-button> -->
					<div class="tip" style="padding: 0">
						<i class="icon i-ic-baseline-tips-and-updates"></i>
						<p>说明: 转发不限制必须设置转发审核流程</p>
					</div>
				</template>

				<template #form-auditFlow-right>
					<el-button type="primary" class="mg-left-10" @click="onViewFlow"
						>查看流程</el-button
					>
					<!-- <el-button type="primary" @click="onJumpCreateFlow">新建流程</el-button> -->
				</template>
			</Form>
		</Dialog>

		<ViewFlow v-model="showFlow" :code="currentFlowCode"></ViewFlow>
	</div>
</template>
<route>
	{
		meta: {
			title: '任务待办'
		}
	}
</route>
<style scoped lang="scss">
.tip {
	align-items: center;
	color: var(--z-font-color);
	display: flex;
	padding: 0 10px;
	width: 100%;
	i {
		color: #f00;
	}
	p {
		color: var(--z-main);
		font-size: 12px;
	}
}
</style>
