/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AI: typeof import('./components/AI.vue')['default']
    AICharts: typeof import('./components/AICharts.vue')['default']
    AuthorizedComp: typeof import('./components/common/authorized-comp.vue')['default']
    Bar: typeof import('./components/charts/Bar.vue')['default']
    BasetableComp: typeof import('./components/common/basetable-comp.vue')['default']
    BusinessProcess: typeof import('./components/BusinessProcess.vue')['default']
    DepartmentFavoriteComp: typeof import('./components/common/department-favorite-comp.vue')['default']
    DepartmentSelection: typeof import('./components/DepartmentSelection.vue')['default']
    DialogComp: typeof import('./components/common/dialog-comp.vue')['default']
    DrawerComp: typeof import('./components/common/drawer-comp.vue')['default']
    DrawerPlusComp: typeof import('./components/common/drawer-plus-comp.vue')['default']
    DropdownTree: typeof import('./components/common/dropdownTree.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElLoading: typeof import('element-plus/es')['ElLoading']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElTreeV2: typeof import('element-plus/es')['ElTreeV2']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ExcelComp: typeof import('./components/common/excel-comp.vue')['default']
    ExportComp: typeof import('./components/common/export-comp.vue')['default']
    ExportDataDeclaration: typeof import('./components/common/ExportDataDeclaration.vue')['default']
    FeedbackComp: typeof import('./components/common/feedback-comp.vue')['default']
    FeedbackModal: typeof import('./components/common/feedback-modal.vue')['default']
    FloatingBall: typeof import('./components/common/floatingBall.vue')['default']
    FormComp: typeof import('./components/common/form-comp.vue')['default']
    InfoOverviewComp: typeof import('./components/common/info-overview-comp.vue')['default']
    LabelsComp: typeof import('./components/common/labels-comp.vue')['default']
    LedgerFillConfig: typeof import('./components/ledger-fill-config.vue')['default']
    Line: typeof import('./components/charts/Line.vue')['default']
    LinkedDataComp: typeof import('./components/common/linked-data-comp.vue')['default']
    LoadingComp: typeof import('./components/common/loading-comp.vue')['default']
    MarkdownPreview: typeof import('./components/common/markdown-preview.vue')['default']
    MenuItemComp: typeof import('./components/common/menu-item-comp.vue')['default']
    MultiUserCollaborationDialog: typeof import('./components/MultiUserCollaborationDialog.vue')['default']
    NavigationComp: typeof import('./components/common/navigation-comp.vue')['default']
    NavigationCompPlus: typeof import('./components/common/navigation-comp-plus.vue')['default']
    NoticeList: typeof import('./components/notice-list.vue')['default']
    Organize: typeof import('./components/organize.vue')['default']
    ParticleBackground: typeof import('./components/common/ParticleBackground.vue')['default']
    Pie: typeof import('./components/charts/Pie.vue')['default']
    PopupComp: typeof import('./components/common/popup-comp.vue')['default']
    PreviewDialog: typeof import('./components/common/PreviewDialog.vue')['default']
    PrintableTable: typeof import('./components/common/PrintableTable.vue')['default']
    ProgressBar: typeof import('./components/common/progress-bar.vue')['default']
    ReportFilter: typeof import('./components/report-filter.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScheduleProgressComp: typeof import('./components/common/schedule-progress-comp.vue')['default']
    SelectRegionOrDepartment: typeof import('./components/common/SelectRegionOrDepartment.vue')['default']
    TablecolumnComp: typeof import('./components/common/tablecolumn-comp.vue')['default']
    TimePicker: typeof import('./components/common/TimePicker.vue')['default']
    TopComp: typeof import('./components/common/top-comp.vue')['default']
    Tree_comp: typeof import('./components/common/tree_comp.vue')['default']
    UploadfileComp: typeof import('./components/common/uploadfile-comp.vue')['default']
    ValidateCode: typeof import('./components/ValidateCode.vue')['default']
    VideoPlaye: typeof import('./components/common/VideoPlaye.vue')['default']
    ViewFlow: typeof import('./components/common/ViewFlow.vue')['default']
    XlsxPlusComp: typeof import('./components/common/xlsx-plus-comp.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
